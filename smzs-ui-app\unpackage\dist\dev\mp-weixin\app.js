"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
const utils_request = require("./utils/request.js");
if (!Math) {
  "./pages/home/<USER>";
  "./pages/login/index.js";
  "./pages/challenge/index.js";
  "./pages/stats/index.js";
  "./pages/profile/index.js";
  "./pages/challenge/challenge-detail.js";
  "./pages/challenge/challenge-daily-detail.js";
  "./pages/webview/index.js";
  "./pages/challenge/select-fund.js";
  "./pages/terms/terms-pay.js";
  "./pages/home/<USER>";
  "./pages/profile/wallet.js";
  "./pages/profile/challenge-records.js";
  "./pages/profile/about.js";
  "./pages/profile/settings.js";
  "./pages/profile/wallet-detail.js";
  "./pages/profile/pro-subscription.js";
  "./pages/terms/user.js";
  "./pages/terms/privacy.js";
  "./pages/profile/complete.js";
}
const _sfc_main = {
  onLaunch: function() {
    common_vendor.index.$http = utils_request.request;
  },
  onShow: function() {
    common_vendor.index.__f__("log", "at App.vue:10", "App Show");
  },
  onHide: function() {
    common_vendor.index.__f__("log", "at App.vue:13", "App Hide");
  }
};
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  return {
    app
  };
}
const whiteList = [
  "/pages/login/index",
  "/pages/terms/user",
  "/pages/terms/privacy"
];
function base64Decode(input) {
  const keyStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
  let output = "";
  let chr1, chr2, chr3;
  let enc1, enc2, enc3, enc4;
  let i = 0;
  input = input.replace(/[^A-Za-z0-9\+\/\=]/g, "");
  while (i < input.length) {
    enc1 = keyStr.indexOf(input.charAt(i++));
    enc2 = keyStr.indexOf(input.charAt(i++));
    enc3 = keyStr.indexOf(input.charAt(i++));
    enc4 = keyStr.indexOf(input.charAt(i++));
    chr1 = enc1 << 2 | enc2 >> 4;
    chr2 = (enc2 & 15) << 4 | enc3 >> 2;
    chr3 = (enc3 & 3) << 6 | enc4;
    output = output + String.fromCharCode(chr1);
    if (enc3 !== 64) {
      output = output + String.fromCharCode(chr2);
    }
    if (enc4 !== 64) {
      output = output + String.fromCharCode(chr3);
    }
  }
  return decodeURIComponent(escape(output));
}
function jwtDecode(token) {
  const base64Url = token.replace(/-/g, "+").replace(/_/g, "/");
  const padding = "=".repeat((4 - base64Url.length % 4) % 4);
  try {
    return JSON.parse(base64Decode(base64Url + padding));
  } catch (e) {
    common_vendor.index.__f__("error", "at main.js:76", "JWT解析错误:", e);
    return null;
  }
}
function isAuthenticated() {
  const token = common_vendor.index.getStorageSync("token");
  if (!token)
    return false;
  try {
    const tokenParts = token.split(".");
    if (tokenParts.length !== 3) {
      common_vendor.index.__f__("log", "at main.js:90", "token格式无效");
      common_vendor.index.removeStorageSync("token");
      return false;
    }
    const payload = jwtDecode(tokenParts[1]);
    if (!payload) {
      common_vendor.index.__f__("log", "at main.js:98", "token内容解析失败");
      common_vendor.index.removeStorageSync("token");
      return false;
    }
    if (payload.exp && payload.exp < Date.now() / 1e3) {
      common_vendor.index.__f__("log", "at main.js:104", "token已过期");
      common_vendor.index.removeStorageSync("token");
      return false;
    }
    return true;
  } catch (e) {
    common_vendor.index.__f__("error", "at main.js:111", "token验证失败", e);
    common_vendor.index.removeStorageSync("token");
    return false;
  }
}
common_vendor.index.addInterceptor("navigateTo", {
  invoke(e) {
    return checkLogin(e.url);
  }
});
common_vendor.index.addInterceptor("switchTab", {
  invoke(e) {
    return checkLogin(e.url);
  }
});
common_vendor.index.addInterceptor("reLaunch", {
  invoke(e) {
    if (e.url === "/pages/login/index") {
      return true;
    }
    return checkLogin(e.url);
  }
});
function checkLogin(url) {
  const targetPage = url.split("?")[0];
  if (whiteList.includes(targetPage)) {
    return true;
  }
  if (!isAuthenticated()) {
    common_vendor.index.__f__("log", "at main.js:149", "未登录或token无效");
    return true;
  }
  return true;
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
