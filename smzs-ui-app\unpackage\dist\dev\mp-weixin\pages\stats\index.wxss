
.container.data-v-1fa681a1 {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}
.bg-image.data-v-1fa681a1 {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 0;
}
.energy-cards.data-v-1fa681a1 {
  position: relative;
  z-index: 2;
  padding: 180rpx 32rpx 0;
  display: flex;
  justify-content: space-between;
  gap: 180rpx;
}
.energy-card.data-v-1fa681a1 {
  flex: 1;
  background: linear-gradient(to bottom, #FFD700, #ffec80);
  /* 加点透明 */
  opacity: 0.8;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border-radius: 40rpx;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}
.energy-value.data-v-1fa681a1 {
  font-size: 48rpx;
  color: #2a6e3f;
  font-weight: bold;
  font-family: DIN;
}
.level-progress.data-v-1fa681a1 {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.progress-bar.data-v-1fa681a1 {
  flex: 1;
  height: 12rpx;
  background: #7981041a;
  border-radius: 4rpx;
  overflow: hidden;
}
.progress-fill.data-v-1fa681a1 {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
  transition: width 0.3s ease;
}
.level-text.data-v-1fa681a1 {
  font-size: 24rpx;
  color: #2a6e3f;
  font-weight: bold;
}
.energy-label.data-v-1fa681a1 {
  font-size: 24rpx;
  color: #666;
}
.energy-balls.data-v-1fa681a1 {
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600rpx;
  height: 600rpx;
  pointer-events: none;
  z-index: 1;
}
.energy-ball.data-v-1fa681a1 {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  pointer-events: auto;
  transform-origin: center;
  animation: float-1fa681a1 4s ease-in-out infinite alternate;
}
.ball-inner.data-v-1fa681a1 {
  width: 100rpx;
  height: 100rpx;
  background: radial-gradient(circle at 30% 30%, 
    rgba(164, 230, 170, 0.9) 0%, 
    rgba(126, 209, 131, 0.85) 50%, 
    rgba(98, 188, 103, 0.8) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(255, 255, 255, 0.6),
              inset 0 -4rpx 12rpx rgba(0, 0, 0, 0.1),
              inset 2rpx 2rpx 8rpx rgba(255, 255, 255, 0.6);
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
  position: relative;
  overflow: hidden;
  border: 2rpx solid rgba(255, 255, 255, 0.6);
}
.ball-inner.data-v-1fa681a1::after {
  content: '';
  position: absolute;
  top: 15%;
  left: 20%;
  width: 40%;
  height: 40%;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  filter: blur(3rpx);
}
.ball-value.data-v-1fa681a1 {
  font-size: 32rpx;
  color: #FFFFFF;
  font-weight: 600;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  font-family: "Arial Rounded MT Bold", "Helvetica Rounded", Arial, sans-serif;
}
.ball-name.data-v-1fa681a1 {
  font-size: 21rpx;
  color: #2a6e3f;
  text-shadow: 0 2rpx 4rpx rgba(255, 255, 255, 0.9);
  /* // 透明 */
  opacity: 0.8;
  /* background: rgba(255, 255, 255, 0.6);
  padding: 4rpx 16rpx;
  border-radius: 20rpx; */
  /* font-weight: 500;
  letter-spacing: 1rpx; */
}
.energy-ball.collecting.data-v-1fa681a1 {
  animation: collect-1fa681a1 0.5s ease-out forwards;
  pointer-events: none;
}
.collect-success.data-v-1fa681a1 {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 999;
  pointer-events: none;
}
.success-text.data-v-1fa681a1 {
  font-size: 64rpx;
  color: #FFD700;
  font-weight: bold;
  text-shadow: 0 4rpx 8rpx rgba(0,0,0,0.3);
  animation: successPop-1fa681a1 1s ease-out forwards;
}
@keyframes float-1fa681a1 {
0% {
    transform: translateY(0) rotate(0deg);
}
50% {
    transform: translateY(-20rpx) rotate(2deg);
}
100% {
    transform: translateY(0) rotate(-2deg);
}
}
@keyframes collect-1fa681a1 {
0% {
    transform: scale(1) translateY(0);
    opacity: 1;
}
50% {
    transform: scale(1.3) translateY(-30rpx);
    opacity: 0.8;
}
100% {
    transform: scale(0) translateY(-50rpx);
    opacity: 0;
}
}
@keyframes successPop-1fa681a1 {
0% {
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 0;
}
50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 1;
}
100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0;
}
}
