/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文章场景相关 */
/* 现代化页面容器 */
.page-container.modern-design.data-v-0225c721 {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
  position: relative;
  overflow: hidden;
}
.page-container.modern-design.data-v-0225c721::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
  z-index: 0;
}
.page-container.modern-design.data-v-0225c721::after {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.05);
  -webkit-backdrop-filter: blur(1px);
          backdrop-filter: blur(1px);
  z-index: 0;
}

/* 现代化头部 */
.modern-header.data-v-0225c721 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  z-index: 100;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
.modern-header.scrolled.data-v-0225c721 {
  height: 100rpx;
}
.modern-header.scrolled .header-content.data-v-0225c721 {
  padding: 0 32rpx;
}
.modern-header .header-bg.data-v-0225c721 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}
.modern-header .header-bg .bg-gradient.data-v-0225c721 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(138, 43, 226, 0.9) 0%, rgba(30, 144, 255, 0.9) 50%, rgba(255, 20, 147, 0.9) 100%);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
}
.modern-header .header-bg .bg-particles.data-v-0225c721 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.modern-header .header-bg .bg-particles .particle.data-v-0225c721 {
  position: absolute;
  width: 8rpx;
  height: 8rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: float 3s ease-in-out infinite;
}
.modern-header .header-content.data-v-0225c721 {
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 40rpx;
  padding-top: var(--status-bar-height, 20px);
}
.modern-header .header-content .back-button.data-v-0225c721 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background: rgba(255, 255, 255, 0.2);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.modern-header .header-content .back-button.data-v-0225c721:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 0.3);
}
.modern-header .header-content .back-button .back-icon.data-v-0225c721 {
  font-size: 36rpx;
  color: #fff;
  font-weight: bold;
}
.modern-header .header-content .header-title-area.data-v-0225c721 {
  flex: 1;
  text-align: center;
  position: relative;
}
.modern-header .header-content .header-title-area .header-title.data-v-0225c721 {
  font-size: 36rpx;
  font-weight: 700;
  color: #fff;
  opacity: 0;
  transform: translateY(10rpx);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
.modern-header .header-content .header-title-area .header-title.show.data-v-0225c721 {
  opacity: 1;
  transform: translateY(0);
}
.modern-header .header-content .header-title-area .title-decoration.data-v-0225c721 {
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
}
.modern-header .header-content .header-title-area .title-decoration .decoration-line.data-v-0225c721 {
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, #fff, transparent);
  border-radius: 2rpx;
  opacity: 0;
  animation: slideIn 0.5s ease 0.3s forwards;
}
.modern-header .header-content .header-actions.data-v-0225c721 {
  width: 80rpx;
  display: flex;
  justify-content: flex-end;
}
.modern-header .header-content .header-actions .action-btn.data-v-0225c721 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background: rgba(255, 255, 255, 0.2);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.modern-header .header-content .header-actions .action-btn.data-v-0225c721:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 0.3);
}
.modern-header .header-content .header-actions .action-btn .iconfont.data-v-0225c721 {
  font-size: 32rpx;
}

/* 现代化滚动内容 */
.scroll-content.modern-scroll.data-v-0225c721 {
  height: 100vh;
  box-sizing: border-box;
  padding: 32rpx;
  position: relative;
  z-index: 1;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* 现代化今日打卡卡片 */
.today-card.modern-card.data-v-0225c721 {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(30rpx);
  -webkit-backdrop-filter: blur(30rpx);
  border-radius: 40rpx;
  padding: 48rpx;
  margin-bottom: 40rpx;
  position: relative;
  overflow: hidden;
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1), 0 8rpx 32rpx rgba(138, 43, 226, 0.15), inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* 今日头部区域 */
  /* 主要内容区域 */
}
.today-card.modern-card.data-v-0225c721:hover {
  transform: translateY(-8rpx);
  box-shadow: 0 32rpx 80rpx rgba(0, 0, 0, 0.15), 0 16rpx 48rpx rgba(138, 43, 226, 0.2), inset 0 1rpx 0 rgba(255, 255, 255, 0.4);
}
.today-card.modern-card.completed.data-v-0225c721 {
  background: rgba(76, 175, 80, 0.15);
  border-color: rgba(76, 175, 80, 0.3);
}
.today-card.modern-card.completed .card-bg-effects .gradient-orb.data-v-0225c721 {
  background: radial-gradient(circle, rgba(76, 175, 80, 0.3) 0%, transparent 70%);
}
.today-card.modern-card .card-bg-effects.data-v-0225c721 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
}
.today-card.modern-card .card-bg-effects .gradient-orb.data-v-0225c721 {
  position: absolute;
  border-radius: 50%;
  filter: blur(40rpx);
  animation: float 6s ease-in-out infinite;
}
.today-card.modern-card .card-bg-effects .gradient-orb.orb-1.data-v-0225c721 {
  width: 200rpx;
  height: 200rpx;
  top: -50rpx;
  right: -50rpx;
  background: radial-gradient(circle, rgba(255, 20, 147, 0.3) 0%, transparent 70%);
  animation-delay: 0s;
}
.today-card.modern-card .card-bg-effects .gradient-orb.orb-2.data-v-0225c721 {
  width: 150rpx;
  height: 150rpx;
  bottom: -30rpx;
  left: -30rpx;
  background: radial-gradient(circle, rgba(30, 144, 255, 0.3) 0%, transparent 70%);
  animation-delay: 3s;
}
.today-card.modern-card .card-bg-effects .floating-shapes.data-v-0225c721 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.today-card.modern-card .card-bg-effects .floating-shapes .shape.data-v-0225c721 {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  animation: floatShape 8s ease-in-out infinite;
}
.today-card.modern-card .card-bg-effects .floating-shapes .shape.shape-1.data-v-0225c721 {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}
.today-card.modern-card .card-bg-effects .floating-shapes .shape.shape-2.data-v-0225c721 {
  width: 16rpx;
  height: 16rpx;
  border-radius: 4rpx;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}
.today-card.modern-card .card-bg-effects .floating-shapes .shape.shape-3.data-v-0225c721 {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  bottom: 30%;
  left: 20%;
  animation-delay: 4s;
}
.today-card.modern-card .card-bg-effects .floating-shapes .shape.shape-4.data-v-0225c721 {
  width: 18rpx;
  height: 18rpx;
  border-radius: 6rpx;
  top: 40%;
  right: 30%;
  animation-delay: 6s;
}
.today-card.modern-card .today-header.data-v-0225c721 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  position: relative;
  z-index: 1;
}
.today-card.modern-card .today-header .today-badge.data-v-0225c721 {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50rpx;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.today-card.modern-card .today-header .today-badge .badge-emoji.data-v-0225c721 {
  font-size: 32rpx;
  margin-right: 12rpx;
}
.today-card.modern-card .today-header .today-badge .badge-text.data-v-0225c721 {
  font-size: 28rpx;
  font-weight: 600;
  color: #fff;
}
.today-card.modern-card .today-header .today-date-modern.data-v-0225c721 {
  display: flex;
  align-items: center;
}
.today-card.modern-card .today-header .today-date-modern .date-text.data-v-0225c721 {
  font-size: 32rpx;
  font-weight: 700;
  color: #fff;
  margin-right: 16rpx;
}
.today-card.modern-card .today-header .today-date-modern .date-indicator.data-v-0225c721 {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  animation: pulse-0225c721 2s ease-in-out infinite;
}
.today-card.modern-card .today-header .today-date-modern .date-indicator.status-success.data-v-0225c721 {
  background: #4CAF50;
}
.today-card.modern-card .today-header .today-date-modern .date-indicator.status-warning.data-v-0225c721 {
  background: #FF9800;
}
.today-card.modern-card .today-header .today-date-modern .date-indicator.status-failed.data-v-0225c721 {
  background: #F44336;
}
.today-card.modern-card .today-header .today-date-modern .date-indicator.status-pending.data-v-0225c721 {
  background: #2196F3;
}
.today-card.modern-card .today-main-content.data-v-0225c721 {
  position: relative;
  z-index: 1;
}
.today-card.modern-card .today-main-content .status-display.data-v-0225c721 {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 24rpx;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.today-card.modern-card .today-main-content .status-display .status-icon.data-v-0225c721 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  background: rgba(255, 255, 255, 0.2);
}
.today-card.modern-card .today-main-content .status-display .status-icon.status-success.data-v-0225c721 {
  background: linear-gradient(135deg, #4CAF50, #66BB6A);
}
.today-card.modern-card .today-main-content .status-display .status-icon.status-warning.data-v-0225c721 {
  background: linear-gradient(135deg, #FF9800, #FFB74D);
}
.today-card.modern-card .today-main-content .status-display .status-icon.status-failed.data-v-0225c721 {
  background: linear-gradient(135deg, #F44336, #EF5350);
}
.today-card.modern-card .today-main-content .status-display .status-icon.status-pending.data-v-0225c721 {
  background: linear-gradient(135deg, #2196F3, #42A5F5);
}
.today-card.modern-card .today-main-content .status-display .status-icon .status-emoji.data-v-0225c721 {
  font-size: 40rpx;
}
.today-card.modern-card .today-main-content .status-display .status-info.data-v-0225c721 {
  flex: 1;
}
.today-card.modern-card .today-main-content .status-display .status-info .status-title.data-v-0225c721 {
  font-size: 32rpx;
  font-weight: 700;
  color: #fff;
  margin-bottom: 8rpx;
  display: block;
}
.today-card.modern-card .today-main-content .status-display .status-info .status-subtitle.data-v-0225c721 {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  display: block;
}
.today-card.modern-card .today-main-content .time-info-modern.data-v-0225c721 {
  margin-bottom: 24rpx;
  padding: 20rpx 24rpx;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
}
.today-card.modern-card .today-main-content .time-info-modern .time-label.data-v-0225c721 {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}
.today-card.modern-card .today-main-content .time-info-modern .time-label .time-icon.data-v-0225c721 {
  font-size: 24rpx;
  margin-right: 8rpx;
}
.today-card.modern-card .today-main-content .time-info-modern .time-label .time-text.data-v-0225c721 {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}
.today-card.modern-card .today-main-content .time-info-modern .time-range.data-v-0225c721 {
  font-size: 28rpx;
  font-weight: 600;
  color: #fff;
}
.today-card.modern-card .today-main-content .checkin-result-modern .result-item.data-v-0225c721 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 24rpx;
  margin-bottom: 12rpx;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 16rpx;
}
.today-card.modern-card .today-main-content .checkin-result-modern .result-item .result-label.data-v-0225c721 {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}
.today-card.modern-card .today-main-content .checkin-result-modern .result-item .result-value.data-v-0225c721 {
  font-size: 26rpx;
  font-weight: 600;
  color: #fff;
}
.today-card.modern-card .today-main-content .checkin-result-modern .result-item.refund-info .refund-status.data-v-0225c721 {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}
.today-card.modern-card .today-main-content .checkin-result-modern .result-item.refund-info .refund-status.refund-pending.data-v-0225c721 {
  background: rgba(255, 152, 0, 0.2);
  color: #FF9800;
}
.today-card.modern-card .today-main-content .checkin-result-modern .result-item.refund-info .refund-status.refund-success.data-v-0225c721 {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
}
.today-card.modern-card .today-main-content .checkin-result-modern .result-item.refund-info .refund-status.refund-failed.data-v-0225c721 {
  background: rgba(244, 67, 54, 0.2);
  color: #F44336;
}
.today-card.modern-card .today-main-content .checkin-result-modern .result-item.refund-info .refund-status.refund-processing.data-v-0225c721 {
  background: rgba(33, 150, 243, 0.2);
  color: #2196F3;
}
.today-card.modern-card .time-range-info.data-v-0225c721 {
  margin-bottom: 20rpx;
  position: relative;
  z-index: 1;
}
.today-card.modern-card .time-range-info .time-range-text.data-v-0225c721 {
  font-size: 26rpx;
  color: #666;
}
.today-card.modern-card .checkin-result-info.data-v-0225c721 {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: #F9F9F9;
  border-radius: 16rpx;
  font-size: 26rpx;
}
.today-card.modern-card .checkin-result-info .checkin-time.data-v-0225c721 {
  margin-bottom: 10rpx;
  color: #333;
}
.today-card.modern-card .checkin-result-info .checkin-status.data-v-0225c721 {
  display: inline-block;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  margin-bottom: 10rpx;
  font-size: 24rpx;
}
.today-card.modern-card .checkin-result-info .checkin-status.valid.data-v-0225c721 {
  background: rgba(39, 174, 96, 0.1);
  color: #27AE60;
}
.today-card.modern-card .checkin-result-info .checkin-status.invalid.data-v-0225c721 {
  background: rgba(235, 87, 87, 0.1);
  color: #EB5757;
}
.today-card.modern-card .checkin-result-info .energy-info.data-v-0225c721 {
  display: flex;
  justify-content: space-between;
  color: #666;
}
.today-card.modern-card .checkin-result-info .energy-info .refund-pending.data-v-0225c721 {
  color: #FF9F1C;
}
.today-card.modern-card .checkin-result-info .energy-info .refund-done.data-v-0225c721 {
  color: #27AE60;
}
.today-card.modern-card .checkin-result-info .energy-info .refund-failed.data-v-0225c721 {
  color: #EB5757;
}
.today-card.modern-card .checkin-result-info .energy-info .refund-processing.data-v-0225c721 {
  color: #2F80ED;
}
.today-card.modern-card .checkin-result-info .energy-info .refund-none.data-v-0225c721 {
  color: #999;
}
.today-card.modern-card .today-action.data-v-0225c721 {
  position: relative;
  z-index: 1;
}
.today-card.modern-card .today-action .checkin-btn.data-v-0225c721 {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #FFC72C, #FFB300);
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #fff;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(255, 199, 44, 0.15);
  transition: all 0.3s ease;
}
.today-card.modern-card .today-action .checkin-btn.data-v-0225c721:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(255, 199, 44, 0.1);
}
.today-card.modern-card .today-action .checkin-btn .checkin-icon.data-v-0225c721 {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
}
.today-card.modern-card .today-action .checkin-btn.data-v-0225c721::after {
  border: none !important;
}
.daily-task-card.data-v-0225c721 {
  background: #fff;
  border-radius: 32rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 199, 44, 0.15);
  overflow: hidden;
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  /* 添加微妙的动画效果 */
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* 任务头部样式 */
  /* 任务描述区域 */
  /* 增强的进度指示器样式 */
}
.daily-task-card.data-v-0225c721:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 40rpx rgba(255, 199, 44, 0.2);
}
.daily-task-card .task-header.data-v-0225c721 {
  position: relative;
  padding: 40rpx;
  background: linear-gradient(135deg, #FFC72C 0%, #FFB300 100%);
  overflow: hidden;
}
.daily-task-card .task-header .task-header-bg.data-v-0225c721 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.daily-task-card .task-header .task-header-bg .header-wave.data-v-0225c721 {
  position: absolute;
  width: 200%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
}
.daily-task-card .task-header .task-header-bg .header-wave.wave1.data-v-0225c721 {
  top: -50%;
  left: -50%;
  border-radius: 45%;
  animation: wave1-0225c721 6s ease-in-out infinite;
}
.daily-task-card .task-header .task-header-bg .header-wave.wave2.data-v-0225c721 {
  top: -30%;
  right: -50%;
  border-radius: 40%;
  animation: wave2-0225c721 8s ease-in-out infinite reverse;
}
.daily-task-card .task-header .task-header-content.data-v-0225c721 {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
}
.daily-task-card .task-header .task-header-content .day-circle.data-v-0225c721 {
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}
.daily-task-card .task-header .task-header-content .day-circle .day-number.data-v-0225c721 {
  font-size: 48rpx;
  font-weight: 700;
  color: #fff;
  line-height: 1;
}
.daily-task-card .task-header .task-header-content .day-circle .day-text.data-v-0225c721 {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  margin-top: 4rpx;
}
.daily-task-card .task-header .task-header-content .task-header-info.data-v-0225c721 {
  flex: 1;
}
.daily-task-card .task-header .task-header-content .task-header-info .task-category.data-v-0225c721 {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8rpx;
  display: block;
}
.daily-task-card .task-header .task-header-content .task-header-info .task-main-title.data-v-0225c721 {
  font-size: 36rpx;
  font-weight: 700;
  color: #fff;
  margin-bottom: 12rpx;
  display: block;
  line-height: 1.3;
}
.daily-task-card .task-header .task-header-content .task-header-info .task-difficulty-indicator.data-v-0225c721 {
  display: flex;
  align-items: center;
}
.daily-task-card .task-header .task-header-content .task-header-info .task-difficulty-indicator .difficulty-star.data-v-0225c721 {
  font-size: 20rpx;
  margin-right: 4rpx;
  opacity: 0.4;
  transition: opacity 0.3s ease;
}
.daily-task-card .task-header .task-header-content .task-header-info .task-difficulty-indicator .difficulty-star.active.data-v-0225c721 {
  opacity: 1;
}
.daily-task-card .task-header .task-header-content .task-header-info .task-difficulty-indicator .difficulty-text.data-v-0225c721 {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-left: 8rpx;
  font-weight: 500;
}
.daily-task-card .task-description-section.data-v-0225c721 {
  padding: 32rpx 40rpx;
  display: flex;
  align-items: flex-start;
}
.daily-task-card .task-description-section .description-icon.data-v-0225c721 {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
  margin-top: 4rpx;
}
.daily-task-card .task-description-section .description-icon .icon.data-v-0225c721 {
  width: 100%;
  height: 100%;
}
.daily-task-card .task-description-section .description-content.data-v-0225c721 {
  flex: 1;
}
.daily-task-card .task-description-section .description-content .description-title.data-v-0225c721 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}
.daily-task-card .task-description-section .description-content .description-text.data-v-0225c721 {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  display: block;
}
.daily-task-card .task-section.data-v-0225c721 {
  margin-bottom: 20rpx;
}
.daily-task-card .task-section.enhanced.data-v-0225c721 {
  padding: 32rpx 40rpx;
  margin-bottom: 24rpx;
  border-radius: 24rpx;
  background: linear-gradient(135deg, rgba(255, 199, 44, 0.02) 0%, rgba(255, 179, 0, 0.05) 100%);
  border: 1rpx solid rgba(255, 199, 44, 0.1);
  transition: all 0.3s ease;
}
.daily-task-card .task-section.enhanced.data-v-0225c721:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(255, 199, 44, 0.1);
}
.daily-task-card .task-section .section-header.data-v-0225c721 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}
.daily-task-card .task-section .section-header .section-icon-wrapper.data-v-0225c721 {
  display: flex;
  align-items: center;
  padding: 12rpx;
  border-radius: 16rpx;
  background: rgba(255, 199, 44, 0.1);
  margin-right: 16rpx;
}
.daily-task-card .task-section .section-header .section-icon-wrapper.tips.data-v-0225c721 {
  background: rgba(52, 152, 219, 0.1);
}
.daily-task-card .task-section .section-header .section-icon-wrapper.resources.data-v-0225c721 {
  background: rgba(155, 89, 182, 0.1);
}
.daily-task-card .task-section .section-header .section-icon.data-v-0225c721 {
  width: 32rpx;
  height: 32rpx;
}
.daily-task-card .task-section .section-header .section-title.data-v-0225c721 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}
.daily-task-card .task-section .section-header .section-badge.data-v-0225c721 {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: 500;
  background: rgba(255, 199, 44, 0.2);
  color: #FFC72C;
}
.daily-task-card .task-section .section-header .section-badge.tips.data-v-0225c721 {
  background: rgba(52, 152, 219, 0.2);
  color: #3498DB;
}
.daily-task-card .task-section .section-header .section-badge.resources.data-v-0225c721 {
  background: rgba(155, 89, 182, 0.2);
  color: #9B59B6;
}
.daily-task-card .task-section .section-content.data-v-0225c721 {
  font-size: 26rpx;
  color: #333;
  line-height: 1.6;
}
.daily-task-card .task-section .section-content.tips.data-v-0225c721 {
  background: rgba(52, 152, 219, 0.05);
  padding: 24rpx;
  border-radius: 16rpx;
  border-left: 4rpx solid #3498DB;
}
.daily-task-card .task-section .section-content.resources.data-v-0225c721 {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.daily-task-card .task-completion-status.data-v-0225c721 {
  display: flex;
  align-items: center;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  background: rgba(39, 174, 96, 0.1);
  color: #27AE60;
  margin-top: 20rpx;
}
.daily-task-card .task-completion-status .status-icon.data-v-0225c721 {
  width: 48rpx;
  height: 48rpx;
  margin-right: 12rpx;
}
.daily-task-card .task-completion-status text.data-v-0225c721 {
  font-size: 26rpx;
  font-weight: 500;
}
.daily-task-card .task-progress-section.enhanced.data-v-0225c721 {
  padding: 32rpx 40rpx;
  background: linear-gradient(135deg, rgba(255, 199, 44, 0.02) 0%, rgba(255, 179, 0, 0.05) 100%);
  border-radius: 24rpx;
  border: 1rpx solid rgba(255, 199, 44, 0.1);
}
.daily-task-card .task-progress-section.enhanced .progress-header.data-v-0225c721 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}
.daily-task-card .task-progress-section.enhanced .progress-header .progress-title-wrapper.data-v-0225c721 {
  display: flex;
  align-items: center;
}
.daily-task-card .task-progress-section.enhanced .progress-header .progress-title-wrapper .progress-icon.data-v-0225c721 {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}
.daily-task-card .task-progress-section.enhanced .progress-header .progress-title-wrapper .progress-title.data-v-0225c721 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}
.daily-task-card .task-progress-section.enhanced .progress-header .progress-value-wrapper.data-v-0225c721 {
  display: flex;
  align-items: baseline;
}
.daily-task-card .task-progress-section.enhanced .progress-header .progress-value-wrapper .progress-value.data-v-0225c721 {
  font-size: 36rpx;
  font-weight: 700;
  color: #FFC72C;
}
.daily-task-card .task-progress-section.enhanced .progress-header .progress-value-wrapper .progress-separator.data-v-0225c721 {
  font-size: 24rpx;
  color: #999;
  margin: 0 8rpx;
}
.daily-task-card .task-progress-section.enhanced .progress-header .progress-value-wrapper .progress-total.data-v-0225c721 {
  font-size: 28rpx;
  color: #666;
}
.daily-task-card .task-progress-section.enhanced .progress-track.enhanced.data-v-0225c721 {
  height: 16rpx;
  background: rgba(255, 199, 44, 0.1);
  border-radius: 8rpx;
  position: relative;
  overflow: hidden;
}
.daily-task-card .task-progress-section.enhanced .progress-track.enhanced .progress-fill.enhanced.data-v-0225c721 {
  height: 100%;
  background: linear-gradient(90deg, #FFC72C 0%, #FFB300 50%, #FFA000 100%);
  border-radius: 8rpx;
  position: relative;
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}
.daily-task-card .task-progress-section.enhanced .progress-track.enhanced .progress-fill.enhanced .progress-shine.data-v-0225c721 {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);
  animation: shine-0225c721 2s infinite;
}
.daily-task-card .task-progress-section.enhanced .progress-track.enhanced .progress-marker.enhanced.data-v-0225c721 {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  transition: left 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}
.daily-task-card .task-progress-section.enhanced .progress-track.enhanced .progress-marker.enhanced .marker-dot.enhanced.data-v-0225c721 {
  width: 24rpx;
  height: 24rpx;
  background: #FFC72C;
  border-radius: 50%;
  border: 4rpx solid #fff;
  box-shadow: 0 4rpx 12rpx rgba(255, 199, 44, 0.3);
  position: relative;
}
.daily-task-card .task-progress-section.enhanced .progress-track.enhanced .progress-marker.enhanced .marker-dot.enhanced .marker-pulse.data-v-0225c721 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  background: rgba(255, 199, 44, 0.3);
  border-radius: 50%;
  animation: pulse-0225c721 2s infinite;
}
.daily-task-card .task-progress-section.enhanced .progress-milestones.enhanced.data-v-0225c721 {
  display: flex;
  justify-content: space-between;
  margin-top: 16rpx;
  position: relative;
}
.daily-task-card .task-progress-section.enhanced .progress-milestones.enhanced .milestone.enhanced.data-v-0225c721 {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}
.daily-task-card .task-progress-section.enhanced .progress-milestones.enhanced .milestone.enhanced.completed .milestone-dot.enhanced.data-v-0225c721 {
  background: #FFC72C;
  border-color: #FFB300;
}
.daily-task-card .task-progress-section.enhanced .progress-milestones.enhanced .milestone.enhanced.completed .milestone-text.enhanced.data-v-0225c721 {
  color: #FFC72C;
  font-weight: 600;
}
.daily-task-card .task-progress-section.enhanced .progress-milestones.enhanced .milestone.enhanced .milestone-dot.enhanced.data-v-0225c721 {
  width: 16rpx;
  height: 16rpx;
  background: #E0E0E0;
  border: 2rpx solid #BDBDBD;
  border-radius: 50%;
  margin-bottom: 8rpx;
  transition: all 0.3s ease;
}
.daily-task-card .task-progress-section.enhanced .progress-milestones.enhanced .milestone.enhanced .milestone-text.enhanced.data-v-0225c721 {
  font-size: 20rpx;
  color: #999;
  transition: all 0.3s ease;
}
.challenge-card.data-v-0225c721 {
  background: #fff;
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 199, 44, 0.08);
}
.challenge-card .challenge-header.data-v-0225c721 {
  margin-bottom: 40rpx;
}
.challenge-card .challenge-header .challenge-title.data-v-0225c721 {
  font-size: 36rpx;
  font-weight: 600;
  background: linear-gradient(135deg, #FFC72C, #FFB300);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 12rpx;
  display: block;
}
.challenge-card .challenge-header .challenge-period.data-v-0225c721 {
  font-size: 26rpx;
  color: #666;
}
.challenge-card .progress-bar .progress-info.data-v-0225c721 {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}
.challenge-card .progress-bar .progress-info .progress-label.data-v-0225c721 {
  font-size: 26rpx;
  color: #666;
}
.challenge-card .progress-bar .progress-info .progress-value.data-v-0225c721 {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}
.challenge-card .progress-bar .progress-track.data-v-0225c721 {
  height: 20rpx;
  background: #FFF8F0;
  border-radius: 10rpx;
  overflow: hidden;
}
.challenge-card .progress-bar .progress-track .progress-fill.data-v-0225c721 {
  height: 100%;
  background: linear-gradient(90deg, #FFC72C, #FFB300);
  border-radius: 10rpx;
  transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}
.stats-section.data-v-0225c721 {
  margin-bottom: 40rpx;
}
.stats-section .section-title.data-v-0225c721 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  display: block;
}
.stats-section .stats-grid.data-v-0225c721 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}
.stats-section .stats-item.data-v-0225c721 {
  background: #fff;
  border-radius: 32rpx;
  padding: 40rpx 30rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(255, 199, 44, 0.08);
  transition: all 0.3s ease;
}
.stats-section .stats-item.data-v-0225c721:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 36rpx rgba(255, 199, 44, 0.12);
}
.stats-section .stats-item .stats-icon.data-v-0225c721 {
  width: 96rpx;
  height: 96rpx;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24rpx;
  background: #FFF8F0;
}
.stats-section .stats-item .stats-icon image.data-v-0225c721 {
  width: 48rpx;
  height: 48rpx;
}
.stats-section .stats-item .stats-value.data-v-0225c721 {
  font-size: 44rpx;
  font-weight: 600;
  background: linear-gradient(135deg, #FFC72C, #FFB300);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 12rpx;
  display: block;
}
.stats-section .stats-item .stats-label.data-v-0225c721 {
  font-size: 26rpx;
  color: #666;
}
.calendar-section.data-v-0225c721 {
  margin-bottom: 40rpx;
}
.calendar-section .section-title.data-v-0225c721 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  display: block;
}
.calendar-section .calendar-card.data-v-0225c721 {
  background: #fff;
  border-radius: 32rpx;
  padding: 30rpx 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 199, 44, 0.08);
}
.calendar-section .calendar-card .weekdays.data-v-0225c721 {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  margin-bottom: 20rpx;
  text-align: center;
}
.calendar-section .calendar-card .weekdays text.data-v-0225c721 {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  font-weight: 500;
}
.calendar-section .calendar-card .days-grid.data-v-0225c721 {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8rpx;
  padding: 0;
}
.calendar-section .calendar-card .days-grid .day-item.data-v-0225c721 {
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  border-radius: 12rpx;
  min-height: 76rpx;
  transition: all 0.3s ease;
  background: #FFF8F0;
  border: none;
}
.calendar-section .calendar-card .days-grid .day-item.placeholder.data-v-0225c721 {
  background: transparent;
  box-shadow: none;
  pointer-events: none;
}
.calendar-section .calendar-card .days-grid .day-item.checked.data-v-0225c721 {
  background: linear-gradient(135deg, #FFC72C, #FFB300);
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 16rpx rgba(255, 199, 44, 0.2);
}
.calendar-section .calendar-card .days-grid .day-item.checked .day-text.data-v-0225c721, .calendar-section .calendar-card .days-grid .day-item.checked .month-text.data-v-0225c721 {
  color: #fff;
}
.calendar-section .calendar-card .days-grid .day-item.checked .month-text.data-v-0225c721 {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  background: rgba(255, 255, 255, 0.9);
  color: #FFC72C;
  font-size: 16rpx;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  margin: 0;
}
.calendar-section .calendar-card .days-grid .day-item.checked .check-mark.data-v-0225c721 {
  position: absolute;
  bottom: 8rpx;
  width: 28rpx;
  height: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.calendar-section .calendar-card .days-grid .day-item.checked .check-mark image.data-v-0225c721 {
  width: 24rpx;
  height: 24rpx;
}
.calendar-section .calendar-card .days-grid .day-item.invalid-time.data-v-0225c721 {
  background: linear-gradient(135deg, #FF9F1C, #F76E11);
}
.calendar-section .calendar-card .days-grid .day-item.invalid-time.data-v-0225c721::after {
  content: "非效";
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  background: rgba(255, 255, 255, 0.9);
  color: #F76E11;
  font-size: 16rpx;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
}
.calendar-section .calendar-card .days-grid .day-item.failed.data-v-0225c721 {
  background: #FFF5F5;
}
.calendar-section .calendar-card .days-grid .day-item.failed .day-text.data-v-0225c721 {
  color: #FF5252;
}
.calendar-section .calendar-card .days-grid .day-item.failed .month-text.data-v-0225c721 {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  background: rgba(255, 82, 82, 0.1);
  color: #FF5252;
  font-size: 16rpx;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  margin: 0;
}
.calendar-section .calendar-card .days-grid .day-item.today.data-v-0225c721 {
  background: #FFF8F0;
  border: 2rpx solid #FFC72C;
  transform: scale(1.05);
}
.calendar-section .calendar-card .days-grid .day-item.today .day-text.data-v-0225c721 {
  color: #FFC72C;
  font-weight: 600;
}
.calendar-section .calendar-card .days-grid .day-item.today.data-v-0225c721::after {
  content: "今日";
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  background: #FFC72C;
  color: #fff;
  font-size: 16rpx;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  transform: scale(0.8);
}
.calendar-section .calendar-card .days-grid .day-item.in-period.data-v-0225c721:not(.checked):not(.today):not(.failed):hover {
  background: #FFF2E6;
  transform: translateY(-2rpx);
}
.calendar-section .calendar-card .days-grid .day-item.in-period:not(.checked):not(.today):not(.failed) .month-text.data-v-0225c721 {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  background: rgba(255, 199, 44, 0.1);
  color: #FFC72C;
  font-size: 16rpx;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  margin: 0;
}
.calendar-section .calendar-card .days-grid .day-item.data-v-0225c721:not(.in-period) {
  background: #F8F8F8;
  opacity: 0.5;
  pointer-events: none;
}
.calendar-section .calendar-card .days-grid .day-item:not(.in-period) .day-text.data-v-0225c721, .calendar-section .calendar-card .days-grid .day-item:not(.in-period) .month-text.data-v-0225c721 {
  color: #999;
}
.calendar-section .calendar-card .days-grid .day-item .day-text.data-v-0225c721 {
  font-size: 28rpx;
  line-height: 1.2;
  font-weight: 500;
  color: #333;
}
.calendar-section .calendar-card .days-grid .day-item .month-text.data-v-0225c721 {
  font-size: 20rpx;
  color: #666;
  margin-top: 4rpx;
}
.time-section.data-v-0225c721 {
  margin-bottom: 40rpx;
}
.time-section .section-title.data-v-0225c721 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  display: block;
}
.time-section .time-card.data-v-0225c721 {
  background: #fff;
  border-radius: 32rpx;
  padding: 40rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 32rpx rgba(255, 199, 44, 0.08);
  transition: all 0.3s ease;
}
.time-section .time-card.data-v-0225c721:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 36rpx rgba(255, 199, 44, 0.12);
}
.time-section .time-card .time-icon.data-v-0225c721 {
  width: 96rpx;
  height: 96rpx;
  border-radius: 48rpx;
  background: #FFF8F0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}
.time-section .time-card .time-icon image.data-v-0225c721 {
  width: 48rpx;
  height: 48rpx;
}
.time-section .time-card .time-info.data-v-0225c721 {
  flex: 1;
}
.time-section .time-card .time-info .time-value.data-v-0225c721 {
  font-size: 36rpx;
  font-weight: 600;
  background: linear-gradient(135deg, #FFC72C, #FFB300);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 12rpx;
  display: block;
}
.time-section .time-card .time-info .time-label.data-v-0225c721 {
  font-size: 26rpx;
  color: #666;
}
.modal-mask.data-v-0225c721 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  -webkit-backdrop-filter: blur(5px);
          backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}
.success-modal.data-v-0225c721 {
  width: 600rpx;
  padding: 50rpx 40rpx;
  background: #fff;
  border-radius: 32rpx;
  text-align: center;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
}
.success-modal.warning-modal .success-title.data-v-0225c721 {
  background: linear-gradient(135deg, #FF9F1C, #F76E11);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.success-modal.warning-modal .modal-btn.data-v-0225c721 {
  background: linear-gradient(135deg, #FF9F1C, #F76E11);
}
.success-modal.failed-modal .success-title.data-v-0225c721 {
  background: linear-gradient(135deg, #FF9F1C, #F76E11);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.success-modal.failed-modal .modal-btn.data-v-0225c721 {
  background: linear-gradient(135deg, #FF9F1C, #F76E11);
}
.success-modal .success-image.data-v-0225c721 {
  width: 280rpx;
  height: 280rpx;
  margin: 0 auto 40rpx;
}
.success-modal .success-image image.data-v-0225c721 {
  width: 100%;
  height: 100%;
}
.success-modal .success-text.data-v-0225c721 {
  margin-bottom: 50rpx;
}
.success-modal .success-text .success-title.data-v-0225c721 {
  font-size: 44rpx;
  font-weight: 600;
  background: linear-gradient(135deg, #FFC72C, #FFB300);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 20rpx;
  display: block;
}
.success-modal .success-text .success-desc.data-v-0225c721 {
  font-size: 30rpx;
  color: #666;
  line-height: 1.6;
}
.success-modal .modal-btn.data-v-0225c721 {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #FFC72C, #FFB300);
  border-radius: 48rpx;
  color: #fff;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(255, 199, 44, 0.2);
  transition: all 0.3s ease;
}
.success-modal .modal-btn.data-v-0225c721:active {
  transform: scale(0.98);
  background: linear-gradient(135deg, #FFC72C, #DA291C);
}
.resources.data-v-0225c721 {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.resource-item.data-v-0225c721 {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #FFF8F0;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}
.resource-item.data-v-0225c721:active {
  transform: scale(0.98);
  background: #FFF2E6;
}
.resource-icon.data-v-0225c721 {
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
}
.resource-text.data-v-0225c721 {
  color: #FFA500;
  font-size: 26rpx;
  font-weight: 500;
}

/* 动画效果 */
@keyframes wave1-0225c721 {
0%, 100% {
    transform: rotate(0deg) scale(1);
}
50% {
    transform: rotate(180deg) scale(1.1);
}
}
@keyframes wave2-0225c721 {
0%, 100% {
    transform: rotate(0deg) scale(1);
}
50% {
    transform: rotate(-180deg) scale(1.2);
}
}
/* 增强的任务区块样式 */
.task-section.enhanced.data-v-0225c721 {
  margin: 0 40rpx 32rpx;
  border-radius: 20rpx;
  overflow: hidden;
  border: 1rpx solid rgba(255, 199, 44, 0.1);
}
.task-section.enhanced .section-header.data-v-0225c721 {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  background: linear-gradient(135deg, rgba(255, 199, 44, 0.05) 0%, rgba(255, 179, 0, 0.05) 100%);
}
.task-section.enhanced .section-header .section-icon-wrapper.data-v-0225c721 {
  width: 40rpx;
  height: 40rpx;
  background: linear-gradient(135deg, #FFC72C, #FFB300);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}
.task-section.enhanced .section-header .section-icon-wrapper.tips.data-v-0225c721 {
  background: linear-gradient(135deg, #4CAF50, #66BB6A);
}
.task-section.enhanced .section-header .section-icon-wrapper.resources.data-v-0225c721 {
  background: linear-gradient(135deg, #2196F3, #42A5F5);
}
.task-section.enhanced .section-header .section-icon-wrapper .section-icon.data-v-0225c721 {
  width: 24rpx;
  height: 24rpx;
}
.task-section.enhanced .section-header .section-title.data-v-0225c721 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}
.task-section.enhanced .section-header .section-badge.data-v-0225c721 {
  padding: 4rpx 12rpx;
  background: #FFC72C;
  color: #fff;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
}
.task-section.enhanced .section-header .section-badge.tips.data-v-0225c721 {
  background: transparent;
  color: #4CAF50;
  font-size: 24rpx;
}
.task-section.enhanced .section-header .section-badge.resources.data-v-0225c721 {
  background: #2196F3;
}
.task-section.enhanced .section-content.data-v-0225c721 {
  padding: 24rpx;
  background: #fff;
}
.task-section.enhanced .section-content.tips.data-v-0225c721 {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.02) 0%, rgba(102, 187, 106, 0.02) 100%);
  border-left: 4rpx solid #4CAF50;
}
.task-section.enhanced .section-content.resources.data-v-0225c721 {
  padding: 0;
}
.task-section.enhanced .section-content.resources .resource-item.enhanced.data-v-0225c721 {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: background-color 0.3s ease;
}
.task-section.enhanced .section-content.resources .resource-item.enhanced.data-v-0225c721:last-child {
  border-bottom: none;
}
.task-section.enhanced .section-content.resources .resource-item.enhanced.data-v-0225c721:active {
  background-color: rgba(33, 150, 243, 0.05);
}
.task-section.enhanced .section-content.resources .resource-item.enhanced .resource-icon-wrapper.data-v-0225c721 {
  width: 40rpx;
  height: 40rpx;
  background: linear-gradient(135deg, #2196F3, #42A5F5);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}
.task-section.enhanced .section-content.resources .resource-item.enhanced .resource-icon-wrapper .resource-icon.data-v-0225c721 {
  width: 20rpx;
  height: 20rpx;
}
.task-section.enhanced .section-content.resources .resource-item.enhanced .resource-content.data-v-0225c721 {
  flex: 1;
}
.task-section.enhanced .section-content.resources .resource-item.enhanced .resource-content .resource-title.data-v-0225c721 {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
  display: block;
}
.task-section.enhanced .section-content.resources .resource-item.enhanced .resource-content .resource-desc.data-v-0225c721 {
  font-size: 22rpx;
  color: #999;
  display: block;
}
.task-section.enhanced .section-content.resources .resource-item.enhanced .resource-arrow .arrow-icon.data-v-0225c721 {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

/* 任务状态和进度样式 */
.task-status-section.data-v-0225c721 {
  padding: 0 40rpx 40rpx;
}
.task-status-section .task-completion-status.completed.data-v-0225c721 {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(139, 195, 74, 0.1));
  border-radius: 20rpx;
  border: 2rpx solid rgba(76, 175, 80, 0.2);
}
.task-status-section .task-completion-status.completed .completion-icon.data-v-0225c721 {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
}
.task-status-section .task-completion-status.completed .completion-icon .status-icon.data-v-0225c721 {
  width: 100%;
  height: 100%;
}
.task-status-section .task-completion-status.completed .completion-content.data-v-0225c721 {
  flex: 1;
}
.task-status-section .task-completion-status.completed .completion-content .completion-title.data-v-0225c721 {
  font-size: 28rpx;
  font-weight: 600;
  color: #4CAF50;
  margin-bottom: 4rpx;
  display: block;
}
.task-status-section .task-completion-status.completed .completion-content .completion-desc.data-v-0225c721 {
  font-size: 24rpx;
  color: #66BB6A;
  display: block;
}
.task-status-section .task-motivation.data-v-0225c721 {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: linear-gradient(135deg, rgba(255, 199, 44, 0.1), rgba(255, 179, 0, 0.1));
  border-radius: 20rpx;
  border: 2rpx solid rgba(255, 199, 44, 0.2);
}
.task-status-section .task-motivation .motivation-icon.data-v-0225c721 {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
}
.task-status-section .task-motivation .motivation-icon .motivation-img.data-v-0225c721 {
  width: 100%;
  height: 100%;
}
.task-status-section .task-motivation .motivation-content.data-v-0225c721 {
  flex: 1;
}
.task-status-section .task-motivation .motivation-content .motivation-title.data-v-0225c721 {
  font-size: 28rpx;
  font-weight: 600;
  color: #FF8F00;
  margin-bottom: 4rpx;
  display: block;
}
.task-status-section .task-motivation .motivation-content .motivation-desc.data-v-0225c721 {
  font-size: 24rpx;
  color: #FFB300;
  display: block;
  line-height: 1.4;
}
.task-progress-section.data-v-0225c721 {
  padding: 32rpx 40rpx 40rpx;
  background: linear-gradient(135deg, rgba(255, 199, 44, 0.02) 0%, rgba(255, 179, 0, 0.02) 100%);
}
.task-progress-section .progress-header.data-v-0225c721 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.task-progress-section .progress-header .progress-title.data-v-0225c721 {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}
.task-progress-section .progress-header .progress-value.data-v-0225c721 {
  font-size: 24rpx;
  font-weight: 600;
  color: #FFC72C;
}
.task-progress-section .progress-track.data-v-0225c721 {
  position: relative;
  height: 12rpx;
  background: rgba(255, 199, 44, 0.1);
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
}
.task-progress-section .progress-track .progress-fill.data-v-0225c721 {
  height: 100%;
  background: linear-gradient(90deg, #FFC72C 0%, #FFB300 100%);
  border-radius: 6rpx;
  transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}
.task-progress-section .progress-track .progress-marker.data-v-0225c721 {
  position: absolute;
  top: -4rpx;
  transform: translateX(-50%);
  transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}
.task-progress-section .progress-track .progress-marker .marker-dot.data-v-0225c721 {
  width: 20rpx;
  height: 20rpx;
  background: #FFC72C;
  border-radius: 10rpx;
  border: 3rpx solid #fff;
  box-shadow: 0 2rpx 8rpx rgba(255, 199, 44, 0.3);
}
.task-progress-section .progress-milestones.data-v-0225c721 {
  position: relative;
  height: 40rpx;
}
.task-progress-section .progress-milestones .milestone.data-v-0225c721 {
  position: absolute;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
}
.task-progress-section .progress-milestones .milestone .milestone-dot.data-v-0225c721 {
  width: 12rpx;
  height: 12rpx;
  background: rgba(255, 199, 44, 0.3);
  border-radius: 6rpx;
  margin-bottom: 8rpx;
  transition: background-color 0.3s ease;
}
.task-progress-section .progress-milestones .milestone .milestone-text.data-v-0225c721 {
  font-size: 20rpx;
  color: #999;
  font-weight: 500;
}
.task-progress-section .progress-milestones .milestone.completed .milestone-dot.data-v-0225c721 {
  background: #FFC72C;
}
.task-progress-section .progress-milestones .milestone.completed .milestone-text.data-v-0225c721 {
  color: #FFC72C;
}

/* 动画效果 */
@keyframes wave1-0225c721 {
0%, 100% {
    transform: rotate(0deg) scale(1);
}
50% {
    transform: rotate(180deg) scale(1.1);
}
}
@keyframes wave2-0225c721 {
0%, 100% {
    transform: rotate(0deg) scale(1);
}
50% {
    transform: rotate(-180deg) scale(1.1);
}
}
@keyframes shine-0225c721 {
0% {
    left: -100%;
}
100% {
    left: 100%;
}
}
@keyframes pulse-0225c721 {
0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
}
50% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0.5;
}
100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
}
}
@keyframes fadeInUp-0225c721 {
from {
    opacity: 0;
    transform: translateY(30rpx);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
/* 为卡片添加进入动画 */
.today-card.data-v-0225c721,
.daily-task-card.data-v-0225c721,
.challenge-card.data-v-0225c721,
.stats-section.data-v-0225c721,
.calendar-section.data-v-0225c721,
.time-section.data-v-0225c721 {
  animation: fadeInUp-0225c721 0.6s ease-out;
  animation-fill-mode: both;
}
.today-card.data-v-0225c721 {
  animation-delay: 0.1s;
}
.daily-task-card.data-v-0225c721 {
  animation-delay: 0.2s;
}
.challenge-card.data-v-0225c721 {
  animation-delay: 0.3s;
}
.stats-section.data-v-0225c721 {
  animation-delay: 0.4s;
}
.calendar-section.data-v-0225c721 {
  animation-delay: 0.5s;
}
.time-section.data-v-0225c721 {
  animation-delay: 0.6s;
}