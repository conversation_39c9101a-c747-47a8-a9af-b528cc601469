"use strict";
const common_vendor = require("../../common/vendor.js");
const CustomNavbar = () => "../../components/custom-navbar/index.js";
const _sfc_main = {
  components: {
    CustomNavbar
  }
};
if (!Array) {
  const _component_custom_navbar = common_vendor.resolveComponent("custom-navbar");
  _component_custom_navbar();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.p({
      title: "挑战付费服务条款",
      ["show-back"]: true,
      shape: "rounded",
      ["border-radius"]: 30,
      gradient: true,
      ["gradient-colors"]: ["#FFB700", "#FFB700"],
      light: true
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-4d32b948"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/terms/terms-pay.js.map
