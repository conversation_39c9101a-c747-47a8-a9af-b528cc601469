
.pro-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #141414 0%, #1c1c1c 100%);
  position: relative;
  overflow: hidden;
  color: #fff;
}
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}
.bg-circle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.15;
}
.circle-1 {
  width: 800rpx;
  height: 800rpx;
  top: -300rpx;
  right: -200rpx;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  filter: blur(50rpx);
}
.circle-2 {
  width: 600rpx;
  height: 600rpx;
  bottom: -200rpx;
  left: -200rpx;
  background: linear-gradient(135deg, #FFA500, #FF8C00);
  filter: blur(50rpx);
}
.bg-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle, rgba(255, 215, 0, 0.3) 1px, transparent 1px);
  background-size: 30rpx 30rpx;
  opacity: 0.2;
}
.bg-glow {
  position: absolute;
  width: 300rpx;
  height: 300rpx;
  border-radius: 50%;
  filter: blur(100rpx);
}
.top-glow {
  top: -100rpx;
  right: 20%;
  background: rgba(255, 166, 0, 0.2);
}
.bottom-glow {
  bottom: -100rpx;
  left: 30%;
  background: rgba(255, 215, 0, 0.15);
}
.content-container {
  position: relative;
  z-index: 1;
  padding: 0 32rpx 60rpx;
  padding-top: calc(var(--status-bar-height) + 88rpx);
}
.header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.back-button {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
}
.back-icon {
  width: 32rpx;
  height: 32rpx;
  filter: brightness(2);
}
.pro-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 48rpx;
  position: relative;
}
.crown-wrapper {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
}
.pro-icon {
  width: 140rpx;
  height: 140rpx;
  position: relative;
  z-index: 2;
}
.crown-glow {
  position: absolute;
  width: 180rpx;
  height: 180rpx;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 215, 0, 0.8) 0%, rgba(255, 215, 0, 0) 70%);
  filter: blur(15rpx);
  z-index: 1;
  animation: pulse 3s infinite ease-in-out;
}
.shine-animation {
  animation: shine-rotate 8s linear infinite;
  filter: drop-shadow(0 0 15rpx rgba(255, 215, 0, 0.6));
}
.pro-title {
  font-size: 48rpx;
  font-weight: bold;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 16rpx;
  letter-spacing: 2rpx;
  text-align: center;
}
.pro-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 36rpx;
  text-align: center;
}
.benefits-list {
  margin-bottom: 48rpx;
  background: rgba(255, 255, 255, 0.07);
  border-radius: 24rpx;
  padding: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  will-change: transform, opacity;
}
.benefit-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}
.benefit-item:last-child {
  border-bottom: none;
}
.benefit-item:active {
  background: rgba(255, 255, 255, 0.05);
  transform: scale(0.99);
}
.benefit-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: rgba(255, 215, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
  border: 1px solid rgba(255, 215, 0, 0.2);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.benefit-icon {
  width: 42rpx;
  height: 42rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}
.benefit-content {
  flex: 1;
}
.benefit-title {
  font-size: 32rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 8rpx;
}
.benefit-desc {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.4;
}
.pro-status {
  background: linear-gradient(120deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.15));
  padding: 24rpx;
  border-radius: 20rpx;
  margin-bottom: 48rpx;
  border: 1px solid rgba(255, 215, 0, 0.3);
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}
.status-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 165, 0, 0.3));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  position: relative;
  flex-shrink: 0;
  box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.3);
}
.status-crown {
  width: 48rpx;
  height: 48rpx;
}
.status-content {
  flex: 1;
  position: relative;
  z-index: 2;
}
.status-title {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 8rpx;
}
.status-text {
  font-size: 32rpx;
  font-weight: 600;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  -webkit-background-clip: text;
  color: transparent;
  margin-bottom: 8rpx;
  display: block;
}
.expire-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
}
.status-decoration {
  position: absolute;
  top: -30%;
  right: -10%;
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 215, 0, 0.4) 0%, rgba(255, 215, 0, 0) 70%);
  opacity: 0.6;
  z-index: 1;
}
.section-title {
  font-size: 34rpx;
  color: rgba(255, 255, 255, 0.95);
  margin-bottom: 28rpx;
  font-weight: 700;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  position: relative;
  display: inline-block;
  padding-left: 24rpx;
}
.section-title:before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 30rpx;
  background: linear-gradient(to bottom, #FFD700, #FFA500);
  border-radius: 4rpx;
}
.membership-selector {
  display: flex;
  justify-content: space-between;
  margin-bottom: 56rpx;
  background: transparent;
  padding: 8rpx 0;
  border-radius: 24rpx;
  overflow: visible;
  gap: 20rpx;
}
.membership-option {
  flex: 1;
  padding: 30rpx 24rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  min-height: 180rpx;
  justify-content: space-between;
  will-change: transform, opacity;
}
.membership-option.selected {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 165, 0, 0.2));
  border: 2rpx solid #FFD700;
  transform: translateY(-8rpx) scale(1.05);
  box-shadow: 0 16rpx 40rpx rgba(255, 215, 0, 0.3);
  z-index: 2;
}
.plan-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 16rpx;
  width: 100%;
  padding-bottom: 12rpx;
  position: relative;
}
.plan-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 20%;
  right: 20%;
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
}
.plan-type {
  font-size: 30rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}
.plan-tag {
  margin-top: 8rpx;
  font-size: 20rpx;
  background: linear-gradient(120deg, #FF8C00, #FFA500);
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 140, 0, 0.3);
}
.plan-price {
  font-size: 48rpx;
  font-weight: bold;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 4rpx;
  line-height: 1.2;
}
.plan-original {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.5);
  text-decoration: line-through;
}
.option-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 1;
  pointer-events: none;
}
.option-shine {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 215, 0, 0.4) 0%, rgba(255, 215, 0, 0) 50%);
  opacity: 0.5;
  animation: rotation 8s linear infinite;
}
.pay-button {
  height: 100rpx;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 34rpx;
  font-weight: bold;
  box-shadow: 0 10rpx 30rpx rgba(255, 165, 0, 0.4);
  margin-bottom: 36rpx;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  border: none;
}
.pay-button:active {
  transform: translateY(3rpx);
  box-shadow: 0 6rpx 16rpx rgba(255, 165, 0, 0.3);
}
.pay-text {
  position: relative;
  z-index: 2;
  letter-spacing: 4rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}
.button-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0) 0%, 
    rgba(255, 255, 255, 0.3) 50%, 
    rgba(255, 255, 255, 0) 100%
  );
  z-index: 1;
  animation: button-shine 3s infinite;
}
.tips-section {
  padding: 32rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20rpx;
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}
.tips-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.5);
  line-height: 1.6;
  display: block;
  margin-bottom: 12rpx;
}
.tips-text:last-child {
  margin-bottom: 0;
}
@keyframes pulse {
0% {
    opacity: 0.5;
    transform: scale(0.95);
}
50% {
    opacity: 0.8;
    transform: scale(1.05);
}
100% {
    opacity: 0.5;
    transform: scale(0.95);
}
}
@keyframes rotation {
from {
    transform: rotate(0deg);
}
to {
    transform: rotate(360deg);
}
}
@keyframes shine-rotate {
from {
    filter: drop-shadow(0 0 5rpx rgba(255, 215, 0, 0.6));
}
50% {
    filter: drop-shadow(0 0 15rpx rgba(255, 215, 0, 0.8));
}
to {
    filter: drop-shadow(0 0 5rpx rgba(255, 215, 0, 0.6));
}
}
@keyframes button-shine {
0% {
    left: -100%;
}
20% {
    left: 100%;
}
100% {
    left: 100%;
}
}
.pro-menu-item {
  background: linear-gradient(120deg, rgba(255, 215, 0, 0.05) 0%, rgba(255, 165, 0, 0.1) 100%);
  border-left: 6rpx solid #FFD700;
  position: relative;
  overflow: hidden;
  border-top: none;
  margin-top: 0;
}
.permanent-notice {
  background: linear-gradient(120deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.15));
  padding: 24rpx;
  border-radius: 20rpx;
  margin-bottom: 48rpx;
  border: 1px solid rgba(255, 215, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}
.notice-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
}
.disabled-plan {
  opacity: 0.5;
  position: relative;
}
.disabled-plan::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 24rpx;
  pointer-events: none;
}
