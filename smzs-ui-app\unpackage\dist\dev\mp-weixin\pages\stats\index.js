"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const _sfc_main = {
  data() {
    return {
      totalEnergy: 0,
      dailyEnergy: 0,
      visibleEnergyBalls: [],
      showCollectSuccess: false,
      collectedEnergy: 0,
      backgroundImages: {
        1: "https://cos.ap-shanghai.myqcloud.com/foolishsister-1257000723/smzs/bg-level1.png",
        2: "https://cos.ap-shanghai.myqcloud.com/foolishsister-1257000723/smzs/bg-level2.png",
        3: "https://cos.ap-shanghai.myqcloud.com/foolishsister-1257000723/smzs/bg-level3.png",
        4: "https://cos.ap-shanghai.myqcloud.com/foolishsister-1257000723/smzs/bg-level4.png",
        5: "https://cos.ap-shanghai.myqcloud.com/foolishsister-1257000723/smzs/bg-level5.png"
      },
      levelThresholds: [0, 1e3, 2e3, 3e3, 4e3]
    };
  },
  computed: {
    growthLevel() {
      let level = 1;
      for (let i = 1; i < this.levelThresholds.length; i++) {
        if (this.totalEnergy >= this.levelThresholds[i]) {
          level = i + 1;
        }
      }
      return level;
    },
    currentBgImage() {
      return this.backgroundImages[this.growthLevel] || this.backgroundImages[1];
    },
    progressWidth() {
      const currentLevel = this.growthLevel;
      const currentThreshold = this.levelThresholds[currentLevel - 1];
      const nextThreshold = this.levelThresholds[currentLevel] || this.levelThresholds[currentLevel - 1];
      const progress = (this.totalEnergy - currentThreshold) / (nextThreshold - currentThreshold) * 100;
      return Math.min(Math.max(progress, 0), 100);
    }
  },
  onShow() {
    this.loadData();
  },
  methods: {
    async loadData() {
      try {
        const [todayRes, totalRes] = await Promise.all([
          api_index.getTodayEnergy(),
          api_index.getTotalEnergy()
        ]);
        this.dailyEnergy = todayRes.data.energy || 0;
        this.totalEnergy = totalRes.data.experience || 0;
        this.generateEnergyBalls(todayRes.data.dailyList || []);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/stats/index.vue:107", "加载数据失败:", error);
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "none"
        });
      }
    },
    generateEnergyBalls(dailyList) {
      const positions = this.calculateBallPositions(dailyList.length);
      this.visibleEnergyBalls = dailyList.map((daily, index) => ({
        id: daily.id,
        value: daily.energy,
        challengeName: daily.challengeName,
        top: positions[index].top + "%",
        left: positions[index].left + "%",
        isCollecting: false,
        collected: daily.collected
      }));
    },
    calculateBallPositions(count) {
      const positions = [];
      const centerX = 50;
      const centerY = 45;
      const radius = 30;
      for (let i = 0; i < count; i++) {
        const angle = i * 2 * Math.PI / count + (Math.random() * 0.2 - 0.1);
        const distance = radius * (0.6 + Math.random() * 0.4);
        positions.push({
          left: centerX + Math.cos(angle) * distance,
          top: centerY + Math.sin(angle) * distance
        });
      }
      return positions;
    },
    async collectEnergyBall(ball) {
      if (ball.collected || ball.isCollecting)
        return;
      try {
        ball.isCollecting = true;
        const res = await api_index.collectEnergy(ball.id);
        if (res.code === 200) {
          this.collectedEnergy = ball.value;
          this.showCollectSuccess = true;
          this.totalEnergy += ball.value;
          this.dailyEnergy += ball.value;
          ball.collected = true;
          await new Promise((resolve) => setTimeout(resolve, 500));
          const index = this.visibleEnergyBalls.findIndex((b) => b.id === ball.id);
          if (index > -1) {
            this.visibleEnergyBalls.splice(index, 1);
          }
          setTimeout(() => {
            this.showCollectSuccess = false;
          }, 500);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/stats/index.vue:179", "收集能量失败:", error);
        common_vendor.index.showToast({
          title: "收集失败",
          icon: "none"
        });
      } finally {
        ball.isCollecting = false;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $options.currentBgImage,
    b: common_vendor.t($data.totalEnergy),
    c: $options.progressWidth + "%",
    d: common_vendor.t($options.growthLevel),
    e: common_vendor.t($data.dailyEnergy),
    f: common_vendor.f($data.visibleEnergyBalls, (ball, index, i0) => {
      return {
        a: common_vendor.t(ball.value),
        b: common_vendor.t(ball.challengeName),
        c: index,
        d: ball.isCollecting ? 1 : "",
        e: ball.top,
        f: ball.left,
        g: common_vendor.o(($event) => $options.collectEnergyBall(ball), index)
      };
    }),
    g: $data.showCollectSuccess
  }, $data.showCollectSuccess ? {
    h: common_vendor.t($data.collectedEnergy)
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-1fa681a1"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/stats/index.js.map
