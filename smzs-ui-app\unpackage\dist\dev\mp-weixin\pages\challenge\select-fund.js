"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const common_assets = require("../../common/assets.js");
const CustomNavbar = () => "../../components/custom-navbar/index.js";
const _sfc_main = {
  components: {
    CustomNavbar
  },
  data() {
    return {
      challengeDetail: {},
      cardList: [],
      selectedAmount: 0,
      // 单位:分
      customAmount: "",
      // 用户输入的金额(元)
      isCustomAmount: false,
      showError: false,
      agreed: false,
      loading: false,
      showAgreementModal: false,
      showParticipationModal: false,
      // 显示已参与提示框
      showEndedModal: false
      // 显示已结束提示框
    };
  },
  computed: {
    isValidAmount() {
      if (!this.isCustomAmount)
        return true;
      const amount = parseFloat(this.customAmount);
      return amount >= 1;
    },
    displayAmount() {
      if (this.isCustomAmount) {
        return this.customAmount ? Math.round(parseFloat(this.customAmount) * 100) : 0;
      }
      return this.selectedAmount;
    }
  },
  onLoad(options) {
    if (options.challengeDetail) {
      this.challengeDetail = JSON.parse(decodeURIComponent(options.challengeDetail));
      this.initCardList();
      this.checkChallengeStatus();
    } else {
      common_vendor.index.$u.toast("请求参数错误");
    }
  },
  methods: {
    initCardList() {
      const entryFee = this.challengeDetail.entryFee || 0;
      const requiredDays = this.challengeDetail.requiredDays || 0;
      this.cardList = [
        { amount: 0 },
        { amount: entryFee * requiredDays },
        { amount: entryFee * 2 * requiredDays },
        { amount: entryFee * 3 * requiredDays },
        { amount: entryFee * 4 * requiredDays }
      ];
      this.selectedAmount = entryFee * requiredDays;
    },
    formatAmount(cents) {
      if (cents === 0)
        return "免费";
      return (cents / 100).toFixed(2) + "元";
    },
    selectCard(amount) {
      this.selectedAmount = amount;
      this.isCustomAmount = false;
      this.customAmount = "";
      this.showError = false;
    },
    selectCustomAmount() {
      this.isCustomAmount = true;
      this.selectedAmount = 0;
      this.customAmount = "";
      this.showError = false;
    },
    onCustomAmountInput(e) {
      const value = e.detail.value;
      this.customAmount = value.replace(/[^\d.]/g, "");
      this.showError = parseFloat(this.customAmount) < 1;
    },
    onCustomAmountBlur() {
      if (this.customAmount) {
        const amount = parseFloat(this.customAmount);
        if (amount < 1) {
          this.showError = true;
        } else {
          this.showError = false;
        }
      }
    },
    onAgreeChange() {
      this.agreed = !this.agreed;
    },
    showTerms() {
      common_vendor.index.navigateTo({
        url: "/pages/terms/terms-pay"
      });
    },
    closeModal() {
      this.showAgreementModal = false;
    },
    agreeAndContinue() {
      this.agreed = true;
      this.showAgreementModal = false;
    },
    async handleSubmit() {
      const now = /* @__PURE__ */ new Date();
      const endDate = new Date(this.challengeDetail.endDate);
      common_vendor.index.__f__("log", "at pages/challenge/select-fund.vue:265", "提交前再次检查 - 当前时间:", now);
      common_vendor.index.__f__("log", "at pages/challenge/select-fund.vue:266", "提交前再次检查 - 挑战结束时间:", endDate);
      if (now > endDate) {
        common_vendor.index.__f__("log", "at pages/challenge/select-fund.vue:269", "提交前再次检查 - 挑战已结束");
        this.showEndedModal = true;
        return;
      }
      try {
        const res = await api_index.checkParticipationExists(this.challengeDetail.challengeId);
        common_vendor.index.__f__("log", "at pages/challenge/select-fund.vue:277", "提交前再次检查参与状态:", res);
        if (res.code === 200 && res.data && res.data.exists === true) {
          common_vendor.index.__f__("log", "at pages/challenge/select-fund.vue:280", "提交前再次检查 - 用户有未完成的挑战");
          this.showParticipationModal = true;
          return;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/challenge/select-fund.vue:285", "提交前检查挑战状态失败:", error);
      }
      if (!this.agreed) {
        this.showAgreementModal = true;
        return;
      }
      if (this.isCustomAmount && !this.isValidAmount) {
        this.showError = true;
        return;
      }
      if (this.loading)
        return;
      this.loading = true;
      try {
        common_vendor.index.showLoading({ title: "处理中...", mask: true });
        const amount = this.isCustomAmount ? Math.round(parseFloat(this.customAmount) * 100) : this.selectedAmount;
        if (amount === 0) {
          const participationData = {
            challengeId: this.challengeDetail.challengeId,
            entryFee: amount,
            title: this.challengeDetail.title,
            startDate: this.challengeDetail.startDate,
            endDate: this.challengeDetail.endDate
          };
          await api_index.joinChallenge(participationData);
          common_vendor.index.showToast({ title: "参与成功", icon: "success" });
          common_vendor.index.switchTab({ url: "/pages/home/<USER>" });
        } else {
          common_vendor.index.__f__("log", "at pages/challenge/select-fund.vue:323", "准备创建支付订单，金额:", amount);
          const payRes = await api_index.createChallengePayment({
            challengeId: this.challengeDetail.challengeId,
            amount
            // source参数已在API中自动添加
          });
          if (payRes.code === 200) {
            common_vendor.index.__f__("log", "at pages/challenge/select-fund.vue:331", "支付参数:", payRes.data);
            const source = payRes.data.paySource || "unknown";
            if (!payRes.data) {
              throw new Error("支付参数为空，请重试");
            }
            if (source === "app") {
              if (!payRes.data.appId || !payRes.data.partnerId || !payRes.data.prepayId) {
                common_vendor.index.__f__("error", "at pages/challenge/select-fund.vue:342", "APP支付参数不完整:", payRes.data);
                throw new Error("APP支付参数不完整，请重试");
              }
            } else {
              if (!payRes.data.timeStamp || !payRes.data.nonceStr || !payRes.data.package || !payRes.data.signType || !payRes.data.paySign) {
                common_vendor.index.__f__("error", "at pages/challenge/select-fund.vue:348", "小程序支付参数不完整:", payRes.data);
                throw new Error("小程序支付参数不完整，请重试");
              }
            }
            await new Promise((resolve, reject) => {
              const platform = common_vendor.index.getSystemInfoSync().platform;
              common_vendor.index.__f__("log", "at pages/challenge/select-fund.vue:357", "当前平台:", platform, "支付来源:", payRes.data.paySource);
              if (platform === "android" || platform === "ios") {
                if (payRes.data.paySource === "app") {
                  common_vendor.index.requestPayment({
                    provider: "wxpay",
                    orderInfo: {
                      appid: payRes.data.appId,
                      partnerid: payRes.data.partnerId,
                      prepayid: payRes.data.prepayId,
                      package: payRes.data.package,
                      noncestr: payRes.data.nonceStr,
                      timestamp: payRes.data.timeStamp,
                      sign: payRes.data.paySign
                    },
                    success: (res) => {
                      common_vendor.index.__f__("log", "at pages/challenge/select-fund.vue:375", "APP支付成功:", res);
                      common_vendor.index.showToast({ title: "参与成功", icon: "success" });
                      resolve();
                    },
                    fail: (err) => {
                      common_vendor.index.__f__("error", "at pages/challenge/select-fund.vue:380", "APP支付失败:", err);
                      if (err.errMsg && err.errMsg.indexOf("cancel") !== -1) {
                        common_vendor.index.showToast({ title: "支付已取消", icon: "none" });
                      } else {
                        common_vendor.index.showToast({ title: "支付失败: " + (err.errMsg || "未知错误"), icon: "none" });
                      }
                      reject(err);
                    }
                  });
                } else {
                  common_vendor.index.showToast({
                    title: "当前环境不支持此支付方式，请使用微信小程序",
                    icon: "none",
                    duration: 3e3
                  });
                  reject(new Error("不支持的支付方式"));
                }
              } else {
                common_vendor.index.requestPayment({
                  provider: "wxpay",
                  timeStamp: payRes.data.timeStamp,
                  nonceStr: payRes.data.nonceStr,
                  package: payRes.data.package,
                  signType: payRes.data.signType,
                  paySign: payRes.data.paySign,
                  success: async (res) => {
                    try {
                      common_vendor.index.__f__("log", "at pages/challenge/select-fund.vue:409", "小程序支付成功:", res);
                      common_vendor.index.showToast({ title: "参与成功", icon: "success" });
                      resolve();
                    } catch (err) {
                      common_vendor.index.__f__("error", "at pages/challenge/select-fund.vue:413", "支付成功后处理失败:", err);
                      reject(err);
                    }
                  },
                  fail: (err) => {
                    common_vendor.index.__f__("error", "at pages/challenge/select-fund.vue:418", "小程序支付失败:", err);
                    if (err.errMsg && err.errMsg.indexOf("cancel") !== -1) {
                      common_vendor.index.showToast({ title: "支付已取消", icon: "none" });
                    } else {
                      common_vendor.index.showToast({ title: "支付失败: " + (err.errMsg || "未知错误"), icon: "none" });
                    }
                    reject(err);
                  }
                });
              }
            });
            common_vendor.index.switchTab({ url: "/pages/home/<USER>" });
          } else {
            throw new Error(payRes.msg || "创建支付订单失败");
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/challenge/select-fund.vue:436", "提交失败:", error);
        common_vendor.index.showToast({
          title: error.message || "提交失败",
          icon: "none"
        });
      } finally {
        common_vendor.index.hideLoading();
        this.loading = false;
      }
    },
    // 检查挑战状态
    async checkChallengeStatus() {
      try {
        const now = /* @__PURE__ */ new Date();
        const endDate = new Date(this.challengeDetail.endDate);
        common_vendor.index.__f__("log", "at pages/challenge/select-fund.vue:454", "检查挑战状态 - 当前时间:", now);
        common_vendor.index.__f__("log", "at pages/challenge/select-fund.vue:455", "检查挑战状态 - 挑战结束时间:", endDate);
        if (now > endDate) {
          common_vendor.index.__f__("log", "at pages/challenge/select-fund.vue:458", "检查挑战状态 - 挑战已结束");
          this.showEndedModal = true;
          return;
        }
        const res = await api_index.checkParticipationExists(this.challengeDetail.challengeId);
        common_vendor.index.__f__("log", "at pages/challenge/select-fund.vue:466", "检查挑战参与状态:", res);
        if (res.code === 200 && res.data && res.data.exists === true) {
          common_vendor.index.__f__("log", "at pages/challenge/select-fund.vue:469", "检查挑战状态 - 用户有未完成的挑战");
          this.showParticipationModal = true;
        } else {
          common_vendor.index.__f__("log", "at pages/challenge/select-fund.vue:473", "检查挑战状态 - 用户可以参与该挑战");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/challenge/select-fund.vue:476", "检查挑战状态失败:", error);
      }
    },
    closeParticipationModal() {
      this.showParticipationModal = false;
      common_vendor.index.switchTab({ url: "/pages/home/<USER>" });
    },
    closeEndedModal() {
      this.showEndedModal = false;
      common_vendor.index.switchTab({ url: "/pages/home/<USER>" });
    }
  }
};
if (!Array) {
  const _component_custom_navbar = common_vendor.resolveComponent("custom-navbar");
  _component_custom_navbar();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      title: $data.challengeDetail.title,
      ["show-back"]: true,
      shape: "rounded",
      ["border-radius"]: 30,
      gradient: true,
      ["gradient-colors"]: ["#FFB700", "#FFB700"],
      light: true
    }),
    b: common_assets._imports_0$4,
    c: common_vendor.f($data.cardList, (card, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t($options.formatAmount(card.amount)),
        b: card.amount === 0
      }, card.amount === 0 ? {} : {}, {
        c: index,
        d: $data.selectedAmount === card.amount && !$data.isCustomAmount ? 1 : "",
        e: common_vendor.o(($event) => $options.selectCard(card.amount), index)
      });
    }),
    d: $data.isCustomAmount ? 1 : "",
    e: common_vendor.o((...args) => $options.selectCustomAmount && $options.selectCustomAmount(...args)),
    f: $data.isCustomAmount
  }, $data.isCustomAmount ? common_vendor.e({
    g: common_vendor.o([($event) => $data.customAmount = $event.detail.value, (...args) => $options.onCustomAmountInput && $options.onCustomAmountInput(...args)]),
    h: common_vendor.o((...args) => $options.onCustomAmountBlur && $options.onCustomAmountBlur(...args)),
    i: $data.customAmount,
    j: $data.showError
  }, $data.showError ? {} : {}) : {}, {
    k: $data.agreed ? 1 : "",
    l: common_vendor.o((...args) => $options.showTerms && $options.showTerms(...args)),
    m: common_vendor.o((...args) => $options.onAgreeChange && $options.onAgreeChange(...args)),
    n: $options.displayAmount > 0
  }, $options.displayAmount > 0 ? {
    o: common_vendor.t($options.formatAmount($options.displayAmount))
  } : {}, {
    p: !$data.agreed || $data.isCustomAmount && !$options.isValidAmount ? 1 : "",
    q: common_vendor.o((...args) => $options.handleSubmit && $options.handleSubmit(...args)),
    r: $data.showAgreementModal
  }, $data.showAgreementModal ? {
    s: common_assets._imports_1$3,
    t: common_vendor.o((...args) => $options.closeModal && $options.closeModal(...args)),
    v: common_vendor.o((...args) => $options.closeModal && $options.closeModal(...args)),
    w: common_vendor.o((...args) => $options.agreeAndContinue && $options.agreeAndContinue(...args))
  } : {}, {
    x: $data.showParticipationModal
  }, $data.showParticipationModal ? {
    y: common_assets._imports_1$3,
    z: common_vendor.o((...args) => $options.closeParticipationModal && $options.closeParticipationModal(...args)),
    A: common_vendor.o((...args) => $options.closeParticipationModal && $options.closeParticipationModal(...args))
  } : {}, {
    B: $data.showEndedModal
  }, $data.showEndedModal ? {
    C: common_assets._imports_1$3,
    D: common_vendor.o((...args) => $options.closeEndedModal && $options.closeEndedModal(...args)),
    E: common_vendor.o((...args) => $options.closeEndedModal && $options.closeEndedModal(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-b0ae1aa1"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/challenge/select-fund.js.map
