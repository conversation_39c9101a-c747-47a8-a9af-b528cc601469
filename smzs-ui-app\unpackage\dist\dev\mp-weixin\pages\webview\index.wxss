
.container.data-v-a96d96f3 {
  position: relative;
  width: 100vw;
  height: 100vh;
}
.error-container.data-v-a96d96f3 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  padding-top: calc(var(--status-bar-height) + 88rpx);
  z-index: 10;
}
.error-icon.data-v-a96d96f3 {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 40rpx;
  opacity: 0.7;
}
.error-title.data-v-a96d96f3 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}
.error-desc.data-v-a96d96f3 {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 60rpx;
}
.retry-btn.data-v-a96d96f3, .back-btn.data-v-a96d96f3 {
  width: 70%;
  height: 88rpx;
  border-radius: 44rpx;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: 500;
}
.retry-btn.data-v-a96d96f3 {
  background: linear-gradient(135deg, #FF6D1C, #FFB700);
  color: #fff;
  box-shadow: 0 8rpx 16rpx rgba(255, 183, 0, 0.2);
  border: none;
}
.retry-btn.data-v-a96d96f3::after {
  border: none;
}
.back-btn.data-v-a96d96f3 {
  background: #f5f5f5;
  color: #666;
  border: 1rpx solid #e0e0e0;
}
