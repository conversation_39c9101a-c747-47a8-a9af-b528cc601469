
.settings-page {
  min-height: 100vh;
  background-color: #f8f9fa;
}
.content {
  padding: 24rpx;
}
.menu-list {
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
}
.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid #f5f5f5;
}
.menu-item:last-child {
  border-bottom: none;
}
.menu-text {
  font-size: 32rpx;
  color: #333;
}
.arrow-icon {
  width: 32rpx;
  height: 32rpx;
}
.logout-btn {
  margin-top: 48rpx;
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background: #ff5722;
  color: #fff;
  border-radius: 44rpx;
  font-size: 32rpx;
  border: none;
}
.feedback-popup {
  width: 600rpx;
  background: #fff;
  border-radius: 24rpx;
}
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f5f5f5;
}
.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.popup-close {
  font-size: 48rpx;
  color: #999;
  line-height: 1;
}
.popup-content {
  padding: 32rpx;
}
.feedback-input {
  width: 100%;
  height: 240rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}
.contact-input {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  margin-top: 24rpx;
  box-sizing: border-box;
}
.submit-btn {
  margin-top: 32rpx;
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background: #4CAF50;
  color: #fff;
  border-radius: 44rpx;
  font-size: 32rpx;
  border: none;
}
.submit-btn::after {
  border: none;
}
.submit-btn[disabled] {
  background: #ccc !important;
  color: #fff !important;
  border: none !important;
  outline: none !important;
}
.confirm-popup {
  width: 540rpx;
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
}
.confirm-content {
  padding: 48rpx 32rpx;
  text-align: center;
}
.confirm-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}
.confirm-buttons {
  display: flex;
  border-top: 1rpx solid #f5f5f5;
}
.confirm-buttons button {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
  border: none;
  border-radius: 0;
}
.confirm-buttons button::after {
  border: none;
}
.cancel-btn {
  background: #fff;
  color: #666;
}
.cancel-btn::after {
  border: none !important;
}
.confirm-btn {
  background: #fff;
  color: #ff5722;
  border-left: 1rpx solid #f5f5f5;
}
