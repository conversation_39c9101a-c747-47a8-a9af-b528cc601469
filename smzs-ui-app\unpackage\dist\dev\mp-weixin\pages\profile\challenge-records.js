"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const common_assets = require("../../common/assets.js");
const CustomNavBar = () => "../../components/custom-navbar/index.js";
const _sfc_main = {
  components: {
    CustomNavBar
  },
  data() {
    return {
      navHeight: 0,
      recordList: [],
      total: 0,
      pageNum: 1,
      pageSize: 10,
      isLoading: false,
      hasMore: false
    };
  },
  onLoad() {
    this.setNavHeight();
  },
  onShow() {
    this.resetAndLoadRecords();
  },
  methods: {
    // 设置导航栏高度，兼容不同平台
    setNavHeight() {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      this.navHeight = systemInfo.statusBarHeight + 44;
      if (this.navHeight === 0) {
        this.navHeight = 88;
      }
    },
    async resetAndLoadRecords() {
      this.pageNum = 1;
      await this.loadRecords(true);
    },
    onPulling() {
    },
    async onRefresh() {
      this.isLoading = true;
      await this.resetAndLoadRecords();
      this.isLoading = false;
    },
    async loadRecords(isReset = false) {
      var _a, _b;
      if (this.isLoading && !isReset)
        return;
      this.isLoading = true;
      try {
        const res = await api_index.getParticipationList({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          status: "finished"
          // 获取已结束的挑战（状态为1-成功或2-失败）
        });
        if (res.code === 200) {
          const list = ((_a = res.data) == null ? void 0 : _a.data) || [];
          if (this.pageNum === 1) {
            if (list.length > 0 || isReset) {
              this.recordList = list;
            }
          } else {
            this.recordList = [...this.recordList, ...list];
          }
          this.total = ((_b = res.data) == null ? void 0 : _b.total) || 0;
          this.hasMore = this.recordList.length < this.total;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/profile/challenge-records.vue:153", "获取挑战记录失败:", error);
        common_vendor.index.showToast({
          title: "获取挑战记录失败",
          icon: "none"
        });
      } finally {
        this.isLoading = false;
      }
    },
    loadMore() {
      if (this.hasMore && !this.isLoading) {
        this.pageNum += 1;
        this.loadRecords();
      }
    },
    viewChallengeDetail(item) {
      common_vendor.index.navigateTo({
        url: "/pages/home/<USER>" + encodeURIComponent(JSON.stringify(item))
      });
    },
    getStatusClass(status) {
      switch (status) {
        case "1":
          return "status-success";
        case "2":
          return "status-failed";
        default:
          return "";
      }
    },
    getStatusText(status) {
      switch (status) {
        case "1":
          return "挑战成功";
        case "2":
          return "挑战失败";
        default:
          return "未知状态";
      }
    },
    getProgressWidth(item) {
      if (!item.ruleDays)
        return "0%";
      const progress = item.keepDays / item.ruleDays * 100;
      return `${Math.min(progress, 100)}%`;
    },
    formatDate(dateStr) {
      if (!dateStr)
        return "";
      const date = new Date(dateStr);
      return `${date.getMonth() + 1}月${date.getDate()}日`;
    }
  }
};
if (!Array) {
  const _component_custom_nav_bar = common_vendor.resolveComponent("custom-nav-bar");
  _component_custom_nav_bar();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      title: "挑战记录"
    }),
    b: $data.recordList.length === 0 && !$data.isLoading
  }, $data.recordList.length === 0 && !$data.isLoading ? {
    c: common_assets._imports_0$7
  } : {
    d: common_vendor.f($data.recordList, (item, k0, i0) => {
      return {
        a: item.challengeImg || "/static/images/challenge/default-banner.png",
        b: common_vendor.t($options.getStatusText(item.status)),
        c: common_vendor.n($options.getStatusClass(item.status)),
        d: common_vendor.t(item.title),
        e: common_vendor.t($options.formatDate(item.startDate)),
        f: common_vendor.t($options.formatDate(item.endDate)),
        g: $options.getProgressWidth(item),
        h: common_vendor.t(item.keepDays),
        i: common_vendor.t(item.ruleDays),
        j: item.participationId,
        k: common_vendor.o(($event) => $options.viewChallengeDetail(item), item.participationId)
      };
    }),
    e: common_assets._imports_1,
    f: common_assets._imports_2
  }, {
    g: $data.recordList.length > 0 && $data.hasMore
  }, $data.recordList.length > 0 && $data.hasMore ? {
    h: common_vendor.o((...args) => $options.loadMore && $options.loadMore(...args))
  } : {}, {
    i: $data.isLoading,
    j: common_vendor.o((...args) => $options.onPulling && $options.onPulling(...args)),
    k: common_vendor.o((...args) => $options.onRefresh && $options.onRefresh(...args)),
    l: $data.navHeight + "px"
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-4af6c3e0"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/profile/challenge-records.js.map
