"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  name: "CustomNavbar",
  props: {
    title: {
      type: String,
      default: ""
    },
    titleIcon: {
      type: String,
      default: ""
    },
    showBack: {
      type: Boolean,
      default: true
    },
    background: {
      type: String,
      default: "#fff"
    },
    navHeight: {
      type: Number,
      default: 88
    },
    shadow: {
      type: Boolean,
      default: false
    },
    gradient: {
      type: Boolean,
      default: false
    },
    gradientColors: {
      type: Array,
      default: () => ["#2196F3", "#64B5F6"]
    },
    light: {
      type: Boolean,
      default: false
    },
    transparent: {
      type: Boolean,
      default: false
    },
    shape: {
      type: String,
      default: "default",
      validator: (value) => ["default", "rounded", "wave", "slant"].includes(value)
    },
    borderRadius: {
      type: [String, Number],
      default: 0
    },
    decorationLine: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    },
    decorationStyle: {
      type: String,
      default: "solid"
    }
  },
  data() {
    return {
      statusBarHeight: 20
    };
  },
  computed: {
    getBackground() {
      if (this.gradient) {
        return `linear-gradient(to right, ${this.gradientColors.join(", ")})`;
      }
      return this.background;
    },
    getNavbarClass() {
      return {
        "shadow": this.shadow,
        "gradient": this.gradient,
        "transparent": this.transparent,
        [`shape-${this.shape}`]: true,
        "has-decoration": this.decorationLine
      };
    },
    getNavbarStyle() {
      const style = {
        background: this.getBackground,
        opacity: this.transparent ? 0.98 : 1,
        height: `calc(${this.statusBarHeight}px + ${this.navHeight}rpx)`
      };
      if (this.shape === "rounded") {
        style["--border-radius"] = typeof this.borderRadius === "number" ? `${this.borderRadius}rpx` : this.borderRadius;
      }
      return style;
    }
  },
  created() {
    const systemInfo = common_vendor.index.getSystemInfoSync();
    this.statusBarHeight = systemInfo.statusBarHeight;
  },
  methods: {
    handleBack() {
      this.$emit("back");
      common_vendor.index.navigateBack({
        delta: 1
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: `${$data.statusBarHeight}px`,
    b: $props.showBack
  }, $props.showBack ? {
    c: common_assets._imports_0$10,
    d: common_vendor.o((...args) => $options.handleBack && $options.handleBack(...args))
  } : {}, {
    e: $props.titleIcon
  }, $props.titleIcon ? {
    f: $props.titleIcon
  } : {}, {
    g: common_vendor.t($props.title),
    h: $props.light || $props.gradient ? 1 : "",
    i: `${$props.navHeight}rpx`,
    j: common_vendor.n($options.getNavbarClass),
    k: common_vendor.s($options.getNavbarStyle),
    l: $props.decorationStyle
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-653593b9"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/custom-navbar/index.js.map
