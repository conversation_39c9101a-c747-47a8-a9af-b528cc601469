
.wallet-detail {
  min-height: 100vh;
  background-color: #f8f9fa;
}
.content {
  display: flex;
  flex-direction: column;
  height: 100vh;
}
.filter-bar {
  display: flex;
  background: #fff;
  padding: 24rpx;
  margin-bottom: 24rpx;
}
.filter-item {
  flex: 1;
  height: 64rpx;
  line-height: 64rpx;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
}
.filter-item.active {
  color: #4CAF50;
  font-weight: bold;
}
.filter-item.active::after {
  content: '';
  position: absolute;
  left: 50%;
  bottom: -24rpx;
  transform: translateX(-50%);
  width: 48rpx;
  height: 4rpx;
  background: #4CAF50;
  border-radius: 2rpx;
}
.transaction-list {
  flex: 1;
  background: #fff;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
.empty-tip {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 48rpx 0;
}
.transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid #f5f5f5;
}
.item-left {
  flex: 1;
}
.item-title {
  font-size: 30rpx;
  color: #333;
}
.item-time {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}
.item-right {
  text-align: right;
}
.item-amount {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  font-family: DIN;
}
.income {
  color: #4CAF50;
}
.expense {
  color: #FF5722;
}
.item-status {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}
.loading-status {
  text-align: center;
  color: #999;
  font-size: 24rpx;
  padding: 24rpx 0;
}
