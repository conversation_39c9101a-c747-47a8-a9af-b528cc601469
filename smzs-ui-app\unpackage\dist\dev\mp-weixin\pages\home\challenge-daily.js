"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const common_assets = require("../../common/assets.js");
const CustomNavbar = () => "../../components/custom-navbar/index.js";
const _sfc_main = {
  components: {
    CustomNavbar
  },
  data() {
    return {
      statusBarHeight: 20,
      challenge: {},
      stats: {
        keepDays: 0,
        completionRate: 0,
        totalEnergy: 0,
        maxContinuousDays: 0,
        averageCheckinTime: ""
      },
      selectedDates: [],
      checkinRemark: "",
      isTodayCompleted: false,
      todayStatus: null,
      currentMonth: "",
      calendarDays: [],
      isLoading: true,
      checkinPopup: false,
      isValidTime: true,
      dailyTask: null,
      currentDayNumber: 0,
      // 现代化UI控制
      isScrolled: false,
      showTitle: false,
      lastScrollTop: 0,
      headerHeight: 120,
      scrollY: 0
    };
  },
  computed: {
    // 头部样式
    headerStyle() {
      const opacity = Math.min(this.scrollY / 100, 0.95);
      return {
        background: `linear-gradient(135deg,
          rgba(138, 43, 226, ${0.8 + opacity * 0.2}) 0%,
          rgba(30, 144, 255, ${0.8 + opacity * 0.2}) 50%,
          rgba(255, 20, 147, ${0.8 + opacity * 0.2}) 100%)`,
        backdropFilter: `blur(${10 + this.scrollY * 0.1}px)`
      };
    },
    getTodayStatusText() {
      if (this.isLoading)
        return "加载中...";
      if (this.challenge.status !== "0")
        return "挑战已结束";
      if (!this.todayStatus || !this.todayStatus.record)
        return "等待打卡";
      if (this.todayStatus.record && this.todayStatus.record.checkinStatus === "1") {
        return this.todayStatus.record.isValidTime ? "打卡成功 🎉" : "时间无效 😅";
      } else if (this.todayStatus.record && this.todayStatus.record.checkinStatus === "2") {
        return "打卡失败 😔";
      } else {
        return "等待打卡";
      }
    },
    getTodayStatusClass() {
      if (this.isLoading)
        return "status-loading";
      if (this.challenge.status !== "0")
        return "status-ended";
      if (!this.todayStatus)
        return "status-pending";
      if (this.todayStatus.record && this.todayStatus.record.checkinStatus === "1") {
        return this.todayStatus.record.isValidTime ? "status-success" : "status-warning";
      } else if (this.todayStatus.record && this.todayStatus.record.checkinStatus === "2") {
        return "status-failed";
      } else {
        return "status-pending";
      }
    },
    progressWidth() {
      if (!this.challenge.ruleDays)
        return "0%";
      return `${(this.stats.keepDays / this.challenge.ruleDays * 100).toFixed(1)}%`;
    },
    periodDays() {
      if (!this.challenge.startDate || !this.challenge.endDate)
        return [];
      const startDate = new Date(this.challenge.startDate);
      const endDate = new Date(this.challenge.endDate);
      const days = [];
      const startDayOfWeek = startDate.getDay();
      for (let i = 0; i < startDayOfWeek; i++) {
        days.push({
          date: "",
          month: 0,
          showMonth: false,
          checked: false,
          isToday: false,
          inPeriod: false,
          failed: false,
          isPlaceholder: true
        });
      }
      let currentDate = new Date(startDate);
      const today = /* @__PURE__ */ new Date();
      today.setHours(0, 0, 0, 0);
      while (currentDate <= endDate) {
        const isStartMonth = currentDate.getDate() === 1 || currentDate.getTime() === startDate.getTime();
        const isFailed = currentDate < today && !this.isDateChecked(currentDate) && this.challenge.status === "0";
        days.push({
          date: currentDate.getDate(),
          month: currentDate.getMonth() + 1,
          showMonth: isStartMonth,
          checked: this.isDateChecked(currentDate),
          isToday: currentDate.toDateString() === today.toDateString(),
          inPeriod: true,
          failed: isFailed,
          fullDate: new Date(currentDate),
          dayOfWeek: currentDate.getDay()
          // 添加星期几信息 (0-6)
        });
        currentDate.setDate(currentDate.getDate() + 1);
      }
      return days;
    },
    // 计算任务进度宽度
    taskProgressWidth() {
      const totalDays = this.challenge.durationDays || 21;
      const progress = this.currentDayNumber / totalDays * 100;
      return `${Math.min(progress, 100)}%`;
    }
  },
  onPullDownRefresh() {
    this.loadData().then(() => {
      common_vendor.index.stopPullDownRefresh();
    }).catch(() => {
      common_vendor.index.stopPullDownRefresh();
    });
  },
  onLoad(options) {
    const systemInfo = common_vendor.index.getSystemInfoSync();
    this.statusBarHeight = systemInfo.statusBarHeight;
    if (options.selectedCard) {
      this.challenge = JSON.parse(decodeURIComponent(options.selectedCard));
      this.loadData();
      this.initCalendar();
    }
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 处理滚动事件 - 现代化动画效果
    handleScroll(e) {
      const scrollTop = e.detail.scrollTop;
      this.scrollY = scrollTop;
      this.isScrolled = scrollTop > 50;
      this.showTitle = scrollTop > 80;
      this.lastScrollTop = scrollTop;
    },
    // 获取粒子样式
    getParticleStyle(index) {
      const positions = [
        { left: "10%", top: "20%", animationDelay: "0s" },
        { left: "80%", top: "15%", animationDelay: "1s" },
        { left: "60%", top: "40%", animationDelay: "2s" },
        { left: "20%", top: "60%", animationDelay: "0.5s" },
        { left: "90%", top: "70%", animationDelay: "1.5s" },
        { left: "40%", top: "80%", animationDelay: "2.5s" }
      ];
      return positions[index - 1] || {};
    },
    // 获取状态表情
    getStatusEmoji() {
      const statusClass = this.getTodayStatusClass;
      const emojiMap = {
        "status-success": "🎉",
        "status-warning": "⚠️",
        "status-failed": "😔",
        "status-pending": "⏳",
        "status-ended": "🏁",
        "status-loading": "⏳"
      };
      return emojiMap[statusClass] || "⏳";
    },
    // 获取状态副标题
    getStatusSubtitle() {
      const statusClass = this.getTodayStatusClass;
      const subtitleMap = {
        "status-success": "太棒了！继续保持",
        "status-warning": "时间不对，下次注意",
        "status-failed": "没关系，明天再来",
        "status-pending": "准备好了吗？",
        "status-ended": "挑战已完成",
        "status-loading": "正在加载..."
      };
      return subtitleMap[statusClass] || "加油！";
    },
    // 获取退款状态样式类
    getRefundStatusClass(status) {
      const classMap = {
        "0": "refund-pending",
        "1": "refund-success",
        "2": "refund-failed",
        "3": "refund-processing"
      };
      return classMap[status] || "";
    },
    // 获取退款状态文本
    getRefundStatusText(status) {
      const textMap = {
        "0": "退款待处理",
        "1": "已退款",
        "2": "退款失败",
        "3": "退款处理中"
      };
      return textMap[status] || "未知状态";
    },
    async loadData() {
      var _a;
      try {
        this.isLoading = true;
        common_vendor.index.showLoading({ title: "加载中..." });
        const statusRes = await api_index.getTodayTaskStatus(this.challenge.participationId);
        if (statusRes.code === 200) {
          this.todayStatus = statusRes.data;
          this.isTodayCompleted = (_a = this.todayStatus) == null ? void 0 : _a.record;
        }
        const statsRes = await api_index.getChallengeStats(this.challenge.participationId);
        if (statsRes.code === 200) {
          this.stats = statsRes.data;
          this.updateCalendarCheckedDays(this.stats.checkinDates);
        }
        if (this.challenge.hasDailyTasks) {
          const today = /* @__PURE__ */ new Date();
          const startDate = new Date(this.challenge.startDate);
          const dayDiff = Math.floor((today - startDate) / (1e3 * 60 * 60 * 24)) + 1;
          this.currentDayNumber = Math.min(Math.max(1, dayDiff), 21);
          try {
            const taskRes = await api_index.getChallengeTask(this.challenge.participationId, this.currentDayNumber);
            if (taskRes.code === 200) {
              this.dailyTask = taskRes.data;
            }
          } catch (error) {
            common_vendor.index.__f__("error", "at pages/home/<USER>", "获取每日任务失败:", error);
          }
        }
        common_vendor.index.hideLoading();
        this.isLoading = false;
      } catch (error) {
        common_vendor.index.hideLoading();
        this.isLoading = false;
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "none"
        });
      }
    },
    formatToday() {
      const today = /* @__PURE__ */ new Date();
      return `${today.getMonth() + 1}月${today.getDate()}日`;
    },
    formatDateRange(start, end) {
      if (!start || !end)
        return "";
      const startDate = new Date(start);
      const endDate = new Date(end);
      return `${startDate.getMonth() + 1}月${startDate.getDate()}日 - ${endDate.getMonth() + 1}月${endDate.getDate()}日`;
    },
    formatDateTime(time) {
      if (!time)
        return "--:--";
      const date = new Date(time);
      return `${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}`;
    },
    formatTime(dateStr) {
      if (!dateStr)
        return "00:00";
      const timeStr = dateStr.split(" ")[1] || dateStr;
      const timeParts = timeStr.split(":");
      if (timeParts.length >= 2) {
        return `${timeParts[0]}:${timeParts[1]}`;
      }
      return timeStr;
    },
    initCalendar() {
      const startDate = new Date(this.challenge.startDate);
      const endDate = new Date(this.challenge.endDate);
      const now = /* @__PURE__ */ new Date();
      let currentDate = now;
      if (now < startDate || now > endDate) {
        currentDate = startDate;
      }
      this.currentMonth = `${currentDate.getFullYear()}年${currentDate.getMonth() + 1}月`;
      this.generateCalendarDays(currentDate);
    },
    generateCalendarDays(date) {
      const year = date.getFullYear();
      const month = date.getMonth();
      const firstDay = new Date(year, month, 1);
      const lastDay = new Date(year, month + 1, 0);
      const startDate = new Date(this.challenge.startDate);
      const endDate = new Date(this.challenge.endDate);
      const days = [];
      const startOffset = firstDay.getDay();
      for (let i = startOffset - 1; i >= 0; i--) {
        const prevDate = new Date(year, month, -i);
        days.push({
          date: prevDate.getDate(),
          currentMonth: false,
          checked: false,
          isToday: false,
          fullDate: prevDate,
          inRange: prevDate >= startDate && prevDate <= endDate
        });
      }
      const today = /* @__PURE__ */ new Date();
      for (let i = 1; i <= lastDay.getDate(); i++) {
        const currentDate = new Date(year, month, i);
        days.push({
          date: i,
          currentMonth: true,
          checked: false,
          isToday: currentDate.toDateString() === today.toDateString(),
          fullDate: currentDate,
          inRange: currentDate >= startDate && currentDate <= endDate
        });
      }
      const remainingDays = 42 - days.length;
      for (let i = 1; i <= remainingDays; i++) {
        const nextDate = new Date(year, month + 1, i);
        days.push({
          date: i,
          currentMonth: false,
          checked: false,
          isToday: false,
          fullDate: nextDate,
          inRange: nextDate >= startDate && nextDate <= endDate
        });
      }
      this.calendarDays = days;
    },
    updateCalendarCheckedDays(checkedDates) {
      if (!checkedDates || !checkedDates.length)
        return;
      if (this.stats.dailyRecords && this.stats.dailyRecords.length > 0) {
        this.stats.dailyRecords.forEach((daily) => {
          const checkedDate = new Date(daily.checkinDate);
          const dayIndex = this.calendarDays.findIndex(
            (day) => day.fullDate.toDateString() === checkedDate.toDateString()
          );
          if (dayIndex !== -1) {
            this.calendarDays[dayIndex].checked = true;
            this.calendarDays[dayIndex].invalidTime = !daily.isValidTime;
          }
        });
      } else {
        checkedDates.forEach((dateStr) => {
          const checkedDate = new Date(dateStr);
          const dayIndex = this.calendarDays.findIndex(
            (day) => day.fullDate.toDateString() === checkedDate.toDateString()
          );
          if (dayIndex !== -1) {
            this.calendarDays[dayIndex].checked = true;
          }
        });
      }
    },
    prevMonth() {
      const [year, month] = this.currentMonth.replace(/[年月]/g, "-").split("-");
      const prevDate = new Date(parseInt(year), parseInt(month) - 2);
      const startDate = new Date(this.challenge.startDate);
      if (prevDate < new Date(startDate.getFullYear(), startDate.getMonth(), 1)) {
        return;
      }
      this.currentMonth = `${prevDate.getFullYear()}年${prevDate.getMonth() + 1}月`;
      this.generateCalendarDays(prevDate);
      this.updateCalendarCheckedDays(this.stats.checkinDates);
    },
    nextMonth() {
      const [year, month] = this.currentMonth.replace(/[年月]/g, "-").split("-");
      const nextDate = new Date(parseInt(year), parseInt(month));
      const endDate = new Date(this.challenge.endDate);
      if (nextDate > new Date(endDate.getFullYear(), endDate.getMonth(), 1)) {
        return;
      }
      this.currentMonth = `${nextDate.getFullYear()}年${nextDate.getMonth() + 1}月`;
      this.generateCalendarDays(nextDate);
      this.updateCalendarCheckedDays(this.stats.checkinDates);
    },
    async handleCheckin() {
      if (this.challenge.status !== "0") {
        common_vendor.index.showToast({
          title: "任务已结束",
          icon: "none"
        });
        return;
      }
      if (this.isTodayCompleted) {
        common_vendor.index.showToast({
          title: "今日已完成打卡",
          icon: "none"
        });
        return;
      }
      try {
        common_vendor.index.showLoading({ title: "打卡中..." });
        const res = await api_index.checkInTask({
          participationId: this.challenge.participationId,
          remark: ""
        });
        common_vendor.index.hideLoading();
        if (res.code === 200) {
          this.isTodayCompleted = true;
          this.isValidTime = res.data.isValidTime || false;
          await this.loadData();
          this.checkinPopup = true;
        } else {
          throw new Error(res.msg);
        }
      } catch (error) {
        common_vendor.index.__f__("log", "at pages/home/<USER>", "打卡错误:", error);
        common_vendor.index.hideLoading();
        let errorMsg = "";
        if (error.message) {
          errorMsg = error.message;
        } else if (typeof error === "string") {
          errorMsg = error;
        } else if (error.msg) {
          errorMsg = error.msg;
        } else {
          errorMsg = "打卡失败";
        }
        common_vendor.index.showToast({
          title: errorMsg,
          icon: "none",
          duration: 3e3
        });
      }
    },
    closeCheckinPopup() {
      this.checkinPopup = false;
    },
    isDateChecked(date) {
      if (!this.stats.checkinDates)
        return false;
      return this.stats.checkinDates.some(
        (checkedDate) => new Date(checkedDate).toDateString() === date.toDateString()
      );
    },
    getModalIcon() {
      if (this.todayStatus && this.todayStatus.record && this.todayStatus.record.checkinStatus === "2") {
        return "https://cos.ap-shanghai.myqcloud.com/foolishsister-1257000723/smzs/challenge/checkin-warning.png";
      } else if (this.todayStatus && this.todayStatus.record && this.todayStatus.record.checkinStatus === "1") {
        return this.isValidTime ? "https://cos.ap-shanghai.myqcloud.com/foolishsister-1257000723/smzs/challenge/checkin-success.png" : "https://cos.ap-shanghai.myqcloud.com/foolishsister-1257000723/smzs/challenge/checkin-warning.png";
      }
      return "https://cos.ap-shanghai.myqcloud.com/foolishsister-1257000723/smzs/challenge/checkin-warning.png";
    },
    getModalTitle() {
      if (this.todayStatus && this.todayStatus.record && this.todayStatus.record.checkinStatus === "2") {
        return "非有效时间打卡";
      } else if (this.todayStatus && this.todayStatus.record && this.todayStatus.record.checkinStatus === "1") {
        return this.isValidTime ? "打卡成功！" : "非有效时间打卡";
      }
      return "非有效时间打卡";
    },
    getModalDescription() {
      var _a, _b, _c, _d;
      const timeRange = `${this.formatTime(((_b = (_a = this.todayStatus) == null ? void 0 : _a.validTimeRange) == null ? void 0 : _b.startTime) || this.challenge.startTime)}-${this.formatTime(((_d = (_c = this.todayStatus) == null ? void 0 : _c.validTimeRange) == null ? void 0 : _d.endTime) || this.challenge.endTime)}`;
      if (this.todayStatus && this.todayStatus.record && this.todayStatus.record.checkinStatus === "2") {
        return `"错过${timeRange}，米没了！你连这都坚持不了还想翻身？开玩笑的，别哭，下次别摆烂了"`;
      } else if (this.todayStatus && this.todayStatus.record && this.todayStatus.record.checkinStatus === "1") {
        return this.isValidTime ? '"你干得不错，比昨天的你强了不少"' : `"错过${timeRange}，米没了！你连这都坚持不了还想翻身？开玩笑的，别哭，下次别摆烂了"`;
      }
      return `"错过${timeRange}，米没了！你连这都坚持不了还想翻身？开玩笑的，别哭，下次别摆烂了"`;
    },
    formatResourceUrls(urls) {
      if (!urls)
        return [];
      let urlArray = urls;
      if (typeof urls === "string") {
        try {
          urlArray = JSON.parse(urls);
        } catch (e) {
          urlArray = urls.split(",");
        }
      }
      if (!Array.isArray(urlArray)) {
        urlArray = [urlArray.toString()];
      }
      return urlArray;
    },
    openResource(url) {
      let formattedUrl = url;
      if (!url.startsWith("http://") && !url.startsWith("https://")) {
        formattedUrl = "https://" + url;
      }
      common_vendor.index.navigateTo({
        url: `/pages/webview/index?url=${encodeURIComponent(formattedUrl)}`
      });
    },
    // 获取任务分类
    getTaskCategory(task) {
      if (!task || !task.title)
        return "成长任务";
      const title = task.title.toLowerCase();
      if (title.includes("冥想") || title.includes("meditation"))
        return "冥想修行";
      if (title.includes("运动") || title.includes("exercise"))
        return "运动健身";
      if (title.includes("阅读") || title.includes("reading"))
        return "知识学习";
      if (title.includes("反思") || title.includes("reflection"))
        return "自我反思";
      if (title.includes("写作") || title.includes("writing"))
        return "创作表达";
      if (title.includes("社交") || title.includes("social"))
        return "人际交往";
      return "成长任务";
    },
    // 获取任务难度
    getTaskDifficulty(dayNumber) {
      if (dayNumber <= 7)
        return 1;
      if (dayNumber <= 14)
        return 2;
      return 3;
    },
    // 获取难度文本
    getDifficultyText(dayNumber) {
      const difficulty = this.getTaskDifficulty(dayNumber);
      const texts = ["", "入门", "进阶", "挑战"];
      return texts[difficulty] || "入门";
    },
    // 获取激励标题
    getMotivationTitle(dayNumber) {
      const titles = [
        "开始你的成长之旅！",
        "坚持就是胜利！",
        "你已经走了很远！",
        "继续保持这个节奏！",
        "你正在变得更好！",
        "每一天都是新的开始！",
        "第一周即将完成！",
        "恭喜完成第一周！",
        "进入第二阶段！",
        "你的坚持令人敬佩！",
        "已经过半了！",
        "你比想象中更强大！",
        "距离目标越来越近！",
        "第二周即将完成！",
        "进入最后冲刺阶段！",
        "你已经证明了自己！",
        "坚持到现在真不容易！",
        "胜利就在眼前！",
        "最后几天了！",
        "明天就是最后一天！",
        "恭喜完成21天挑战！"
      ];
      return titles[dayNumber - 1] || "继续加油！";
    },
    // 获取激励描述
    getMotivationDesc(dayNumber) {
      if (dayNumber <= 7) {
        return "新习惯的养成需要时间，每一天的坚持都在为未来的自己投资。";
      } else if (dayNumber <= 14) {
        return "你已经度过了最困难的适应期，现在是建立稳定习惯的关键时期。";
      } else {
        return "你即将完成这个了不起的挑战，这份坚持将成为你人生的宝贵财富。";
      }
    },
    // 获取进度里程碑
    getProgressMilestones() {
      const totalDays = this.challenge.durationDays || 21;
      return [
        { day: 1, text: "开始", position: "0%" },
        { day: Math.floor(totalDays / 3), text: "1/3", position: "33.33%" },
        { day: Math.floor(totalDays * 2 / 3), text: "2/3", position: "66.66%" },
        { day: totalDays, text: "完成", position: "100%" }
      ];
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a, _b, _c, _d;
  return common_vendor.e({
    a: common_vendor.f(6, (i, k0, i0) => {
      return {
        a: i,
        b: common_vendor.s($options.getParticleStyle(i))
      };
    }),
    b: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    c: common_vendor.t($data.challenge.title || "挑战详情"),
    d: $data.showTitle ? 1 : "",
    e: $data.isScrolled ? 1 : "",
    f: common_vendor.s($options.headerStyle),
    g: common_vendor.f(4, (i, k0, i0) => {
      return {
        a: i,
        b: common_vendor.n(`shape-${i}`)
      };
    }),
    h: common_vendor.t($options.formatToday()),
    i: common_vendor.n($options.getTodayStatusClass),
    j: common_vendor.t($options.getStatusEmoji()),
    k: common_vendor.n($options.getTodayStatusClass),
    l: common_vendor.t($options.getTodayStatusText),
    m: common_vendor.t($options.getStatusSubtitle()),
    n: common_vendor.t($options.formatTime(((_b = (_a = $data.todayStatus) == null ? void 0 : _a.validTimeRange) == null ? void 0 : _b.startTime) || $data.challenge.startTime)),
    o: common_vendor.t($options.formatTime(((_d = (_c = $data.todayStatus) == null ? void 0 : _c.validTimeRange) == null ? void 0 : _d.endTime) || $data.challenge.endTime)),
    p: $data.todayStatus && $data.todayStatus.record
  }, $data.todayStatus && $data.todayStatus.record ? common_vendor.e({
    q: common_vendor.t($options.formatDateTime($data.todayStatus.record.checkinTime)),
    r: $data.todayStatus.record.refundStatus !== "4"
  }, $data.todayStatus.record.refundStatus !== "4" ? {
    s: common_vendor.t($options.getRefundStatusText($data.todayStatus.record.refundStatus)),
    t: common_vendor.n($options.getRefundStatusClass($data.todayStatus.record.refundStatus))
  } : {}) : {}, {
    v: !$data.isTodayCompleted && $data.challenge.status === "0"
  }, !$data.isTodayCompleted && $data.challenge.status === "0" ? {
    w: common_vendor.o((...args) => $options.handleCheckin && $options.handleCheckin(...args))
  } : {}, {
    x: $data.isTodayCompleted ? 1 : "",
    y: $data.challenge.hasDailyTasks && $data.dailyTask
  }, $data.challenge.hasDailyTasks && $data.dailyTask ? common_vendor.e({
    z: common_vendor.t($data.currentDayNumber),
    A: common_vendor.t($options.getTaskCategory($data.dailyTask.task)),
    B: common_vendor.t($data.dailyTask.task.title),
    C: common_vendor.f(3, (star, k0, i0) => {
      return {
        a: star,
        b: star <= $options.getTaskDifficulty($data.currentDayNumber) ? 1 : ""
      };
    }),
    D: common_vendor.t($options.getDifficultyText($data.currentDayNumber)),
    E: common_assets._imports_0$5,
    F: common_vendor.t($data.dailyTask.task.description),
    G: $data.dailyTask.task.taskRequirements
  }, $data.dailyTask.task.taskRequirements ? {
    H: common_assets._imports_1$4,
    I: common_vendor.t($data.dailyTask.task.taskRequirements)
  } : {}, {
    J: $data.dailyTask.task.tips
  }, $data.dailyTask.task.tips ? {
    K: common_assets._imports_2$4,
    L: common_vendor.t($data.dailyTask.task.tips)
  } : {}, {
    M: $data.dailyTask.task.resourceUrls
  }, $data.dailyTask.task.resourceUrls ? {
    N: common_assets._imports_3$2,
    O: common_vendor.t($options.formatResourceUrls($data.dailyTask.task.resourceUrls).length),
    P: common_vendor.f($options.formatResourceUrls($data.dailyTask.task.resourceUrls), (url, index, i0) => {
      return {
        a: common_vendor.t(index + 1),
        b: index,
        c: common_vendor.o(($event) => $options.openResource(url), index)
      };
    }),
    Q: common_assets._imports_4$2,
    R: common_assets._imports_5$1
  } : {}, {
    S: $data.dailyTask.isCompleted
  }, $data.dailyTask.isCompleted ? {
    T: common_assets._imports_6$2
  } : {
    U: common_assets._imports_7$1,
    V: common_vendor.t($options.getMotivationTitle($data.currentDayNumber)),
    W: common_vendor.t($options.getMotivationDesc($data.currentDayNumber))
  }, {
    X: common_assets._imports_8,
    Y: common_vendor.t($data.currentDayNumber),
    Z: common_vendor.t($data.challenge.durationDays || 21),
    aa: $options.taskProgressWidth,
    ab: $options.taskProgressWidth,
    ac: common_vendor.f($options.getProgressMilestones(), (milestone, k0, i0) => {
      return {
        a: common_vendor.t(milestone.text),
        b: milestone.day,
        c: $data.currentDayNumber >= milestone.day ? 1 : "",
        d: milestone.position
      };
    })
  }) : {}, {
    ad: common_vendor.t($data.challenge.title),
    ae: common_vendor.t($options.formatDateRange($data.challenge.startDate, $data.challenge.endDate)),
    af: common_vendor.t($data.stats.keepDays),
    ag: common_vendor.t($data.challenge.ruleDays),
    ah: $options.progressWidth,
    ai: common_assets._imports_9$1,
    aj: common_vendor.t($data.stats.keepDays),
    ak: common_assets._imports_10,
    al: common_vendor.t($data.stats.totalEnergy),
    am: common_assets._imports_11,
    an: common_vendor.t($data.stats.maxContinuousDays),
    ao: common_assets._imports_12,
    ap: common_vendor.t(($data.stats.completionRate * 100).toFixed(1)),
    aq: common_vendor.f(["日", "一", "二", "三", "四", "五", "六"], (day, k0, i0) => {
      return {
        a: common_vendor.t(day),
        b: day
      };
    }),
    ar: common_vendor.f($options.periodDays, (day, index, i0) => {
      return common_vendor.e({
        a: !day.isPlaceholder
      }, !day.isPlaceholder ? common_vendor.e({
        b: common_vendor.t(day.date),
        c: day.showMonth
      }, day.showMonth ? {
        d: common_vendor.t(day.month)
      } : {}, {
        e: day.checked
      }, day.checked ? {
        f: common_assets._imports_13
      } : {}) : {}, {
        g: index,
        h: day.checked ? 1 : "",
        i: day.invalidTime ? 1 : "",
        j: day.isToday ? 1 : "",
        k: day.inPeriod ? 1 : "",
        l: day.failed ? 1 : "",
        m: day.isPlaceholder ? 1 : ""
      });
    }),
    as: common_assets._imports_14,
    at: common_vendor.t($data.stats.averageCheckinTime || "暂无数据"),
    av: common_vendor.o((...args) => $options.handleScroll && $options.handleScroll(...args)),
    aw: $data.headerHeight + "px",
    ax: $data.checkinPopup
  }, $data.checkinPopup ? {
    ay: $options.getModalIcon(),
    az: common_vendor.t($options.getModalTitle()),
    aA: common_vendor.t($options.getModalDescription()),
    aB: common_vendor.t($data.isValidTime ? "我还能更狠一点" : "发誓不再废"),
    aC: common_vendor.o((...args) => $options.closeCheckinPopup && $options.closeCheckinPopup(...args)),
    aD: !$data.isValidTime ? 1 : "",
    aE: $data.todayStatus && $data.todayStatus.record && $data.todayStatus.record.checkinStatus === "2" ? 1 : ""
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-0225c721"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/home/<USER>
