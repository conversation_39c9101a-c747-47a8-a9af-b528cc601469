<template>
    <view class="page-container">
        <!-- 背景装饰 -->
        <view class="bg-decoration">
            <view class="decoration-dots"></view>
            <view class="bg-circle circle-1"></view>
            <view class="bg-circle circle-2"></view>
            <view class="bg-circle circle-3"></view>
        </view>

        <view class="content-wrapper">
            <!-- 标签栏 -->
            <view class="tabs-container">
                <view v-for="(tab, index) in tabsList" 
                      :key="index"
                      :class="['tab-item', activeIndex === index ? 'active' : '']"
                      @tap="handleTabChange(index)">
                    {{ tab.name }}
                    <view class="tab-line" v-if="activeIndex === index"></view>
                </view>
            </view>

            <!-- 主要内容区域 -->
            <view class="main-content">
                <swiper class="swiper-content" 
                       :current="activeIndex" 
                       @change="handleSwiperChange">
                    <swiper-item v-for="(tab, index) in tabsList" :key="index">
                        <scroll-view 
                            scroll-y 
                            class="challenges-scroll" 
                            refresher-enabled
                            :bounces="false"
                            @refresherrefresh="onRefresh" 
                            :refresher-triggered="isRefreshing"
                        >
                            <view class="challenges-list">
                                <view v-for="item in getFilteredChallenges(index)" 
                                      :key="item.challenge" 
                                      @tap="handleChallengeItemClick(item)"
                                      class="challenge-card">
                                    <!-- PRO标识 -->
                                    <view v-if="item.requirePro" class="pro-badge">PRO</view>

                                    <image :src="item.imageUrls" class="card-image" mode="aspectFill"></image>
                                    <view class="card-content">
                                        <view class="card-header">
                                            <text class="title">{{ item.title }}</text>
                                            <view class="tag" :class="getTagClass(item.challengeType)">
                                                {{ item.challengeType }}
                                            </view>
                                        </view>
                                        <view class="card-info">
                                            <!-- <view class="info-item">
                                                <image src="/static/images/common/user.png" class="info-icon"></image>
                                                <text>{{ item.remark || 0 }}人参与</text>
                                            </view> -->
                                            <view class="info-item">
                                                <image src="/static/images/common/calendar.png" class="info-icon"></image>
                                                <text>{{ formatDate(item.startTime) }} - {{ formatDate(item.endTime) }}</text>
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </scroll-view>
                    </swiper-item>
                </swiper>
            </view>
        </view>

        <!-- PRO会员提示弹窗 -->
        <view class="pro-dialog" v-if="showProDialog">
            <view class="pro-dialog-mask" @tap="closeProDialog"></view>
            <view class="pro-dialog-content">
                <view class="pro-dialog-crown-container">
                    <image src="/static/images/profile/crown-big.png" class="pro-dialog-icon" mode="aspectFit"></image>
                    <view class="glow-effect"></view>
                </view>
                <view class="pro-dialog-title">PRO会员专享挑战</view>
                <view class="pro-dialog-desc">升级会员，体验精品挑战，加速自我成长</view>
                
                <view class="pro-benefits">
                    <view class="benefit-item">
                        <image src="/static/icons/check-circle.png" class="benefit-icon"></image>
                        <text>解锁所有精品挑战</text>
                    </view>
                    <view class="benefit-item">
                        <image src="/static/icons/check-circle.png" class="benefit-icon"></image>
                        <text>获取专属成长指导</text>
                    </view>
                    <view class="benefit-item">
                        <image src="/static/icons/check-circle.png" class="benefit-icon"></image>
                        <text>专属数据统计与分析</text>
                    </view>
                </view>
                
                <view class="pro-dialog-actions">
                    <button class="pro-dialog-button cancel" @tap="closeProDialog">
                        <text>暂不开通</text>
                    </button>
                    <button class="pro-dialog-button confirm" @tap="goToProPage">
                        <text class="button-tag">限时优惠</text>
                        <text>立即开通</text>
                    </button>
                </view>
                
                <view class="dialog-footer">
                    <text>开通即视为同意《会员服务协议》</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import { getChallengeList, checkProStatus } from '@/api/index';

export default {
    data() {
        return {
            activeIndex: 0,
            challenges: [],
            isRefreshing: false,
            tabsList: [
                { name: '全部' },
                { name: '生活' },
                { name: '学习' },
                { name: '运动' }
            ],
            pageNum: 1,
            pageSize: 20,
            isPro: false,
            showProDialog: false,
            selectedChallenge: null
        };
    },
    onShow() {
        this.loadChallengeList();
        this.checkProStatus();
    },
    methods: {
        handleSearch(keyword) {
            this.searchKeyword = keyword;
            // 可以在这里实现实时搜索
        },
        handleSearchConfirm(keyword) {
            this.searchKeyword = keyword;
            this.loadChallengeList();
        },
        handleFilter() {
            uni.showToast({
                title: '筛选功能开发中',
                icon: 'none'
            });
        },
        loadChallengeList() {
            getChallengeList({
                pageNum: this.pageNum,
                pageSize: this.pageSize,
                keyword: this.searchKeyword
            }).then(res => {
                if (res.code === 200) {
                    this.challenges = res.data.data || [];
                } else {
                    uni.showToast({
                        title: '获取列表失败',
                        icon: 'none'
                    });
                }
            }).catch(err => {
                console.error('获取挑战列表失败:', err);
                uni.showToast({
                    title: '获取列表失败',
                    icon: 'none'
                });
            });
        },
        handleTabChange(index) {
            this.activeIndex = index;
        },
        handleSwiperChange(e) {
            this.activeIndex = e.detail.current;
        },
        handleChallengeItemClick(item) {
            // 如果挑战需要PRO会员，检查用户是否有会员权限
            if (item.requirePro && !this.isPro) {
                this.selectedChallenge = item;
                this.showProDialog = true;
                return;
            }
            
            // 判断是否有每日任务，有则跳转到任务列表页面，否则跳转到原挑战详情页
            if (item.hasDailyTasks) {
                uni.navigateTo({
                    url: `/pages/challenge/challenge-daily-detail?data=${encodeURIComponent(JSON.stringify(item))}`
                });
            } else {
                // 原来的跳转逻辑，进入挑战详情页
                uni.navigateTo({
                    url: `/pages/challenge/challenge-detail?data=${encodeURIComponent(JSON.stringify(item))}`
                });
            }
        },
        formatDate(dateStr) {
            if (!dateStr) return '';
            const timeStr = dateStr.split(' ')[1] || dateStr;
            const timeParts = timeStr.split(':');
            if (timeParts.length >= 2) {
                return `${timeParts[0]}:${timeParts[1]}`;
            }
            return timeStr;
        },
        getTagClass(type) {
            const classMap = {
                '运动': 'tag-sport',
                '学习': 'tag-study',
                '生活': 'tag-life'
            };
            return classMap[type] || 'tag-default';
        },
        async onRefresh() {
            this.isRefreshing = true;
            await this.loadChallengeList();
            this.isRefreshing = false;
        },
        getFilteredChallenges(index) {
            if (index === 0) {
                return this.challenges;
            }
            return this.challenges.filter(item => 
                item.challengeType === this.tabsList[index].name
            );
        },
        async checkProStatus() {
            try {
                const token = uni.getStorageSync('token');
                if (!token) return;
                
                const res = await checkProStatus();
                if (res.code === 200) {
                    this.isPro = res.data.isPro;
                }
            } catch (error) {
                console.error('获取会员状态失败:', error);
            }
        },
        closeProDialog() {
            this.showProDialog = false;
        },
        goToProPage() {
            this.showProDialog = false;
            uni.navigateTo({
                url: '/pages/profile/pro-subscription'
            });
        }
    }
};
</script>

<style>
.page-container {
    min-height: 100vh;
    background: #F8F8F8;
    position: relative;
    overflow: hidden;
}

/* 重新设计背景装饰 */
.bg-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    z-index: 0;
}

.bg-circle {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, #FFC72C, #FFB300);
    opacity: 0.08;
    animation: float 20s infinite ease-in-out;
}

.circle-1 {
    width: 600rpx;
    height: 600rpx;
    top: -200rpx;
    left: -200rpx;
    animation-delay: -5s;
    background: linear-gradient(135deg, #FFC72C, #FF9800);
}

.circle-2 {
    width: 800rpx;
    height: 800rpx;
    top: 40%;
    right: -400rpx;
    animation-delay: -10s;
    background: linear-gradient(135deg, #FFB300, #FFC72C);
}

.circle-3 {
    width: 400rpx;
    height: 400rpx;
    bottom: -100rpx;
    left: 20%;
    animation-delay: -15s;
    background: linear-gradient(135deg, #FF9800, #FFB300);
}

/* 添加更多装饰元素 */
.decoration-dots {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0.1;
    background-image: radial-gradient(#FFC72C 1px, transparent 1px);
    background-size: 30px 30px;
}

.content-wrapper {
    position: relative;
    z-index: 1;
    padding-top: calc(var(--status-bar-height) + 88rpx);
    height: 100vh;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
}

.tabs-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    margin: 38rpx 32rpx 32rpx;
    border-radius: 20rpx;
    display: flex;
    justify-content: space-around;
    padding: 12rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
    z-index: 99;
    height: 88rpx;
    position: sticky;
    top: calc(var(--status-bar-height) + 88rpx);
    flex-shrink: 0;
}

.tab-item {
    position: relative;
    padding: 0 40rpx;
    font-size: 30rpx;
    color: rgba(51, 51, 51, 0.7);
    transition: all 0.3s ease;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tab-item.active {
    color: #FFC72C;
    font-weight: 600;
}

.tab-line {
    position: absolute;
    bottom: 6rpx;
    left: 50%;
    width: 40rpx;
    height: 4rpx;
    background: #FFC72C;
    border-radius: 4rpx;
    transform: translateX(-50%);
}

.main-content {
    flex: 1;
    position: relative;
    overflow: hidden;
}

.swiper-content {
    height: 100%;
}

.challenges-scroll {
    height: 100%;
    box-sizing: border-box;
}

.challenges-list {
    padding: 0 32rpx 32rpx;
    box-sizing: border-box;
}

.challenge-card {
    margin-bottom: 30rpx;
    border-radius: 24rpx;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    overflow: hidden;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
    border: 1rpx solid rgba(255, 255, 255, 0.5);
    transform: translateY(0);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform, box-shadow;
    animation: slideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    animation-fill-mode: both;
    position: relative;
}

.challenge-card:active {
    transform: translateY(2rpx) scale(0.99);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);
}

/* PRO标识样式 */
.pro-badge {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #fff;
    padding: 8rpx 16rpx;
    border-radius: 12rpx;
    font-size: 24rpx;
    font-weight: 700;
    z-index: 10;
    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
    text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

.card-image {
    width: 100%;
    height: 360rpx;
    object-fit: cover;
}

.card-content {
    padding: 32rpx;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24rpx;
}

.title {
    flex: 1;
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
    margin-right: 24rpx;
    line-height: 1.4;
}

.tag {
    padding: 8rpx 20rpx;
    border-radius: 24rpx;
    font-size: 24rpx;
    font-weight: 500;
    flex-shrink: 0;
}

.tag-sport {
    background: rgba(218, 41, 28, 0.1);
    color: #DA291C;
}

.tag-study {
    background: rgba(255, 199, 44, 0.2);
    color: #B27C00;
}

.tag-life {
    background: rgba(255, 227, 227, 0.6);
    color: #71ac12;
}

.tag-default {
    background: #F5F5F5;
    color: #999999;
}

.card-info {
    display: flex;
    align-items: center;
    gap: 20rpx;
    margin-top: 20rpx;
    flex-wrap: wrap;
}

.info-item {
    display: inline-flex;
    align-items: center;
    font-size: 26rpx;
    color: #666666;
    white-space: nowrap;
    background: rgba(0, 0, 0, 0.03);
    padding: 10rpx 20rpx;
    border-radius: 20rpx;
}

.info-icon {
    width: 28rpx;
    height: 28rpx;
    margin-right: 12rpx;
    opacity: 0.8;
}

@keyframes slideIn {
    from {
        transform: translateY(20rpx);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.challenge-card:nth-child(n) {
    animation-delay: calc(0.1s * var(--i, 0));
}

/* 添加动画效果 */
@keyframes float {
    0%, 100% {
        transform: translateY(0) rotate(0deg);
    }
    50% {
        transform: translateY(-30rpx) rotate(5deg);
    }
}

/* PRO会员弹窗样式 */
.pro-dialog {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pro-dialog-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(8px);
}

.pro-dialog-content {
    position: relative;
    width: 80%;
    max-width: 600rpx;
    background: #ffffff;
    border-radius: 32rpx;
    padding: 50rpx 30rpx 30rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: 0 30rpx 60rpx rgba(0, 0, 0, 0.25);
    animation: fadeInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    will-change: transform, opacity;
}

.pro-dialog-content::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 220rpx;
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 140, 0, 0.2));
    z-index: -1;
}

.pro-dialog-crown-container {
    position: relative;
    margin-bottom: 20rpx;
}

.pro-dialog-icon {
    width: 150rpx;
    height: 150rpx;
    filter: drop-shadow(0 10rpx 20rpx rgba(255, 183, 0, 0.4));
    animation: float 4s infinite ease-in-out;
    position: relative;
    z-index: 1;
}

.glow-effect {
    position: absolute;
    width: 200rpx;
    height: 200rpx;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.4) 0%, rgba(255, 215, 0, 0) 70%);
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 0;
    animation: pulse 2s infinite ease-in-out;
}

.pro-dialog-title {
    font-size: 44rpx;
    font-weight: 700;
    margin-bottom: 20rpx;
    background: linear-gradient(135deg, #FFD700, #FF8C00);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2rpx 10rpx rgba(255, 199, 44, 0.2);
    letter-spacing: 1px;
}

.pro-dialog-desc {
    font-size: 28rpx;
    color: #666;
    text-align: center;
    margin-bottom: 40rpx;
    line-height: 1.6;
    max-width: 85%;
}

.pro-benefits {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 16rpx;
    margin-bottom: 40rpx;
    background: #FFFCF5;
    padding: 24rpx;
    border-radius: 20rpx;
    border: 1px solid rgba(255, 215, 0, 0.15);
    box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.03);
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 16rpx;
}

.benefit-icon {
    width: 36rpx;
    height: 36rpx;
    flex-shrink: 0;
}

.benefit-item text {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
}

.pro-dialog-actions {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 10rpx;
    gap: 32rpx;
    margin-bottom: 30rpx;
}

.pro-dialog-button {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100rpx;
    font-size: 34rpx;
    border-radius: 50rpx;
    transition: all 0.3s;
    position: relative;
    outline: none;
}

.pro-dialog-button.cancel {
    background-color: #f5f5f5;
    color: #555;
    font-weight: 500;
    border: none;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.pro-dialog-button.cancel:after {
    border: none;
}

.pro-dialog-button.cancel:active {
    background-color: #e8e8e8;
    transform: translateY(2rpx);
}

.pro-dialog-button.confirm {
    background: linear-gradient(135deg, #FFD700, #FF8C00);
    color: #fff;
    font-weight: 600;
    box-shadow: 0 10rpx 24rpx rgba(255, 165, 0, 0.4);
    border: none;
    overflow: visible;
}

.pro-dialog-button.confirm:after {
    border: none;
}

.button-tag {
    position: absolute;
    top: -18rpx;
    right: 5rpx;
    background: #FF3B30;
    color: white;
    font-size: 20rpx;
    padding: 4rpx 14rpx;
    border-radius: 30rpx;
    font-weight: 500;
    white-space: nowrap;
    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
    z-index: 2;
}

.button-tag::after {
    content: "";
    position: absolute;
    bottom: -6rpx;
    left: 80%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 6rpx solid transparent;
    border-right: 6rpx solid transparent;
    border-top: 6rpx solid #FF3B30;
}

.pro-dialog-button.confirm:active {
    transform: translateY(3rpx);
    box-shadow: 0 4rpx 12rpx rgba(255, 165, 0, 0.25);
}

.dialog-footer {
    width: 100%;
    text-align: center;
    font-size: 24rpx;
    color: #999;
    margin-top: 10rpx;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(40rpx);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 0.6;
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.1);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10rpx);
    }
}
</style>
