{"version": 3, "file": "index.js", "sources": ["pages/challenge/index.vue", "../../../../tool/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY2hhbGxlbmdlL2luZGV4LnZ1ZQ"], "sourcesContent": ["<template>\r\n    <view class=\"page-container\">\r\n        <!-- 背景装饰 -->\r\n        <view class=\"bg-decoration\">\r\n            <view class=\"decoration-dots\"></view>\r\n            <view class=\"bg-circle circle-1\"></view>\r\n            <view class=\"bg-circle circle-2\"></view>\r\n            <view class=\"bg-circle circle-3\"></view>\r\n        </view>\r\n\r\n        <view class=\"content-wrapper\">\r\n            <!-- 标签栏 -->\r\n            <view class=\"tabs-container\">\r\n                <view v-for=\"(tab, index) in tabsList\" \r\n                      :key=\"index\"\r\n                      :class=\"['tab-item', activeIndex === index ? 'active' : '']\"\r\n                      @tap=\"handleTabChange(index)\">\r\n                    {{ tab.name }}\r\n                    <view class=\"tab-line\" v-if=\"activeIndex === index\"></view>\r\n                </view>\r\n            </view>\r\n\r\n            <!-- 主要内容区域 -->\r\n            <view class=\"main-content\">\r\n                <swiper class=\"swiper-content\" \r\n                       :current=\"activeIndex\" \r\n                       @change=\"handleSwiperChange\">\r\n                    <swiper-item v-for=\"(tab, index) in tabsList\" :key=\"index\">\r\n                        <scroll-view \r\n                            scroll-y \r\n                            class=\"challenges-scroll\" \r\n                            refresher-enabled\r\n                            :bounces=\"false\"\r\n                            @refresherrefresh=\"onRefresh\" \r\n                            :refresher-triggered=\"isRefreshing\"\r\n                        >\r\n                            <view class=\"challenges-list\">\r\n                                <view v-for=\"item in getFilteredChallenges(index)\" \r\n                                      :key=\"item.challenge\" \r\n                                      @tap=\"handleChallengeItemClick(item)\"\r\n                                      class=\"challenge-card\">\r\n                                    <!-- PRO标识 -->\r\n                                    <view v-if=\"item.requirePro\" class=\"pro-badge\">PRO</view>\r\n\r\n                                    <image :src=\"item.imageUrls\" class=\"card-image\" mode=\"aspectFill\"></image>\r\n                                    <view class=\"card-content\">\r\n                                        <view class=\"card-header\">\r\n                                            <text class=\"title\">{{ item.title }}</text>\r\n                                            <view class=\"tag\" :class=\"getTagClass(item.challengeType)\">\r\n                                                {{ item.challengeType }}\r\n                                            </view>\r\n                                        </view>\r\n                                        <view class=\"card-info\">\r\n                                            <!-- <view class=\"info-item\">\r\n                                                <image src=\"/static/images/common/user.png\" class=\"info-icon\"></image>\r\n                                                <text>{{ item.remark || 0 }}人参与</text>\r\n                                            </view> -->\r\n                                            <view class=\"info-item\">\r\n                                                <image src=\"/static/images/common/calendar.png\" class=\"info-icon\"></image>\r\n                                                <text>{{ formatDate(item.startTime) }} - {{ formatDate(item.endTime) }}</text>\r\n                                            </view>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                            </view>\r\n                        </scroll-view>\r\n                    </swiper-item>\r\n                </swiper>\r\n            </view>\r\n        </view>\r\n\r\n        <!-- PRO会员提示弹窗 -->\r\n        <view class=\"pro-dialog\" v-if=\"showProDialog\">\r\n            <view class=\"pro-dialog-mask\" @tap=\"closeProDialog\"></view>\r\n            <view class=\"pro-dialog-content\">\r\n                <view class=\"pro-dialog-crown-container\">\r\n                    <image src=\"/static/images/profile/crown-big.png\" class=\"pro-dialog-icon\" mode=\"aspectFit\"></image>\r\n                    <view class=\"glow-effect\"></view>\r\n                </view>\r\n                <view class=\"pro-dialog-title\">PRO会员专享挑战</view>\r\n                <view class=\"pro-dialog-desc\">升级会员，体验精品挑战，加速自我成长</view>\r\n                \r\n                <view class=\"pro-benefits\">\r\n                    <view class=\"benefit-item\">\r\n                        <image src=\"/static/icons/check-circle.png\" class=\"benefit-icon\"></image>\r\n                        <text>解锁所有精品挑战</text>\r\n                    </view>\r\n                    <view class=\"benefit-item\">\r\n                        <image src=\"/static/icons/check-circle.png\" class=\"benefit-icon\"></image>\r\n                        <text>获取专属成长指导</text>\r\n                    </view>\r\n                    <view class=\"benefit-item\">\r\n                        <image src=\"/static/icons/check-circle.png\" class=\"benefit-icon\"></image>\r\n                        <text>专属数据统计与分析</text>\r\n                    </view>\r\n                </view>\r\n                \r\n                <view class=\"pro-dialog-actions\">\r\n                    <button class=\"pro-dialog-button cancel\" @tap=\"closeProDialog\">\r\n                        <text>暂不开通</text>\r\n                    </button>\r\n                    <button class=\"pro-dialog-button confirm\" @tap=\"goToProPage\">\r\n                        <text class=\"button-tag\">限时优惠</text>\r\n                        <text>立即开通</text>\r\n                    </button>\r\n                </view>\r\n                \r\n                <view class=\"dialog-footer\">\r\n                    <text>开通即视为同意《会员服务协议》</text>\r\n                </view>\r\n            </view>\r\n        </view>\r\n    </view>\r\n</template>\r\n\r\n<script>\r\nimport { getChallengeList, checkProStatus } from '@/api/index';\r\n\r\nexport default {\r\n    data() {\r\n        return {\r\n            activeIndex: 0,\r\n            challenges: [],\r\n            isRefreshing: false,\r\n            tabsList: [\r\n                { name: '全部' },\r\n                { name: '生活' },\r\n                { name: '学习' },\r\n                { name: '运动' }\r\n            ],\r\n            pageNum: 1,\r\n            pageSize: 20,\r\n            isPro: false,\r\n            showProDialog: false,\r\n            selectedChallenge: null\r\n        };\r\n    },\r\n    onShow() {\r\n        this.loadChallengeList();\r\n        this.checkProStatus();\r\n    },\r\n    methods: {\r\n        handleSearch(keyword) {\r\n            this.searchKeyword = keyword;\r\n            // 可以在这里实现实时搜索\r\n        },\r\n        handleSearchConfirm(keyword) {\r\n            this.searchKeyword = keyword;\r\n            this.loadChallengeList();\r\n        },\r\n        handleFilter() {\r\n            uni.showToast({\r\n                title: '筛选功能开发中',\r\n                icon: 'none'\r\n            });\r\n        },\r\n        loadChallengeList() {\r\n            getChallengeList({\r\n                pageNum: this.pageNum,\r\n                pageSize: this.pageSize,\r\n                keyword: this.searchKeyword\r\n            }).then(res => {\r\n                if (res.code === 200) {\r\n                    this.challenges = res.data.data || [];\r\n                } else {\r\n                    uni.showToast({\r\n                        title: '获取列表失败',\r\n                        icon: 'none'\r\n                    });\r\n                }\r\n            }).catch(err => {\r\n                console.error('获取挑战列表失败:', err);\r\n                uni.showToast({\r\n                    title: '获取列表失败',\r\n                    icon: 'none'\r\n                });\r\n            });\r\n        },\r\n        handleTabChange(index) {\r\n            this.activeIndex = index;\r\n        },\r\n        handleSwiperChange(e) {\r\n            this.activeIndex = e.detail.current;\r\n        },\r\n        handleChallengeItemClick(item) {\r\n            // 如果挑战需要PRO会员，检查用户是否有会员权限\r\n            if (item.requirePro && !this.isPro) {\r\n                this.selectedChallenge = item;\r\n                this.showProDialog = true;\r\n                return;\r\n            }\r\n            \r\n            // 判断是否有每日任务，有则跳转到任务列表页面，否则跳转到原挑战详情页\r\n            if (item.hasDailyTasks) {\r\n                uni.navigateTo({\r\n                    url: `/pages/challenge/challenge-daily-detail?data=${encodeURIComponent(JSON.stringify(item))}`\r\n                });\r\n            } else {\r\n                // 原来的跳转逻辑，进入挑战详情页\r\n                uni.navigateTo({\r\n                    url: `/pages/challenge/challenge-detail?data=${encodeURIComponent(JSON.stringify(item))}`\r\n                });\r\n            }\r\n        },\r\n        formatDate(dateStr) {\r\n            if (!dateStr) return '';\r\n            const timeStr = dateStr.split(' ')[1] || dateStr;\r\n            const timeParts = timeStr.split(':');\r\n            if (timeParts.length >= 2) {\r\n                return `${timeParts[0]}:${timeParts[1]}`;\r\n            }\r\n            return timeStr;\r\n        },\r\n        getTagClass(type) {\r\n            const classMap = {\r\n                '运动': 'tag-sport',\r\n                '学习': 'tag-study',\r\n                '生活': 'tag-life'\r\n            };\r\n            return classMap[type] || 'tag-default';\r\n        },\r\n        async onRefresh() {\r\n            this.isRefreshing = true;\r\n            await this.loadChallengeList();\r\n            this.isRefreshing = false;\r\n        },\r\n        getFilteredChallenges(index) {\r\n            if (index === 0) {\r\n                return this.challenges;\r\n            }\r\n            return this.challenges.filter(item => \r\n                item.challengeType === this.tabsList[index].name\r\n            );\r\n        },\r\n        async checkProStatus() {\r\n            try {\r\n                const token = uni.getStorageSync('token');\r\n                if (!token) return;\r\n                \r\n                const res = await checkProStatus();\r\n                if (res.code === 200) {\r\n                    this.isPro = res.data.isPro;\r\n                }\r\n            } catch (error) {\r\n                console.error('获取会员状态失败:', error);\r\n            }\r\n        },\r\n        closeProDialog() {\r\n            this.showProDialog = false;\r\n        },\r\n        goToProPage() {\r\n            this.showProDialog = false;\r\n            uni.navigateTo({\r\n                url: '/pages/profile/pro-subscription'\r\n            });\r\n        }\r\n    }\r\n};\r\n</script>\r\n\r\n<style>\r\n.page-container {\r\n    min-height: 100vh;\r\n    background: #F8F8F8;\r\n    position: relative;\r\n    overflow: hidden;\r\n}\r\n\r\n/* 重新设计背景装饰 */\r\n.bg-decoration {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    overflow: hidden;\r\n    z-index: 0;\r\n}\r\n\r\n.bg-circle {\r\n    position: absolute;\r\n    border-radius: 50%;\r\n    background: linear-gradient(135deg, #FFC72C, #FFB300);\r\n    opacity: 0.08;\r\n    animation: float 20s infinite ease-in-out;\r\n}\r\n\r\n.circle-1 {\r\n    width: 600rpx;\r\n    height: 600rpx;\r\n    top: -200rpx;\r\n    left: -200rpx;\r\n    animation-delay: -5s;\r\n    background: linear-gradient(135deg, #FFC72C, #FF9800);\r\n}\r\n\r\n.circle-2 {\r\n    width: 800rpx;\r\n    height: 800rpx;\r\n    top: 40%;\r\n    right: -400rpx;\r\n    animation-delay: -10s;\r\n    background: linear-gradient(135deg, #FFB300, #FFC72C);\r\n}\r\n\r\n.circle-3 {\r\n    width: 400rpx;\r\n    height: 400rpx;\r\n    bottom: -100rpx;\r\n    left: 20%;\r\n    animation-delay: -15s;\r\n    background: linear-gradient(135deg, #FF9800, #FFB300);\r\n}\r\n\r\n/* 添加更多装饰元素 */\r\n.decoration-dots {\r\n    position: absolute;\r\n    width: 100%;\r\n    height: 100%;\r\n    opacity: 0.1;\r\n    background-image: radial-gradient(#FFC72C 1px, transparent 1px);\r\n    background-size: 30px 30px;\r\n}\r\n\r\n.content-wrapper {\r\n    position: relative;\r\n    z-index: 1;\r\n    padding-top: calc(var(--status-bar-height) + 88rpx);\r\n    height: 100vh;\r\n    display: flex;\r\n    flex-direction: column;\r\n    box-sizing: border-box;\r\n}\r\n\r\n.tabs-container {\r\n    background: rgba(255, 255, 255, 0.95);\r\n    backdrop-filter: blur(20px);\r\n    margin: 38rpx 32rpx 32rpx;\r\n    border-radius: 20rpx;\r\n    display: flex;\r\n    justify-content: space-around;\r\n    padding: 12rpx;\r\n    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);\r\n    z-index: 99;\r\n    height: 88rpx;\r\n    position: sticky;\r\n    top: calc(var(--status-bar-height) + 88rpx);\r\n    flex-shrink: 0;\r\n}\r\n\r\n.tab-item {\r\n    position: relative;\r\n    padding: 0 40rpx;\r\n    font-size: 30rpx;\r\n    color: rgba(51, 51, 51, 0.7);\r\n    transition: all 0.3s ease;\r\n    flex: 1;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n}\r\n\r\n.tab-item.active {\r\n    color: #FFC72C;\r\n    font-weight: 600;\r\n}\r\n\r\n.tab-line {\r\n    position: absolute;\r\n    bottom: 6rpx;\r\n    left: 50%;\r\n    width: 40rpx;\r\n    height: 4rpx;\r\n    background: #FFC72C;\r\n    border-radius: 4rpx;\r\n    transform: translateX(-50%);\r\n}\r\n\r\n.main-content {\r\n    flex: 1;\r\n    position: relative;\r\n    overflow: hidden;\r\n}\r\n\r\n.swiper-content {\r\n    height: 100%;\r\n}\r\n\r\n.challenges-scroll {\r\n    height: 100%;\r\n    box-sizing: border-box;\r\n}\r\n\r\n.challenges-list {\r\n    padding: 0 32rpx 32rpx;\r\n    box-sizing: border-box;\r\n}\r\n\r\n.challenge-card {\r\n    margin-bottom: 30rpx;\r\n    border-radius: 24rpx;\r\n    background: rgba(255, 255, 255, 0.95);\r\n    backdrop-filter: blur(20px);\r\n    overflow: hidden;\r\n    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);\r\n    border: 1rpx solid rgba(255, 255, 255, 0.5);\r\n    transform: translateY(0);\r\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n    will-change: transform, box-shadow;\r\n    animation: slideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n    animation-fill-mode: both;\r\n    position: relative;\r\n}\r\n\r\n.challenge-card:active {\r\n    transform: translateY(2rpx) scale(0.99);\r\n    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);\r\n}\r\n\r\n/* PRO标识样式 */\r\n.pro-badge {\r\n    position: absolute;\r\n    top: 20rpx;\r\n    right: 20rpx;\r\n    background: linear-gradient(135deg, #FFD700, #FFA500);\r\n    color: #fff;\r\n    padding: 8rpx 16rpx;\r\n    border-radius: 12rpx;\r\n    font-size: 24rpx;\r\n    font-weight: 700;\r\n    z-index: 10;\r\n    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);\r\n    text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.card-image {\r\n    width: 100%;\r\n    height: 360rpx;\r\n    object-fit: cover;\r\n}\r\n\r\n.card-content {\r\n    padding: 32rpx;\r\n}\r\n\r\n.card-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: flex-start;\r\n    margin-bottom: 24rpx;\r\n}\r\n\r\n.title {\r\n    flex: 1;\r\n    font-size: 32rpx;\r\n    font-weight: 600;\r\n    color: #333333;\r\n    margin-right: 24rpx;\r\n    line-height: 1.4;\r\n}\r\n\r\n.tag {\r\n    padding: 8rpx 20rpx;\r\n    border-radius: 24rpx;\r\n    font-size: 24rpx;\r\n    font-weight: 500;\r\n    flex-shrink: 0;\r\n}\r\n\r\n.tag-sport {\r\n    background: rgba(218, 41, 28, 0.1);\r\n    color: #DA291C;\r\n}\r\n\r\n.tag-study {\r\n    background: rgba(255, 199, 44, 0.2);\r\n    color: #B27C00;\r\n}\r\n\r\n.tag-life {\r\n    background: rgba(255, 227, 227, 0.6);\r\n    color: #71ac12;\r\n}\r\n\r\n.tag-default {\r\n    background: #F5F5F5;\r\n    color: #999999;\r\n}\r\n\r\n.card-info {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 20rpx;\r\n    margin-top: 20rpx;\r\n    flex-wrap: wrap;\r\n}\r\n\r\n.info-item {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    font-size: 26rpx;\r\n    color: #666666;\r\n    white-space: nowrap;\r\n    background: rgba(0, 0, 0, 0.03);\r\n    padding: 10rpx 20rpx;\r\n    border-radius: 20rpx;\r\n}\r\n\r\n.info-icon {\r\n    width: 28rpx;\r\n    height: 28rpx;\r\n    margin-right: 12rpx;\r\n    opacity: 0.8;\r\n}\r\n\r\n@keyframes slideIn {\r\n    from {\r\n        transform: translateY(20rpx);\r\n        opacity: 0;\r\n    }\r\n    to {\r\n        transform: translateY(0);\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n.challenge-card:nth-child(n) {\r\n    animation-delay: calc(0.1s * var(--i, 0));\r\n}\r\n\r\n/* 添加动画效果 */\r\n@keyframes float {\r\n    0%, 100% {\r\n        transform: translateY(0) rotate(0deg);\r\n    }\r\n    50% {\r\n        transform: translateY(-30rpx) rotate(5deg);\r\n    }\r\n}\r\n\r\n/* PRO会员弹窗样式 */\r\n.pro-dialog {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    z-index: 9999;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n}\r\n\r\n.pro-dialog-mask {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background-color: rgba(0, 0, 0, 0.7);\r\n    backdrop-filter: blur(8px);\r\n}\r\n\r\n.pro-dialog-content {\r\n    position: relative;\r\n    width: 80%;\r\n    max-width: 600rpx;\r\n    background: #ffffff;\r\n    border-radius: 32rpx;\r\n    padding: 50rpx 30rpx 30rpx;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    box-shadow: 0 30rpx 60rpx rgba(0, 0, 0, 0.25);\r\n    animation: fadeInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n    overflow: hidden;\r\n    will-change: transform, opacity;\r\n}\r\n\r\n.pro-dialog-content::before {\r\n    content: \"\";\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    height: 220rpx;\r\n    background: linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 140, 0, 0.2));\r\n    z-index: -1;\r\n}\r\n\r\n.pro-dialog-crown-container {\r\n    position: relative;\r\n    margin-bottom: 20rpx;\r\n}\r\n\r\n.pro-dialog-icon {\r\n    width: 150rpx;\r\n    height: 150rpx;\r\n    filter: drop-shadow(0 10rpx 20rpx rgba(255, 183, 0, 0.4));\r\n    animation: float 4s infinite ease-in-out;\r\n    position: relative;\r\n    z-index: 1;\r\n}\r\n\r\n.glow-effect {\r\n    position: absolute;\r\n    width: 200rpx;\r\n    height: 200rpx;\r\n    border-radius: 50%;\r\n    background: radial-gradient(circle, rgba(255, 215, 0, 0.4) 0%, rgba(255, 215, 0, 0) 70%);\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%, -50%);\r\n    z-index: 0;\r\n    animation: pulse 2s infinite ease-in-out;\r\n}\r\n\r\n.pro-dialog-title {\r\n    font-size: 44rpx;\r\n    font-weight: 700;\r\n    margin-bottom: 20rpx;\r\n    background: linear-gradient(135deg, #FFD700, #FF8C00);\r\n    -webkit-background-clip: text;\r\n    -webkit-text-fill-color: transparent;\r\n    text-shadow: 0 2rpx 10rpx rgba(255, 199, 44, 0.2);\r\n    letter-spacing: 1px;\r\n}\r\n\r\n.pro-dialog-desc {\r\n    font-size: 28rpx;\r\n    color: #666;\r\n    text-align: center;\r\n    margin-bottom: 40rpx;\r\n    line-height: 1.6;\r\n    max-width: 85%;\r\n}\r\n\r\n.pro-benefits {\r\n    width: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 16rpx;\r\n    margin-bottom: 40rpx;\r\n    background: #FFFCF5;\r\n    padding: 24rpx;\r\n    border-radius: 20rpx;\r\n    border: 1px solid rgba(255, 215, 0, 0.15);\r\n    box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.03);\r\n}\r\n\r\n.benefit-item {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 16rpx;\r\n}\r\n\r\n.benefit-icon {\r\n    width: 36rpx;\r\n    height: 36rpx;\r\n    flex-shrink: 0;\r\n}\r\n\r\n.benefit-item text {\r\n    font-size: 28rpx;\r\n    color: #333;\r\n    font-weight: 500;\r\n}\r\n\r\n.pro-dialog-actions {\r\n    width: 100%;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    padding: 0 10rpx;\r\n    gap: 32rpx;\r\n    margin-bottom: 30rpx;\r\n}\r\n\r\n.pro-dialog-button {\r\n    flex: 1;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 100rpx;\r\n    font-size: 34rpx;\r\n    border-radius: 50rpx;\r\n    transition: all 0.3s;\r\n    position: relative;\r\n    outline: none;\r\n}\r\n\r\n.pro-dialog-button.cancel {\r\n    background-color: #f5f5f5;\r\n    color: #555;\r\n    font-weight: 500;\r\n    border: none;\r\n    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.pro-dialog-button.cancel:after {\r\n    border: none;\r\n}\r\n\r\n.pro-dialog-button.cancel:active {\r\n    background-color: #e8e8e8;\r\n    transform: translateY(2rpx);\r\n}\r\n\r\n.pro-dialog-button.confirm {\r\n    background: linear-gradient(135deg, #FFD700, #FF8C00);\r\n    color: #fff;\r\n    font-weight: 600;\r\n    box-shadow: 0 10rpx 24rpx rgba(255, 165, 0, 0.4);\r\n    border: none;\r\n    overflow: visible;\r\n}\r\n\r\n.pro-dialog-button.confirm:after {\r\n    border: none;\r\n}\r\n\r\n.button-tag {\r\n    position: absolute;\r\n    top: -18rpx;\r\n    right: 5rpx;\r\n    background: #FF3B30;\r\n    color: white;\r\n    font-size: 20rpx;\r\n    padding: 4rpx 14rpx;\r\n    border-radius: 30rpx;\r\n    font-weight: 500;\r\n    white-space: nowrap;\r\n    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);\r\n    z-index: 2;\r\n}\r\n\r\n.button-tag::after {\r\n    content: \"\";\r\n    position: absolute;\r\n    bottom: -6rpx;\r\n    left: 80%;\r\n    transform: translateX(-50%);\r\n    width: 0;\r\n    height: 0;\r\n    border-left: 6rpx solid transparent;\r\n    border-right: 6rpx solid transparent;\r\n    border-top: 6rpx solid #FF3B30;\r\n}\r\n\r\n.pro-dialog-button.confirm:active {\r\n    transform: translateY(3rpx);\r\n    box-shadow: 0 4rpx 12rpx rgba(255, 165, 0, 0.25);\r\n}\r\n\r\n.dialog-footer {\r\n    width: 100%;\r\n    text-align: center;\r\n    font-size: 24rpx;\r\n    color: #999;\r\n    margin-top: 10rpx;\r\n}\r\n\r\n@keyframes fadeInUp {\r\n    from {\r\n        opacity: 0;\r\n        transform: translateY(40rpx);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: translateY(0);\r\n    }\r\n}\r\n\r\n@keyframes pulse {\r\n    0%, 100% {\r\n        opacity: 0.6;\r\n        transform: translate(-50%, -50%) scale(1);\r\n    }\r\n    50% {\r\n        opacity: 1;\r\n        transform: translate(-50%, -50%) scale(1.1);\r\n    }\r\n}\r\n\r\n@keyframes float {\r\n    0%, 100% {\r\n        transform: translateY(0);\r\n    }\r\n    50% {\r\n        transform: translateY(-10rpx);\r\n    }\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'E:/work/study/smzs/smzs-ui-app/pages/challenge/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "getChallengeList", "checkProStatus"], "mappings": ";;;;AAsHA,MAAK,YAAU;AAAA,EACX,OAAO;AACH,WAAO;AAAA,MACH,aAAa;AAAA,MACb,YAAY,CAAE;AAAA,MACd,cAAc;AAAA,MACd,UAAU;AAAA,QACN,EAAE,MAAM,KAAM;AAAA,QACd,EAAE,MAAM,KAAM;AAAA,QACd,EAAE,MAAM,KAAM;AAAA,QACd,EAAE,MAAM,KAAK;AAAA,MAChB;AAAA,MACD,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,MACP,eAAe;AAAA,MACf,mBAAmB;AAAA;EAE1B;AAAA,EACD,SAAS;AACL,SAAK,kBAAiB;AACtB,SAAK,eAAc;AAAA,EACtB;AAAA,EACD,SAAS;AAAA,IACL,aAAa,SAAS;AAClB,WAAK,gBAAgB;AAAA,IAExB;AAAA,IACD,oBAAoB,SAAS;AACzB,WAAK,gBAAgB;AACrB,WAAK,kBAAiB;AAAA,IACzB;AAAA,IACD,eAAe;AACXA,oBAAAA,MAAI,UAAU;AAAA,QACV,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAC;AAAA,IACJ;AAAA,IACD,oBAAoB;AAChBC,iCAAiB;AAAA,QACb,SAAS,KAAK;AAAA,QACd,UAAU,KAAK;AAAA,QACf,SAAS,KAAK;AAAA,OACjB,EAAE,KAAK,SAAO;AACX,YAAI,IAAI,SAAS,KAAK;AAClB,eAAK,aAAa,IAAI,KAAK,QAAQ,CAAA;AAAA,eAChC;AACHD,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,UACV,CAAC;AAAA,QACL;AAAA,MACJ,CAAC,EAAE,MAAM,SAAO;AACZA,sBAAc,MAAA,MAAA,SAAA,oCAAA,aAAa,GAAG;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QACV,CAAC;AAAA,MACL,CAAC;AAAA,IACJ;AAAA,IACD,gBAAgB,OAAO;AACnB,WAAK,cAAc;AAAA,IACtB;AAAA,IACD,mBAAmB,GAAG;AAClB,WAAK,cAAc,EAAE,OAAO;AAAA,IAC/B;AAAA,IACD,yBAAyB,MAAM;AAE3B,UAAI,KAAK,cAAc,CAAC,KAAK,OAAO;AAChC,aAAK,oBAAoB;AACzB,aAAK,gBAAgB;AACrB;AAAA,MACJ;AAGA,UAAI,KAAK,eAAe;AACpBA,sBAAAA,MAAI,WAAW;AAAA,UACX,KAAK,gDAAgD,mBAAmB,KAAK,UAAU,IAAI,CAAC,CAAC;AAAA,QACjG,CAAC;AAAA,aACE;AAEHA,sBAAAA,MAAI,WAAW;AAAA,UACX,KAAK,0CAA0C,mBAAmB,KAAK,UAAU,IAAI,CAAC,CAAC;AAAA,QAC3F,CAAC;AAAA,MACL;AAAA,IACH;AAAA,IACD,WAAW,SAAS;AAChB,UAAI,CAAC;AAAS,eAAO;AACrB,YAAM,UAAU,QAAQ,MAAM,GAAG,EAAE,CAAC,KAAK;AACzC,YAAM,YAAY,QAAQ,MAAM,GAAG;AACnC,UAAI,UAAU,UAAU,GAAG;AACvB,eAAO,GAAG,UAAU,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC;AAAA,MAC1C;AACA,aAAO;AAAA,IACV;AAAA,IACD,YAAY,MAAM;AACd,YAAM,WAAW;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA;AAEV,aAAO,SAAS,IAAI,KAAK;AAAA,IAC5B;AAAA,IACD,MAAM,YAAY;AACd,WAAK,eAAe;AACpB,YAAM,KAAK;AACX,WAAK,eAAe;AAAA,IACvB;AAAA,IACD,sBAAsB,OAAO;AACzB,UAAI,UAAU,GAAG;AACb,eAAO,KAAK;AAAA,MAChB;AACA,aAAO,KAAK,WAAW;AAAA,QAAO,UAC1B,KAAK,kBAAkB,KAAK,SAAS,KAAK,EAAE;AAAA;IAEnD;AAAA,IACD,MAAM,iBAAiB;AACnB,UAAI;AACA,cAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AACxC,YAAI,CAAC;AAAO;AAEZ,cAAM,MAAM,MAAME,UAAAA;AAClB,YAAI,IAAI,SAAS,KAAK;AAClB,eAAK,QAAQ,IAAI,KAAK;AAAA,QAC1B;AAAA,MACF,SAAO,OAAO;AACZF,+EAAc,aAAa,KAAK;AAAA,MACpC;AAAA,IACH;AAAA,IACD,iBAAiB;AACb,WAAK,gBAAgB;AAAA,IACxB;AAAA,IACD,cAAc;AACV,WAAK,gBAAgB;AACrBA,oBAAAA,MAAI,WAAW;AAAA,QACX,KAAK;AAAA,MACT,CAAC;AAAA,IACL;AAAA,EACJ;AACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChQA,GAAG,WAAW,eAAe;"}