"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      selectedPlan: 2,
      userProStatus: {
        isPro: false,
        proType: "",
        expireTime: "",
        daysRemaining: 0
      },
      membershipPlans: [
        {
          name: "月度会员",
          price: "9.9",
          originalPrice: "19.9",
          type: "month",
          key: "pro_month_price"
        },
        {
          name: "年度会员",
          price: "158",
          originalPrice: "238.8",
          tag: "优惠34%",
          type: "year",
          key: "pro_year_price"
        },
        {
          name: "永久会员",
          price: "298",
          originalPrice: "598",
          tag: "限时特惠",
          type: "permanent",
          key: "pro_permanent_price"
        }
      ],
      benefits: [
        {
          icon: "/static/images/profile/crown.png",
          title: "专属PRO挑战",
          desc: "解锁所有高级挑战，提升自我更有效"
        },
        {
          icon: "/static/images/profile/star.png",
          title: "无广告体验",
          desc: "纯净无打扰的使用体验"
        },
        {
          icon: "/static/images/profile/gift.png",
          title: "专属活动福利",
          desc: "更多福利、更多惊喜，仅限PRO会员"
        }
      ],
      showPayButton: true,
      isPermanentMember: false
    };
  },
  onLoad() {
    this.loadData();
  },
  methods: {
    // 加载所有数据
    async loadData() {
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      try {
        await Promise.all([
          this.checkUserProStatus(),
          this.loadProConfig()
        ]);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/profile/pro-subscription.vue:179", "加载数据失败:", error);
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    // 获取会员状态
    async checkUserProStatus() {
      try {
        const res = await api_index.checkProStatus();
        if (res.code === 200) {
          this.userProStatus = res.data;
          this.isPermanentMember = res.data.proType === "permanent";
          this.showPayButton = !this.isPermanentMember;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/profile/pro-subscription.vue:195", "获取会员状态失败:", error);
      }
    },
    // 加载会员配置
    async loadProConfig() {
      try {
        const res = await api_index.getProConfig();
        if (res.code === 200) {
          this.membershipPlans.forEach((plan) => {
            if (res.data[plan.key]) {
              plan.price = (parseInt(res.data[plan.key]) / 100).toFixed(2);
            }
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/profile/pro-subscription.vue:213", "获取会员配置失败:", error);
      }
    },
    // 选择会员类型
    selectPlan(index) {
      if (this.isPermanentMember && this.membershipPlans[index].type !== "permanent") {
        common_vendor.index.showToast({
          title: "您已是永久会员，无法降级",
          icon: "none"
        });
        return;
      }
      this.selectedPlan = index;
    },
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr)
        return "";
      return dateStr.split(" ")[0];
    },
    // 获取会员类型文本
    getMembershipTypeText() {
      const typeMap = {
        "month": "月度会员",
        "year": "年度会员",
        "permanent": "永久会员"
      };
      return typeMap[this.userProStatus.proType] || "普通用户";
    },
    // 处理支付
    async handlePayment() {
      common_vendor.index.showLoading({
        title: "处理中..."
      });
      try {
        const plan = this.membershipPlans[this.selectedPlan];
        const amount = parseInt(parseFloat(plan.price) * 100);
        const isRenewal = this.userProStatus.isPro && this.userProStatus.proType === plan.type;
        let source = "miniprogram";
        const subscription = {
          subscriptionType: plan.type,
          amount,
          remark: isRenewal ? "renewal" : "new",
          // 标记是否为续费
          source
          // 添加source参数标识客户端类型
        };
        const res = await api_index.createSubscription(subscription);
        common_vendor.index.hideLoading();
        if (res.code === 200 && res.data) {
          const payParams = res.data;
          common_vendor.index.requestPayment({
            provider: "wxpay",
            // 注意：根据不同平台(小程序、APP等)，支付参数可能有所不同
            ...payParams,
            success: async () => {
              await this.checkUserProStatus();
              common_vendor.index.showModal({
                title: "支付成功",
                content: `您已成功${isRenewal ? "续费" : "开通"}${plan.name}`,
                showCancel: false
              });
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at pages/profile/pro-subscription.vue:310", "支付失败:", err);
              common_vendor.index.showToast({
                title: "支付已取消",
                icon: "none"
              });
            }
          });
        } else {
          throw new Error(res.message || "创建支付订单失败");
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: error.message || "支付失败",
          icon: "none"
        });
      }
    },
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$8,
    b: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    c: common_assets._imports_1$1,
    d: common_vendor.f($data.benefits, (benefit, index, i0) => {
      return {
        a: benefit.icon,
        b: common_vendor.t(benefit.title),
        c: common_vendor.t(benefit.desc),
        d: index
      };
    }),
    e: $data.userProStatus.isPro
  }, $data.userProStatus.isPro ? common_vendor.e({
    f: common_assets._imports_2$2,
    g: common_vendor.t($options.getMembershipTypeText()),
    h: $data.userProStatus.proType !== "permanent"
  }, $data.userProStatus.proType !== "permanent" ? {
    i: common_vendor.t($options.formatDate($data.userProStatus.expireTime))
  } : {}) : {}, {
    j: common_vendor.t($data.isPermanentMember ? "您的会员套餐" : "选择会员套餐"),
    k: common_vendor.f($data.membershipPlans, (plan, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(plan.name),
        b: plan.tag
      }, plan.tag ? {
        c: common_vendor.t(plan.tag)
      } : {}, {
        d: common_vendor.t(plan.price),
        e: plan.originalPrice
      }, plan.originalPrice ? {
        f: common_vendor.t(plan.originalPrice)
      } : {}, {
        g: $data.selectedPlan === index
      }, $data.selectedPlan === index ? {} : {}, {
        h: index,
        i: common_vendor.n($data.selectedPlan === index ? "selected" : ""),
        j: common_vendor.n($data.isPermanentMember && plan.type !== "permanent" ? "disabled-plan" : ""),
        k: common_vendor.o(($event) => $options.selectPlan(index), index)
      });
    }),
    l: $data.showPayButton
  }, $data.showPayButton ? {
    m: common_vendor.t($data.userProStatus.isPro ? "续费会员" : "立即开通"),
    n: common_vendor.o((...args) => $options.handlePayment && $options.handlePayment(...args))
  } : {}, {
    o: $data.isPermanentMember
  }, $data.isPermanentMember ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/profile/pro-subscription.js.map
