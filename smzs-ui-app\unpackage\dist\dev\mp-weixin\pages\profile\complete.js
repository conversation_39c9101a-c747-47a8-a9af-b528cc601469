"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      statusBarHeight: 20,
      defaultAvatar: "/static/images/common/default-avatar.png",
      formData: {
        avatar: "",
        nickname: "",
        gender: "0"
      },
      isSubmitting: false
    };
  },
  computed: {
    isFormValid() {
      return this.formData.avatar && this.formData.nickname && this.formData.gender !== "0";
    }
  },
  onLoad() {
    const systemInfo = common_vendor.index.getSystemInfoSync();
    this.statusBarHeight = systemInfo.statusBarHeight;
    const userInfo = common_vendor.index.getStorageSync("userInfo");
    if (userInfo) {
      this.formData = {
        ...this.formData,
        ...userInfo
      };
    }
  },
  methods: {
    // 处理头像选择
    async onChooseAvatar(e) {
      try {
        const tempFilePath = e.detail.avatarUrl;
        if (!tempFilePath) {
          throw new Error("获取头像失败");
        }
        const uploadRes = await api_index.uploadAvatarApi(tempFilePath);
        if (uploadRes.code === 200) {
          this.formData.avatar = uploadRes.data.url;
        } else {
          throw new Error(uploadRes.message || "上传失败");
        }
      } catch (error) {
        common_vendor.index.showToast({
          title: error.message || "上传失败",
          icon: "none"
        });
      }
    },
    // 处理昵称变更
    onNicknameChange(e) {
      this.formData.nickname = e.detail.value;
    },
    // 选择性别
    selectGender(gender) {
      this.formData.gender = gender;
    },
    // 提交表单
    async handleSubmit() {
      if (!this.isFormValid || this.isSubmitting)
        return;
      this.isSubmitting = true;
      try {
        const res = await api_index.updateUserInfoApi(this.formData);
        if (res.code === 200) {
          common_vendor.index.setStorageSync("userInfo", this.formData);
          common_vendor.index.showToast({
            title: "保存成功",
            icon: "success",
            duration: 1500
          });
          setTimeout(() => {
            common_vendor.index.switchTab({
              url: "/pages/home/<USER>"
            });
          }, 1500);
        } else {
          throw new Error(res.message || "保存失败");
        }
      } catch (error) {
        common_vendor.index.showToast({
          title: error.message || "保存失败",
          icon: "none"
        });
      } finally {
        this.isSubmitting = false;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.statusBarHeight + "px",
    b: $data.formData.avatar || $data.defaultAvatar,
    c: common_assets._imports_0$9,
    d: common_vendor.o((...args) => $options.onChooseAvatar && $options.onChooseAvatar(...args)),
    e: common_vendor.o((...args) => $options.onNicknameChange && $options.onNicknameChange(...args)),
    f: $data.formData.nickname,
    g: common_vendor.o(($event) => $data.formData.nickname = $event.detail.value),
    h: common_assets._imports_1$6,
    i: common_assets._imports_2$5,
    j: $data.formData.gender === "1"
  }, $data.formData.gender === "1" ? {} : {}, {
    k: $data.formData.gender === "1" ? 1 : "",
    l: common_vendor.o(($event) => $options.selectGender("1")),
    m: common_assets._imports_3$3,
    n: $data.formData.gender === "2"
  }, $data.formData.gender === "2" ? {} : {}, {
    o: $data.formData.gender === "2" ? 1 : "",
    p: common_vendor.o(($event) => $options.selectGender("2")),
    q: !$data.isSubmitting
  }, !$data.isSubmitting ? {} : {}, {
    r: $options.isFormValid ? 1 : "",
    s: !$options.isFormValid || $data.isSubmitting,
    t: common_vendor.o((...args) => $options.handleSubmit && $options.handleSubmit(...args)),
    v: $data.statusBarHeight + 64 + "px"
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/profile/complete.js.map
