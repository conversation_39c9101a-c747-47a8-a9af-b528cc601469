"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const common_assets = require("../../common/assets.js");
const CustomNavbar = () => "../../components/custom-navbar/index.js";
const _sfc_main = {
  components: {
    CustomNavbar
  },
  data() {
    return {
      challenge: {},
      tasks: [],
      isLoading: true,
      showNavTitle: false,
      // 控制导航栏标题显示
      lastScrollTop: 0,
      // 上次滚动位置
      scrollDirection: "down",
      // 滚动方向
      isBottomHidden: false,
      // 底部栏是否隐藏
      scrollTimer: null,
      // 滚动计时器
      showTaskDialog: false,
      // 显示任务详情弹窗
      selectedTask: {},
      // 选中的任务
      selectedTaskDay: 0,
      // 选中的任务天数
      activeTaskIndex: -1,
      // 当前活跃的任务索引
      touchStartTime: 0,
      // 触摸开始时间
      touchStartY: 0,
      // 底部弹窗手势相关变量
      initialTouchY: 0,
      // 底部弹窗手势相关变量
      isSwiping: false,
      // 底部弹窗手势相关变量
      currentTranslateY: 0,
      // 当前底部弹窗Y轴位移
      hasParticipation: false,
      // 用户是否已参与该挑战
      isPro: false,
      // 用户是否是Pro会员
      showProDialog: false
      // 是否显示Pro会员弹窗
    };
  },
  onLoad(options) {
    if (options.data) {
      this.challenge = JSON.parse(decodeURIComponent(options.data));
      this.loadTaskList();
      this.checkParticipationStatus();
      this.checkProStatus();
    } else {
      common_vendor.index.showToast({
        title: "参数错误",
        icon: "none"
      });
      setTimeout(() => {
        common_vendor.index.navigateBack();
      }, 1500);
    }
  },
  methods: {
    async loadTaskList() {
      try {
        common_vendor.index.showLoading({ title: "加载中..." });
        const res = await api_index.getChallengeTasks(this.challenge.challengeId);
        if (res.code === 200) {
          this.tasks = res.data.tasks || [];
        } else {
          common_vendor.index.showToast({
            title: res.msg || "获取任务列表失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/challenge/challenge-daily-detail.vue:294", "获取任务列表失败:", error);
        common_vendor.index.showToast({
          title: "获取任务列表失败",
          icon: "none"
        });
      } finally {
        common_vendor.index.hideLoading();
        this.isLoading = false;
      }
    },
    formatDate(dateStr) {
      if (!dateStr)
        return "";
      const date = new Date(dateStr);
      return `${date.getMonth() + 1}月${date.getDate()}日`;
    },
    formatTime(dateStr) {
      if (!dateStr)
        return "00:00";
      const timeStr = dateStr.split(" ")[1] || dateStr;
      const timeParts = timeStr.split(":");
      if (timeParts.length >= 2) {
        return `${timeParts[0]}:${timeParts[1]}`;
      }
      return timeStr;
    },
    formatNumber(num) {
      return num < 10 ? "0" + num : num;
    },
    truncateDescription(description, maxLength) {
      if (!description)
        return "";
      if (description.length <= maxLength)
        return description;
      return description.substring(0, maxLength) + "...";
    },
    joinChallenge() {
      if (this.challenge.requirePro && !this.isPro) {
        this.showProDialog = true;
        return;
      }
      if (this.hasParticipation) {
        return;
      }
      common_vendor.index.navigateTo({
        url: `/pages/challenge/select-fund?challengeDetail=${encodeURIComponent(JSON.stringify(this.challenge))}`
      });
    },
    handleScroll(e) {
      const scrollTop = e.detail.scrollTop;
      this.showNavTitle = scrollTop > 200;
      if (scrollTop > this.lastScrollTop) {
        this.scrollDirection = "down";
      } else {
        this.scrollDirection = "up";
      }
      this.lastScrollTop = scrollTop;
    },
    showTaskDetails(task, dayNumber) {
      this.selectedTask = task;
      this.selectedTaskDay = dayNumber;
      this.showTaskDialog = true;
      this.touchStartY = 0;
      this.currentTranslateY = 0;
    },
    closeTaskDialog() {
      this.showTaskDialog = false;
    },
    formatResourceUrls(urls) {
      if (!urls)
        return [];
      let urlArray = urls;
      if (typeof urls === "string") {
        try {
          urlArray = JSON.parse(urls);
        } catch (e) {
          urlArray = urls.split(",");
        }
      }
      if (!Array.isArray(urlArray)) {
        urlArray = [urlArray.toString()];
      }
      return urlArray;
    },
    openResource(url) {
      let formattedUrl = url;
      if (!url.startsWith("http://") && !url.startsWith("https://")) {
        formattedUrl = "https://" + url;
      }
      common_vendor.index.navigateTo({
        url: `/pages/webview/index?url=${encodeURIComponent(formattedUrl)}`
      });
    },
    // 任务项触摸开始
    onTaskTouchStart(index) {
      this.touchStartTime = Date.now();
      this.activeTaskIndex = index;
    },
    // 任务项触摸结束
    onTaskTouchEnd() {
      this.activeTaskIndex = -1;
    },
    // 底部弹窗手势处理
    handleTouchStart(e) {
      this.touchStartY = e.touches[0].clientY;
      this.initialTouchY = e.touches[0].clientY;
      this.isSwiping = false;
    },
    handleTouchMove(e) {
      const currentY = e.touches[0].clientY;
      const diffY = currentY - this.touchStartY;
      if (diffY > 0) {
        this.isSwiping = true;
        this.currentTranslateY = diffY;
        const dialogEl = e.currentTarget.parentNode;
        dialogEl.style.transform = `translateY(${diffY}px)`;
        dialogEl.style.transition = "none";
        const opacity = 1 - Math.min(0.6, diffY / 600);
        const maskEl = dialogEl.parentNode;
        maskEl.style.backgroundColor = `rgba(0, 0, 0, ${opacity})`;
      }
    },
    handleTouchEnd(e) {
      if (this.isSwiping) {
        const dialogEl = e.currentTarget.parentNode;
        const diffY = this.currentTranslateY;
        if (diffY > 150) {
          this.closeTaskDialog();
        } else {
          dialogEl.style.transform = "translateY(0)";
          dialogEl.style.transition = "transform 0.3s ease";
          const maskEl = dialogEl.parentNode;
          maskEl.style.backgroundColor = "rgba(0, 0, 0, 0.3)";
        }
      }
      this.isSwiping = false;
      this.currentTranslateY = 0;
    },
    // 新增检查用户是否已参与挑战
    async checkParticipationStatus() {
      try {
        if (!this.challenge.challengeId)
          return;
        const res = await api_index.checkParticipationExists(parseInt(this.challenge.challengeId));
        if (res.code === 200) {
          this.hasParticipation = res.data && res.data.exists === true;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/challenge/challenge-daily-detail.vue:484", "检查参与状态失败:", error);
      }
    },
    // 新增检查用户是否是Pro会员
    async checkProStatus() {
      try {
        const token = common_vendor.index.getStorageSync("token");
        if (!token)
          return;
        const res = await api_index.checkProStatus();
        if (res.code === 200) {
          this.isPro = res.data.isPro;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/challenge/challenge-daily-detail.vue:499", "获取会员状态失败:", error);
      }
    },
    // 前往会员订阅页面
    goToProPage() {
      common_vendor.index.navigateTo({
        url: "/pages/profile/pro-subscription"
      });
      this.closeProDialog();
    },
    // 关闭Pro会员弹窗
    closeProDialog() {
      this.showProDialog = false;
    }
  }
};
if (!Array) {
  const _component_custom_navbar = common_vendor.resolveComponent("custom-navbar");
  _component_custom_navbar();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      title: $data.showNavTitle ? $data.challenge.title : "",
      ["show-back"]: true,
      shape: $data.showNavTitle ? "default" : "transparent",
      border: $data.showNavTitle,
      background: $data.showNavTitle ? "#ffffff" : "transparent",
      ["title-color"]: $data.showNavTitle ? "#A69770" : "#ffffff",
      ["back-icon-color"]: $data.showNavTitle ? "#A69770" : "#ffffff"
    }),
    b: $data.challenge.requirePro
  }, $data.challenge.requirePro ? {
    c: common_assets._imports_2$2
  } : {}, {
    d: common_vendor.t($data.challenge.title),
    e: common_vendor.t($data.challenge.durationDays || 21),
    f: common_assets._imports_1$2,
    g: common_vendor.t($data.challenge.durationDays || 21),
    h: common_vendor.t($options.formatTime($data.challenge.startTime)),
    i: common_vendor.t($options.formatTime($data.challenge.endTime)),
    j: common_vendor.t($options.formatDate(Date.now())),
    k: common_vendor.t($options.formatDate(Date.now() + ($data.challenge.durationDays - 1) * 24 * 60 * 60 * 1e3)),
    l: $data.challenge.description
  }, $data.challenge.description ? {
    m: common_assets._imports_2$3,
    n: common_vendor.t($data.challenge.description)
  } : {}, {
    o: $data.challenge.rewardRule
  }, $data.challenge.rewardRule ? {
    p: common_assets._imports_3$1,
    q: common_vendor.t($data.challenge.rewardRule)
  } : {}, {
    r: $data.tasks.length > 0
  }, $data.tasks.length > 0 ? {
    s: common_assets._imports_4$3,
    t: common_vendor.t($data.challenge.durationDays || 21),
    v: common_vendor.f($data.tasks, (task, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t($options.formatNumber(index + 1)),
        b: common_vendor.t(task.title),
        c: task.description
      }, task.description ? {
        d: common_vendor.t($options.truncateDescription(task.description, 60))
      } : {}, {
        e: task.taskId,
        f: index === $data.activeTaskIndex ? 1 : "",
        g: common_vendor.o(($event) => $options.showTaskDetails(task, index + 1), task.taskId),
        h: common_vendor.o(($event) => $options.onTaskTouchStart(index), task.taskId),
        i: common_vendor.o((...args) => $options.onTaskTouchEnd && $options.onTaskTouchEnd(...args), task.taskId)
      });
    }),
    w: common_assets._imports_5$1
  } : {}, {
    x: common_vendor.o((...args) => $options.handleScroll && $options.handleScroll(...args)),
    y: common_vendor.t($data.hasParticipation ? "挑战参与中" : "立即参与挑战"),
    z: $data.hasParticipation ? 1 : "",
    A: common_vendor.o((...args) => $options.joinChallenge && $options.joinChallenge(...args)),
    B: $data.showTaskDialog
  }, $data.showTaskDialog ? common_vendor.e({
    C: common_vendor.o((...args) => $options.handleTouchStart && $options.handleTouchStart(...args)),
    D: common_vendor.o((...args) => $options.handleTouchMove && $options.handleTouchMove(...args)),
    E: common_vendor.o((...args) => $options.handleTouchEnd && $options.handleTouchEnd(...args)),
    F: common_vendor.t($data.selectedTaskDay),
    G: common_vendor.t($data.selectedTask.title),
    H: $data.selectedTask.description
  }, $data.selectedTask.description ? {
    I: common_assets._imports_2$3,
    J: common_vendor.t($data.selectedTask.description)
  } : {}, {
    K: $data.selectedTask.taskRequirements
  }, $data.selectedTask.taskRequirements ? {
    L: common_assets._imports_6$1,
    M: common_vendor.t($data.selectedTask.taskRequirements)
  } : {}, {
    N: $data.selectedTask.tips
  }, $data.selectedTask.tips ? {
    O: common_assets._imports_7,
    P: common_vendor.t($data.selectedTask.tips)
  } : {}, {
    Q: $data.selectedTask.resourceUrls
  }, $data.selectedTask.resourceUrls ? {
    R: common_assets._imports_4$2,
    S: common_vendor.f($options.formatResourceUrls($data.selectedTask.resourceUrls), (url, idx, i0) => {
      return {
        a: common_vendor.t(idx + 1),
        b: idx,
        c: common_vendor.o(($event) => $options.openResource(url), idx)
      };
    }),
    T: common_assets._imports_4$2
  } : {}, {
    U: common_vendor.o(() => {
    }),
    V: common_vendor.o((...args) => $options.closeTaskDialog && $options.closeTaskDialog(...args))
  }) : {}, {
    W: $data.showProDialog
  }, $data.showProDialog ? {
    X: common_assets._imports_2$2,
    Y: common_assets._imports_9,
    Z: common_assets._imports_9,
    aa: common_assets._imports_9,
    ab: common_vendor.o((...args) => $options.closeProDialog && $options.closeProDialog(...args)),
    ac: common_vendor.o((...args) => $options.goToProPage && $options.goToProPage(...args)),
    ad: common_vendor.o(() => {
    }),
    ae: common_vendor.o((...args) => $options.closeProDialog && $options.closeProDialog(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-669d69e0"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/challenge/challenge-daily-detail.js.map
