{"version": 3, "file": "app.js", "sources": ["App.vue", "main.js"], "sourcesContent": ["<script>\r\nimport request from './utils/request'\r\n\r\nexport default {\r\n\tonLaunch: function() {\r\n\t\t// 挂载请求工具到全局\r\n\t\tuni.$http = request\r\n\t},\r\n\tonShow: function() {\r\n\t\tconsole.log('App Show')\r\n\t},\r\n\tonHide: function() {\r\n\t\tconsole.log('App Hide')\r\n\t}\r\n}\r\n</script>\r\n\r\n<style>\r\n\t/*每个页面公共css */ \r\n</style>\r\n", "import App from './App'\r\n\r\n// #ifndef VUE3\r\nimport Vue from 'vue'\r\nimport './uni.promisify.adaptor'\r\nVue.config.productionTip = false\r\nApp.mpType = 'app'\r\nconst app = new Vue({\r\n  ...App\r\n})\r\napp.$mount()\r\n// #endif\r\n\r\n// #ifdef VUE3\r\nimport { createSSRApp } from 'vue'\r\nexport function createApp() {\r\n  const app = createSSRApp(App)\r\n  return {\r\n    app\r\n  }\r\n}\r\n// #endif\r\n\r\n// 修改白名单路径格式，确保以 / 开头\r\nconst whiteList = [\r\n  '/pages/login/index', \r\n  '/pages/terms/user', \r\n  '/pages/terms/privacy'\r\n] // 白名单页面\r\n\r\n// base64解码函数\r\nfunction base64Decode(input) {\r\n  // base64编码字符集\r\n  const keyStr = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\r\n  \r\n  let output = '';\r\n  let chr1, chr2, chr3;\r\n  let enc1, enc2, enc3, enc4;\r\n  let i = 0;\r\n  \r\n  // 移除所有非base64字符\r\n  input = input.replace(/[^A-Za-z0-9\\+\\/\\=]/g, '');\r\n  \r\n  while (i < input.length) {\r\n    enc1 = keyStr.indexOf(input.charAt(i++));\r\n    enc2 = keyStr.indexOf(input.charAt(i++));\r\n    enc3 = keyStr.indexOf(input.charAt(i++));\r\n    enc4 = keyStr.indexOf(input.charAt(i++));\r\n    \r\n    chr1 = (enc1 << 2) | (enc2 >> 4);\r\n    chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);\r\n    chr3 = ((enc3 & 3) << 6) | enc4;\r\n    \r\n    output = output + String.fromCharCode(chr1);\r\n    \r\n    if (enc3 !== 64) {\r\n      output = output + String.fromCharCode(chr2);\r\n    }\r\n    if (enc4 !== 64) {\r\n      output = output + String.fromCharCode(chr3);\r\n    }\r\n  }\r\n  \r\n  return decodeURIComponent(escape(output));\r\n}\r\n\r\n// 对于JWT token的特殊base64处理\r\nfunction jwtDecode(token) {\r\n  // 替换JWT特定的base64编码和填充\r\n  const base64Url = token.replace(/-/g, '+').replace(/_/g, '/');\r\n  const padding = '='.repeat((4 - base64Url.length % 4) % 4);\r\n  \r\n  try {\r\n    return JSON.parse(base64Decode(base64Url + padding));\r\n  } catch (e) {\r\n    console.error('JWT解析错误:', e);\r\n    return null;\r\n  }\r\n}\r\n\r\n// 添加登录状态全局校验\r\nfunction isAuthenticated() {\r\n  const token = uni.getStorageSync('token')\r\n  if (!token) return false\r\n  \r\n  try {\r\n    // 检查token是否有效\r\n    const tokenParts = token.split('.')\r\n    if (tokenParts.length !== 3) {\r\n      console.log('token格式无效')\r\n      uni.removeStorageSync('token')\r\n      return false\r\n    }\r\n    \r\n    // 检查token是否过期\r\n    const payload = jwtDecode(tokenParts[1])\r\n    if (!payload) {\r\n      console.log('token内容解析失败')\r\n      uni.removeStorageSync('token')\r\n      return false\r\n    }\r\n    \r\n    if (payload.exp && payload.exp < Date.now() / 1000) {\r\n      console.log('token已过期')\r\n      uni.removeStorageSync('token')\r\n      return false\r\n    }\r\n    \r\n    return true\r\n  } catch (e) {\r\n    console.error('token验证失败', e)\r\n    uni.removeStorageSync('token')\r\n    return false\r\n  }\r\n}\r\n\r\n// 为所有页面添加守卫，确保正确的登录状态\r\nuni.addInterceptor('navigateTo', {\r\n  invoke(e) {\r\n    return checkLogin(e.url)\r\n  }\r\n})\r\n\r\nuni.addInterceptor('switchTab', {\r\n  invoke(e) {\r\n    return checkLogin(e.url)\r\n  }\r\n})\r\n\r\nuni.addInterceptor('reLaunch', {\r\n  invoke(e) {\r\n    // 特殊情况：如果是重定向到登录页，不需要再检查以避免无限循环\r\n    if (e.url === '/pages/login/index') {\r\n      return true\r\n    }\r\n    return checkLogin(e.url)\r\n  }\r\n})\r\n\r\nfunction checkLogin(url) {\r\n  // 检查是否为白名单页面\r\n  const targetPage = url.split('?')[0]\r\n  if (whiteList.includes(targetPage)) {\r\n    return true\r\n  }\r\n  \r\n  // 检查用户是否已登录\r\n  if (!isAuthenticated()) {\r\n    console.log('未登录或token无效')\r\n    \r\n    // #ifdef MP-WEIXIN\r\n    // 微信小程序环境下不跳转登录页，直接静默处理\r\n    // 让页面继续加载，相关页面会自行处理微信登录逻辑\r\n    return true\r\n    // #endif\r\n    \r\n    // #ifndef MP-WEIXIN\r\n    // 非微信小程序环境下才跳转到登录页\r\n    console.log('重定向到登录页面')\r\n    // 清除所有可能导致循环重定向的状态\r\n    setTimeout(() => {\r\n      uni.reLaunch({\r\n        url: '/pages/login/index'\r\n      })\r\n    }, 0)\r\n    return false\r\n    // #endif\r\n  }\r\n  \r\n  return true\r\n}"], "names": ["uni", "request", "createSSRApp", "App"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,MAAK,YAAU;AAAA,EACd,UAAU,WAAW;AAEpBA,kBAAG,MAAC,QAAQC,cAAM;AAAA,EAClB;AAAA,EACD,QAAQ,WAAW;AAClBD,kBAAAA,MAAA,MAAA,OAAA,iBAAY,UAAU;AAAA,EACtB;AAAA,EACD,QAAQ,WAAW;AAClBA,kBAAAA,MAAA,MAAA,OAAA,iBAAY,UAAU;AAAA,EACvB;AACD;ACCO,SAAS,YAAY;AAC1B,QAAM,MAAME,cAAY,aAACC,SAAG;AAC5B,SAAO;AAAA,IACL;AAAA,EACD;AACH;AAIA,MAAM,YAAY;AAAA,EAChB;AAAA,EACA;AAAA,EACA;AACF;AAGA,SAAS,aAAa,OAAO;AAE3B,QAAM,SAAS;AAEf,MAAI,SAAS;AACb,MAAI,MAAM,MAAM;AAChB,MAAI,MAAM,MAAM,MAAM;AACtB,MAAI,IAAI;AAGR,UAAQ,MAAM,QAAQ,uBAAuB,EAAE;AAE/C,SAAO,IAAI,MAAM,QAAQ;AACvB,WAAO,OAAO,QAAQ,MAAM,OAAO,GAAG,CAAC;AACvC,WAAO,OAAO,QAAQ,MAAM,OAAO,GAAG,CAAC;AACvC,WAAO,OAAO,QAAQ,MAAM,OAAO,GAAG,CAAC;AACvC,WAAO,OAAO,QAAQ,MAAM,OAAO,GAAG,CAAC;AAEvC,WAAQ,QAAQ,IAAM,QAAQ;AAC9B,YAAS,OAAO,OAAO,IAAM,QAAQ;AACrC,YAAS,OAAO,MAAM,IAAK;AAE3B,aAAS,SAAS,OAAO,aAAa,IAAI;AAE1C,QAAI,SAAS,IAAI;AACf,eAAS,SAAS,OAAO,aAAa,IAAI;AAAA,IAC3C;AACD,QAAI,SAAS,IAAI;AACf,eAAS,SAAS,OAAO,aAAa,IAAI;AAAA,IAC3C;AAAA,EACF;AAED,SAAO,mBAAmB,OAAO,MAAM,CAAC;AAC1C;AAGA,SAAS,UAAU,OAAO;AAExB,QAAM,YAAY,MAAM,QAAQ,MAAM,GAAG,EAAE,QAAQ,MAAM,GAAG;AAC5D,QAAM,UAAU,IAAI,QAAQ,IAAI,UAAU,SAAS,KAAK,CAAC;AAEzD,MAAI;AACF,WAAO,KAAK,MAAM,aAAa,YAAY,OAAO,CAAC;AAAA,EACpD,SAAQ,GAAG;AACVH,wDAAc,YAAY,CAAC;AAC3B,WAAO;AAAA,EACR;AACH;AAGA,SAAS,kBAAkB;AACzB,QAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AACxC,MAAI,CAAC;AAAO,WAAO;AAEnB,MAAI;AAEF,UAAM,aAAa,MAAM,MAAM,GAAG;AAClC,QAAI,WAAW,WAAW,GAAG;AAC3BA,oBAAAA,MAAA,MAAA,OAAA,iBAAY,WAAW;AACvBA,oBAAG,MAAC,kBAAkB,OAAO;AAC7B,aAAO;AAAA,IACR;AAGD,UAAM,UAAU,UAAU,WAAW,CAAC,CAAC;AACvC,QAAI,CAAC,SAAS;AACZA,oBAAAA,MAAY,MAAA,OAAA,iBAAA,aAAa;AACzBA,oBAAG,MAAC,kBAAkB,OAAO;AAC7B,aAAO;AAAA,IACR;AAED,QAAI,QAAQ,OAAO,QAAQ,MAAM,KAAK,IAAK,IAAG,KAAM;AAClDA,oBAAAA,qCAAY,UAAU;AACtBA,oBAAG,MAAC,kBAAkB,OAAO;AAC7B,aAAO;AAAA,IACR;AAED,WAAO;AAAA,EACR,SAAQ,GAAG;AACVA,kBAAAA,uCAAc,aAAa,CAAC;AAC5BA,kBAAG,MAAC,kBAAkB,OAAO;AAC7B,WAAO;AAAA,EACR;AACH;AAGAA,cAAAA,MAAI,eAAe,cAAc;AAAA,EAC/B,OAAO,GAAG;AACR,WAAO,WAAW,EAAE,GAAG;AAAA,EACxB;AACH,CAAC;AAEDA,cAAAA,MAAI,eAAe,aAAa;AAAA,EAC9B,OAAO,GAAG;AACR,WAAO,WAAW,EAAE,GAAG;AAAA,EACxB;AACH,CAAC;AAEDA,cAAAA,MAAI,eAAe,YAAY;AAAA,EAC7B,OAAO,GAAG;AAER,QAAI,EAAE,QAAQ,sBAAsB;AAClC,aAAO;AAAA,IACR;AACD,WAAO,WAAW,EAAE,GAAG;AAAA,EACxB;AACH,CAAC;AAED,SAAS,WAAW,KAAK;AAEvB,QAAM,aAAa,IAAI,MAAM,GAAG,EAAE,CAAC;AACnC,MAAI,UAAU,SAAS,UAAU,GAAG;AAClC,WAAO;AAAA,EACR;AAGD,MAAI,CAAC,gBAAe,GAAI;AACtBA,kBAAAA,qCAAY,aAAa;AAKzB,WAAO;AAAA,EAcR;AAED,SAAO;AACT;;;"}