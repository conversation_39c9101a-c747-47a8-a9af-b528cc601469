<view class="page-container modern-design data-v-0225c721"><view class="{{['modern-header', 'data-v-0225c721', e && 'scrolled']}}" style="{{f}}"><view class="header-bg data-v-0225c721"><view class="bg-gradient data-v-0225c721"></view><view class="bg-particles data-v-0225c721"><view wx:for="{{a}}" wx:for-item="i" wx:key="a" class="particle data-v-0225c721" style="{{i.b}}"></view></view></view><view class="header-content data-v-0225c721"><view class="back-button data-v-0225c721" bindtap="{{b}}"><view class="back-icon data-v-0225c721"><text class="iconfont data-v-0225c721">←</text></view></view><view class="header-title-area data-v-0225c721"><text class="{{['header-title', 'data-v-0225c721', d && 'show']}}">{{c}}</text><view class="title-decoration data-v-0225c721"><view class="decoration-line data-v-0225c721"></view></view></view><view class="header-actions data-v-0225c721"><view class="action-btn share-btn data-v-0225c721"><text class="iconfont data-v-0225c721">📤</text></view></view></view></view><scroll-view class="scroll-content modern-scroll data-v-0225c721" scroll-y bindscroll="{{av}}" style="{{'padding-top:' + aw}}"><view class="{{['today-card', 'modern-card', 'data-v-0225c721', x && 'completed']}}"><view class="card-bg-effects data-v-0225c721"><view class="gradient-orb orb-1 data-v-0225c721"></view><view class="gradient-orb orb-2 data-v-0225c721"></view><view class="floating-shapes data-v-0225c721"><view wx:for="{{g}}" wx:for-item="i" wx:key="a" class="{{['shape', 'data-v-0225c721', i.b]}}"></view></view></view><view class="today-header data-v-0225c721"><view class="today-badge data-v-0225c721"><text class="badge-emoji data-v-0225c721">🔥</text><text class="badge-text data-v-0225c721">今日挑战</text></view><view class="today-date-modern data-v-0225c721"><text class="date-text data-v-0225c721">{{h}}</text><view class="{{['date-indicator', 'data-v-0225c721', i]}}"></view></view></view><view class="today-main-content data-v-0225c721"><view class="status-display data-v-0225c721"><view class="{{['status-icon', 'data-v-0225c721', k]}}"><text class="status-emoji data-v-0225c721">{{j}}</text></view><view class="status-info data-v-0225c721"><text class="status-title data-v-0225c721">{{l}}</text><text class="status-subtitle data-v-0225c721">{{m}}</text></view></view><view class="time-info-modern data-v-0225c721"><view class="time-label data-v-0225c721"><text class="time-icon data-v-0225c721">⏰</text><text class="time-text data-v-0225c721">有效时间</text></view><text class="time-range data-v-0225c721">{{n}} - {{o}}</text></view><view wx:if="{{p}}" class="checkin-result-modern data-v-0225c721"><view class="result-item data-v-0225c721"><text class="result-label data-v-0225c721">打卡时间</text><text class="result-value data-v-0225c721">{{q}}</text></view><view wx:if="{{r}}" class="result-item refund-info data-v-0225c721"><text class="result-label data-v-0225c721">退款状态</text><view class="{{['refund-status', 'data-v-0225c721', t]}}"><text class="refund-text data-v-0225c721">{{s}}</text></view></view></view></view><view wx:if="{{v}}" class="action-area data-v-0225c721"><button class="modern-checkin-btn data-v-0225c721" bindtap="{{w}}"><view class="btn-bg data-v-0225c721"><view class="btn-gradient data-v-0225c721"></view><view class="btn-shine data-v-0225c721"></view></view><view class="btn-content data-v-0225c721"><text class="btn-emoji data-v-0225c721">⚡</text><text class="btn-text data-v-0225c721">立即打卡</text></view><view class="btn-ripple data-v-0225c721"></view></button></view></view><view wx:if="{{y}}" class="daily-task-card data-v-0225c721"><view class="task-header data-v-0225c721"><view class="task-header-bg data-v-0225c721"><view class="header-wave wave1 data-v-0225c721"></view><view class="header-wave wave2 data-v-0225c721"></view></view><view class="task-header-content data-v-0225c721"><view class="day-circle data-v-0225c721"><text class="day-number data-v-0225c721">{{z}}</text><text class="day-text data-v-0225c721">DAY</text></view><view class="task-header-info data-v-0225c721"><text class="task-category data-v-0225c721">{{A}}</text><text class="task-main-title data-v-0225c721">{{B}}</text><view class="task-difficulty-indicator data-v-0225c721"><view wx:for="{{C}}" wx:for-item="star" wx:key="a" class="{{['difficulty-star', 'data-v-0225c721', star.b && 'active']}}"> ⭐ </view><text class="difficulty-text data-v-0225c721">{{D}}</text></view></view></view></view><view class="task-description-section data-v-0225c721"><view class="description-icon data-v-0225c721"><image src="{{E}}" class="icon data-v-0225c721" mode="aspectFit"></image></view><view class="description-content data-v-0225c721"><text class="description-title data-v-0225c721">今日目标</text><text class="description-text data-v-0225c721">{{F}}</text></view></view><view wx:if="{{G}}" class="task-section enhanced data-v-0225c721"><view class="section-header data-v-0225c721"><view class="section-icon-wrapper data-v-0225c721"><image src="{{H}}" class="section-icon data-v-0225c721" mode="aspectFit"></image></view><text class="section-title data-v-0225c721">任务要求</text><view class="section-badge data-v-0225c721">必读</view></view><view class="section-content data-v-0225c721">{{I}}</view></view><view wx:if="{{J}}" class="task-section enhanced data-v-0225c721"><view class="section-header data-v-0225c721"><view class="section-icon-wrapper tips data-v-0225c721"><image src="{{K}}" class="section-icon data-v-0225c721" mode="aspectFit"></image></view><text class="section-title data-v-0225c721">成长贴士</text><view class="section-badge tips data-v-0225c721">💡</view></view><view class="section-content tips data-v-0225c721">{{L}}</view></view><view wx:if="{{M}}" class="task-section enhanced data-v-0225c721"><view class="section-header data-v-0225c721"><view class="section-icon-wrapper resources data-v-0225c721"><image src="{{N}}" class="section-icon data-v-0225c721" mode="aspectFit"></image></view><text class="section-title data-v-0225c721">学习资源</text><view class="section-badge resources data-v-0225c721">{{O}}</view></view><view class="section-content resources data-v-0225c721"><view wx:for="{{P}}" wx:for-item="url" wx:key="b" class="resource-item enhanced data-v-0225c721" bindtap="{{url.c}}"><view class="resource-icon-wrapper data-v-0225c721"><image src="{{Q}}" class="resource-icon data-v-0225c721" mode="aspectFit"></image></view><view class="resource-content data-v-0225c721"><text class="resource-title data-v-0225c721">学习资源 {{url.a}}</text><text class="resource-desc data-v-0225c721">点击查看详细内容</text></view><view class="resource-arrow data-v-0225c721"><image src="{{R}}" class="arrow-icon data-v-0225c721" mode="aspectFit"></image></view></view></view></view><view class="task-status-section data-v-0225c721"><view wx:if="{{S}}" class="task-completion-status completed data-v-0225c721"><view class="completion-icon data-v-0225c721"><image src="{{T}}" class="status-icon data-v-0225c721" mode="aspectFit"></image></view><view class="completion-content data-v-0225c721"><text class="completion-title data-v-0225c721">🎉 今日任务已完成</text><text class="completion-desc data-v-0225c721">你今天表现得很棒，继续保持！</text></view></view><view wx:else class="task-motivation data-v-0225c721"><view class="motivation-icon data-v-0225c721"><image src="{{U}}" class="motivation-img data-v-0225c721" mode="aspectFit"></image></view><view class="motivation-content data-v-0225c721"><text class="motivation-title data-v-0225c721">{{V}}</text><text class="motivation-desc data-v-0225c721">{{W}}</text></view></view></view><view class="task-progress-section enhanced data-v-0225c721"><view class="progress-header data-v-0225c721"><view class="progress-title-wrapper data-v-0225c721"><image src="{{X}}" class="progress-icon data-v-0225c721" mode="aspectFit"></image><text class="progress-title data-v-0225c721">挑战进度</text></view><view class="progress-value-wrapper data-v-0225c721"><text class="progress-value data-v-0225c721">{{Y}}</text><text class="progress-separator data-v-0225c721">/</text><text class="progress-total data-v-0225c721">{{Z}}</text></view></view><view class="progress-track enhanced data-v-0225c721"><view class="progress-fill enhanced data-v-0225c721" style="{{'width:' + aa}}"><view class="progress-shine data-v-0225c721"></view></view><view class="progress-marker enhanced data-v-0225c721" style="{{'left:' + ab}}"><view class="marker-dot enhanced data-v-0225c721"><view class="marker-pulse data-v-0225c721"></view></view></view></view><view class="progress-milestones enhanced data-v-0225c721"><view wx:for="{{ac}}" wx:for-item="milestone" wx:key="b" class="{{['milestone', 'enhanced', 'data-v-0225c721', milestone.c && 'completed']}}" style="{{'left:' + milestone.d}}"><view class="milestone-dot enhanced data-v-0225c721"></view><text class="milestone-text enhanced data-v-0225c721">{{milestone.a}}</text></view></view></view></view><view class="challenge-card data-v-0225c721"><view class="challenge-header data-v-0225c721"><text class="challenge-title data-v-0225c721">{{ad}}</text><text class="challenge-period data-v-0225c721">{{ae}}</text></view><view class="progress-bar data-v-0225c721"><view class="progress-info data-v-0225c721"><text class="progress-label data-v-0225c721">挑战进度</text><text class="progress-value data-v-0225c721">{{af}}/{{ag}}天</text></view><view class="progress-track data-v-0225c721"><view class="progress-fill data-v-0225c721" style="{{'width:' + ah}}"></view></view></view></view><view class="stats-section data-v-0225c721"><text class="section-title data-v-0225c721">数据概览</text><view class="stats-grid data-v-0225c721"><view class="stats-item data-v-0225c721"><view class="stats-icon data-v-0225c721" style="background:rgba(86, 119, 252, 0.1)"><image class="data-v-0225c721" src="{{ai}}" mode="aspectFit"></image></view><text class="stats-value data-v-0225c721">{{aj}}</text><text class="stats-label data-v-0225c721">累计打卡</text></view><view class="stats-item data-v-0225c721"><view class="stats-icon data-v-0225c721" style="background:rgba(255, 107, 107, 0.1)"><image class="data-v-0225c721" src="{{ak}}" mode="aspectFit"></image></view><text class="stats-value data-v-0225c721">{{al}}</text><text class="stats-label data-v-0225c721">累计能量</text></view><view class="stats-item data-v-0225c721"><view class="stats-icon data-v-0225c721" style="background:rgba(255, 215, 0, 0.1)"><image class="data-v-0225c721" src="{{am}}" mode="aspectFit"></image></view><text class="stats-value data-v-0225c721">{{an}}</text><text class="stats-label data-v-0225c721">最长连续</text></view><view class="stats-item data-v-0225c721"><view class="stats-icon data-v-0225c721" style="background:rgba(32, 201, 151, 0.1)"><image class="data-v-0225c721" src="{{ao}}" mode="aspectFit"></image></view><text class="stats-value data-v-0225c721">{{ap}}%</text><text class="stats-label data-v-0225c721">完成率</text></view></view></view><view class="calendar-section data-v-0225c721"><text class="section-title data-v-0225c721">打卡记录</text><view class="calendar-card data-v-0225c721"><view class="weekdays data-v-0225c721"><text wx:for="{{aq}}" wx:for-item="day" wx:key="b" class="data-v-0225c721">{{day.a}}</text></view><view class="days-grid data-v-0225c721"><view wx:for="{{ar}}" wx:for-item="day" wx:key="g" class="{{['day-item', 'data-v-0225c721', day.h && 'checked', day.i && 'invalid-time', day.j && 'today', day.k && 'in-period', day.l && 'failed', day.m && 'placeholder']}}"><block wx:if="{{day.a}}"><text class="day-text data-v-0225c721">{{day.b}}</text><text wx:if="{{day.c}}" class="month-text data-v-0225c721">{{day.d}}月</text><view wx:if="{{day.e}}" class="check-mark data-v-0225c721"><image class="data-v-0225c721" src="{{day.f}}" mode="aspectFit"></image></view></block></view></view></view></view><view class="time-section data-v-0225c721"><text class="section-title data-v-0225c721">打卡时段</text><view class="time-card data-v-0225c721"><view class="time-icon data-v-0225c721"><image class="data-v-0225c721" src="{{as}}" mode="aspectFit"></image></view><view class="time-info data-v-0225c721"><text class="time-value data-v-0225c721">{{at}}</text><text class="time-label data-v-0225c721">平均打卡时间</text></view></view></view></scroll-view><view wx:if="{{ax}}" class="modal-mask data-v-0225c721"><view class="{{['modal-content', 'success-modal', 'data-v-0225c721', aD && 'warning-modal', aE && 'failed-modal']}}"><view class="success-image data-v-0225c721"><image class="data-v-0225c721" src="{{ay}}" mode="aspectFit"></image></view><view class="success-text data-v-0225c721"><text class="success-title data-v-0225c721">{{az}}</text><text class="success-desc data-v-0225c721">{{aA}}</text></view><view class="modal-footer data-v-0225c721"><button class="modal-btn data-v-0225c721" bindtap="{{aC}}">{{aB}}</button></view></view></view></view>