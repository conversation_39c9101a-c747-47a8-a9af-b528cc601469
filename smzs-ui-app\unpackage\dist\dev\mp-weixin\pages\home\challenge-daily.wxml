<view class="page-container data-v-0225c721"><custom-navbar wx:if="{{c}}" class="dynamic-navbar data-v-0225c721" style="{{'transform:' + a + ';' + ('opacity:' + b)}}" u-i="0225c721-0" bind:__l="__l" u-p="{{c}}"/><scroll-view class="scroll-content data-v-0225c721" scroll-y bindscroll="{{ap}}" style="{{'padding-top:' + '0px'}}"><view class="today-card data-v-0225c721"><view class="today-info data-v-0225c721"><view class="today-left data-v-0225c721"><text class="today-title data-v-0225c721">今日打卡</text><text class="today-date data-v-0225c721">{{d}}</text></view><view class="{{['today-status', 'data-v-0225c721', f]}}">{{e}}</view></view><view class="time-range-info data-v-0225c721"><text class="time-range-text data-v-0225c721">有效打卡时间: {{g}}-{{h}}</text></view><view wx:if="{{i}}" class="checkin-result-info data-v-0225c721"><view class="checkin-time data-v-0225c721">打卡时间: {{j}}</view><view class="energy-info data-v-0225c721"><text wx:if="{{k}}" class="refund-pending data-v-0225c721">退款待处理</text><text wx:elif="{{l}}" class="refund-done data-v-0225c721">已退款</text><text wx:elif="{{m}}" class="refund-failed data-v-0225c721">退款失败，联系客服：lovestore8</text><text wx:elif="{{n}}" class="refund-processing data-v-0225c721">退款处理中</text><text wx:elif="{{o}}" class="refund-none data-v-0225c721">无需退款</text></view></view><view wx:if="{{p}}" class="today-action data-v-0225c721"><button class="checkin-btn data-v-0225c721" bindtap="{{r}}"><image src="{{q}}" class="checkin-icon data-v-0225c721" mode="aspectFit"></image><text class="data-v-0225c721">立即打卡</text></button></view></view><view wx:if="{{s}}" class="daily-task-card data-v-0225c721"><view class="task-header data-v-0225c721"><view class="task-header-bg data-v-0225c721"><view class="header-wave wave1 data-v-0225c721"></view><view class="header-wave wave2 data-v-0225c721"></view></view><view class="task-header-content data-v-0225c721"><view class="day-circle data-v-0225c721"><text class="day-number data-v-0225c721">{{t}}</text><text class="day-text data-v-0225c721">DAY</text></view><view class="task-header-info data-v-0225c721"><text class="task-category data-v-0225c721">{{v}}</text><text class="task-main-title data-v-0225c721">{{w}}</text><view class="task-difficulty-indicator data-v-0225c721"><view wx:for="{{x}}" wx:for-item="star" wx:key="a" class="{{['difficulty-star', 'data-v-0225c721', star.b && 'active']}}"> ⭐ </view><text class="difficulty-text data-v-0225c721">{{y}}</text></view></view></view></view><view class="task-description-section data-v-0225c721"><view class="description-icon data-v-0225c721"><image src="{{z}}" class="icon data-v-0225c721" mode="aspectFit"></image></view><view class="description-content data-v-0225c721"><text class="description-title data-v-0225c721">今日目标</text><text class="description-text data-v-0225c721">{{A}}</text></view></view><view wx:if="{{B}}" class="task-section enhanced data-v-0225c721"><view class="section-header data-v-0225c721"><view class="section-icon-wrapper data-v-0225c721"><image src="{{C}}" class="section-icon data-v-0225c721" mode="aspectFit"></image></view><text class="section-title data-v-0225c721">任务要求</text><view class="section-badge data-v-0225c721">必读</view></view><view class="section-content data-v-0225c721">{{D}}</view></view><view wx:if="{{E}}" class="task-section enhanced data-v-0225c721"><view class="section-header data-v-0225c721"><view class="section-icon-wrapper tips data-v-0225c721"><image src="{{F}}" class="section-icon data-v-0225c721" mode="aspectFit"></image></view><text class="section-title data-v-0225c721">成长贴士</text><view class="section-badge tips data-v-0225c721">💡</view></view><view class="section-content tips data-v-0225c721">{{G}}</view></view><view wx:if="{{H}}" class="task-section enhanced data-v-0225c721"><view class="section-header data-v-0225c721"><view class="section-icon-wrapper resources data-v-0225c721"><image src="{{I}}" class="section-icon data-v-0225c721" mode="aspectFit"></image></view><text class="section-title data-v-0225c721">学习资源</text><view class="section-badge resources data-v-0225c721">{{J}}</view></view><view class="section-content resources data-v-0225c721"><view wx:for="{{K}}" wx:for-item="url" wx:key="b" class="resource-item enhanced data-v-0225c721" bindtap="{{url.c}}"><view class="resource-icon-wrapper data-v-0225c721"><image src="{{L}}" class="resource-icon data-v-0225c721" mode="aspectFit"></image></view><view class="resource-content data-v-0225c721"><text class="resource-title data-v-0225c721">学习资源 {{url.a}}</text><text class="resource-desc data-v-0225c721">点击查看详细内容</text></view><view class="resource-arrow data-v-0225c721"><image src="{{M}}" class="arrow-icon data-v-0225c721" mode="aspectFit"></image></view></view></view></view><view class="task-status-section data-v-0225c721"><view wx:if="{{N}}" class="task-completion-status completed data-v-0225c721"><view class="completion-icon data-v-0225c721"><image src="{{O}}" class="status-icon data-v-0225c721" mode="aspectFit"></image></view><view class="completion-content data-v-0225c721"><text class="completion-title data-v-0225c721">🎉 今日任务已完成</text><text class="completion-desc data-v-0225c721">你今天表现得很棒，继续保持！</text></view></view><view wx:else class="task-motivation data-v-0225c721"><view class="motivation-icon data-v-0225c721"><image src="{{P}}" class="motivation-img data-v-0225c721" mode="aspectFit"></image></view><view class="motivation-content data-v-0225c721"><text class="motivation-title data-v-0225c721">{{Q}}</text><text class="motivation-desc data-v-0225c721">{{R}}</text></view></view></view><view class="task-progress-section enhanced data-v-0225c721"><view class="progress-header data-v-0225c721"><view class="progress-title-wrapper data-v-0225c721"><image src="{{S}}" class="progress-icon data-v-0225c721" mode="aspectFit"></image><text class="progress-title data-v-0225c721">挑战进度</text></view><view class="progress-value-wrapper data-v-0225c721"><text class="progress-value data-v-0225c721">{{T}}</text><text class="progress-separator data-v-0225c721">/</text><text class="progress-total data-v-0225c721">{{U}}</text></view></view><view class="progress-track enhanced data-v-0225c721"><view class="progress-fill enhanced data-v-0225c721" style="{{'width:' + V}}"><view class="progress-shine data-v-0225c721"></view></view><view class="progress-marker enhanced data-v-0225c721" style="{{'left:' + W}}"><view class="marker-dot enhanced data-v-0225c721"><view class="marker-pulse data-v-0225c721"></view></view></view></view><view class="progress-milestones enhanced data-v-0225c721"><view wx:for="{{X}}" wx:for-item="milestone" wx:key="b" class="{{['milestone', 'enhanced', 'data-v-0225c721', milestone.c && 'completed']}}" style="{{'left:' + milestone.d}}"><view class="milestone-dot enhanced data-v-0225c721"></view><text class="milestone-text enhanced data-v-0225c721">{{milestone.a}}</text></view></view></view></view><view class="challenge-card data-v-0225c721"><view class="challenge-header data-v-0225c721"><text class="challenge-title data-v-0225c721">{{Y}}</text><text class="challenge-period data-v-0225c721">{{Z}}</text></view><view class="progress-bar data-v-0225c721"><view class="progress-info data-v-0225c721"><text class="progress-label data-v-0225c721">挑战进度</text><text class="progress-value data-v-0225c721">{{aa}}/{{ab}}天</text></view><view class="progress-track data-v-0225c721"><view class="progress-fill data-v-0225c721" style="{{'width:' + ac}}"></view></view></view></view><view class="stats-section data-v-0225c721"><text class="section-title data-v-0225c721">数据概览</text><view class="stats-grid data-v-0225c721"><view class="stats-item data-v-0225c721"><view class="stats-icon data-v-0225c721" style="background:rgba(86, 119, 252, 0.1)"><image class="data-v-0225c721" src="{{ad}}" mode="aspectFit"></image></view><text class="stats-value data-v-0225c721">{{ae}}</text><text class="stats-label data-v-0225c721">累计打卡</text></view><view class="stats-item data-v-0225c721"><view class="stats-icon data-v-0225c721" style="background:rgba(255, 107, 107, 0.1)"><image class="data-v-0225c721" src="{{af}}" mode="aspectFit"></image></view><text class="stats-value data-v-0225c721">{{ag}}</text><text class="stats-label data-v-0225c721">累计能量</text></view><view class="stats-item data-v-0225c721"><view class="stats-icon data-v-0225c721" style="background:rgba(255, 215, 0, 0.1)"><image class="data-v-0225c721" src="{{ah}}" mode="aspectFit"></image></view><text class="stats-value data-v-0225c721">{{ai}}</text><text class="stats-label data-v-0225c721">最长连续</text></view><view class="stats-item data-v-0225c721"><view class="stats-icon data-v-0225c721" style="background:rgba(32, 201, 151, 0.1)"><image class="data-v-0225c721" src="{{aj}}" mode="aspectFit"></image></view><text class="stats-value data-v-0225c721">{{ak}}%</text><text class="stats-label data-v-0225c721">完成率</text></view></view></view><view class="calendar-section data-v-0225c721"><text class="section-title data-v-0225c721">打卡记录</text><view class="calendar-card data-v-0225c721"><view class="weekdays data-v-0225c721"><text wx:for="{{al}}" wx:for-item="day" wx:key="b" class="data-v-0225c721">{{day.a}}</text></view><view class="days-grid data-v-0225c721"><view wx:for="{{am}}" wx:for-item="day" wx:key="g" class="{{['day-item', 'data-v-0225c721', day.h && 'checked', day.i && 'invalid-time', day.j && 'today', day.k && 'in-period', day.l && 'failed', day.m && 'placeholder']}}"><block wx:if="{{day.a}}"><text class="day-text data-v-0225c721">{{day.b}}</text><text wx:if="{{day.c}}" class="month-text data-v-0225c721">{{day.d}}月</text><view wx:if="{{day.e}}" class="check-mark data-v-0225c721"><image class="data-v-0225c721" src="{{day.f}}" mode="aspectFit"></image></view></block></view></view></view></view><view class="time-section data-v-0225c721"><text class="section-title data-v-0225c721">打卡时段</text><view class="time-card data-v-0225c721"><view class="time-icon data-v-0225c721"><image class="data-v-0225c721" src="{{an}}" mode="aspectFit"></image></view><view class="time-info data-v-0225c721"><text class="time-value data-v-0225c721">{{ao}}</text><text class="time-label data-v-0225c721">平均打卡时间</text></view></view></view></scroll-view><view wx:if="{{aq}}" class="modal-mask data-v-0225c721"><view class="{{['modal-content', 'success-modal', 'data-v-0225c721', ax && 'warning-modal', ay && 'failed-modal']}}"><view class="success-image data-v-0225c721"><image class="data-v-0225c721" src="{{ar}}" mode="aspectFit"></image></view><view class="success-text data-v-0225c721"><text class="success-title data-v-0225c721">{{as}}</text><text class="success-desc data-v-0225c721">{{at}}</text></view><view class="modal-footer data-v-0225c721"><button class="modal-btn data-v-0225c721" bindtap="{{aw}}">{{av}}</button></view></view></view></view>