
.wallet-page {
  min-height: 100vh;
  background-color: #f8f9fa;
}
.content {
  padding: 24rpx;
}
.balance-card {
  margin-top: 80rpx;
  padding: 32rpx;
  background: linear-gradient(135deg, #FFD700, #ecd659);
  border-radius: 24rpx;
  color: #000000;
}
.balance-header {
  margin-bottom: 32rpx;
}
.balance-label {
  font-size: 28rpx;
  opacity: 0.9;
}
.balance-value {
  display: block;
  font-size: 64rpx;
  font-weight: bold;
  font-family: DIN;
  margin-top: 8rpx;
}
.balance-grid {
  display: flex;
  justify-content: space-between;
  margin-top: 32rpx;
  padding-top: 32rpx;
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
}
.grid-item {
  flex: 1;
  text-align: center;
}
.item-label {
  font-size: 24rpx;
  opacity: 0.9;
}
.item-value {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-top: 8rpx;
}
.action-buttons {
  display: flex;
  gap: 24rpx;
  margin-top: 24rpx;
}
.action-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  border: none;
}
.action-btn::after {
  border: none;
}
.withdraw-btn {
  background: #ecd659;
  color: #000000;
}
.detail-btn {
  background: #f5f5f5;
  color: #333;
}
.btn-icon {
  width: 36rpx;
  height: 36rpx;
}
.transaction-list {
  margin-top: 24rpx;
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
}
.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}
.header-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.header-more {
  font-size: 28rpx;
  color: #666;
}
.empty-tip {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 48rpx 0;
}
.transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}
.transaction-item:last-child {
  border-bottom: none;
}
.item-left {
  flex: 1;
}
.item-title {
  font-size: 30rpx;
  color: #333;
}
.item-time {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}
.item-amount {
  font-size: 32rpx;
  font-weight: bold;
  font-family: DIN;
}
.income {
  color: #4CAF50;
}
.expense {
  color: #FF5722;
}
.withdraw-popup {
  width: 600rpx;
  background: #fff;
  border-radius: 24rpx;
}
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f5f5f5;
}
.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.popup-close {
  font-size: 48rpx;
  color: #999;
  line-height: 1;
}
.popup-content {
  padding: 32rpx;
}
.amount-input {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}
.currency {
  font-size: 40rpx;
  color: #333;
  margin-right: 16rpx;
}
.amount-field {
  flex: 1;
  font-size: 40rpx;
  color: #333;
}
.available-balance {
  font-size: 24rpx;
  color: #999;
  margin-top: 16rpx;
}
.confirm-btn {
  margin-top: 32rpx;
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background: #4CAF50;
  color: #fff;
  border-radius: 44rpx;
  font-size: 32rpx;
  border: none;
}
.confirm-btn::after {
  border: none;
}
.confirm-btn[disabled] {
  background: #ccc !important;
  color: #fff !important;
  border: none !important;
  outline: none !important;
}
