"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const CustomNavBar = () => "../../components/custom-navbar/index.js";
const _sfc_main = {
  components: {
    CustomNavBar
  },
  data() {
    return {
      navHeight: 64,
      // 导航栏高度（状态栏 + 44）
      scrollHeight: 0,
      // 滚动区域高度
      filterTypes: [
        { label: "全部", value: "" },
        { label: "收入", value: "income" },
        { label: "支出", value: "expense" }
      ],
      currentType: "",
      // 当前筛选类型
      list: [],
      // 交易列表
      loading: false,
      hasMore: true,
      pageNum: 1,
      pageSize: 20,
      scrollTop: 0,
      touchStartY: 0
    };
  },
  created() {
    const systemInfo = common_vendor.wx$1.getSystemInfoSync();
    this.navHeight = systemInfo.statusBarHeight + 44;
    this.scrollHeight = systemInfo.windowHeight - this.navHeight - 100;
    this.getTransactionList();
  },
  methods: {
    // 获取交易记录
    async getTransactionList(isLoadMore = false) {
      if (this.loading)
        return;
      try {
        this.loading = true;
        const params = {
          pageNum: this.pageNum,
          pageSize: this.pageSize
        };
        if (this.currentType) {
          params.transactionType = this.currentType;
        }
        const res = await api_index.getFundFlowList(params);
        if (res.code === 200) {
          const { list, total } = res.data;
          const formattedList = list.map((item) => ({
            id: item.flowId,
            title: item.remark || "交易",
            amount: item.amount,
            type: item.transactionType,
            // 直接使用后端返回的 transactionType
            status: item.status,
            createTime: this.formatDate(item.createTime)
          }));
          if (isLoadMore) {
            this.list = [...this.list, ...formattedList];
          } else {
            this.list = formattedList;
          }
          this.hasMore = this.list.length < total;
        }
      } catch (error) {
        common_vendor.wx$1.showToast({
          title: error.message || "获取交易记录失败",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    // 处理滚动
    handleScroll(e) {
      const touch = e.touches[0];
      if (!this.touchStartY) {
        this.touchStartY = touch.clientY;
        return;
      }
      const deltaY = touch.clientY - this.touchStartY;
      this.scrollTop -= deltaY;
      this.touchStartY = touch.clientY;
      const element = e.currentTarget;
      if (element.scrollHeight - element.scrollTop <= element.clientHeight + 50) {
        this.loadMore();
      }
    },
    // 处理筛选变化
    handleFilterChange(type) {
      if (this.currentType === type)
        return;
      this.currentType = type;
      this.pageNum = 1;
      this.hasMore = true;
      this.list = [];
      this.getTransactionList();
    },
    // 加载更多
    loadMore() {
      if (!this.hasMore || this.loading)
        return;
      this.pageNum++;
      this.getTransactionList(true);
    },
    // 格式化金额
    formatAmount(amount) {
      return (amount / 100).toFixed(2);
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        "0": "处理中",
        "1": "已完成",
        "2": "已取消"
      };
      return statusMap[status] || status;
    },
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr)
        return "";
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hour = String(date.getHours()).padStart(2, "0");
      const minute = String(date.getMinutes()).padStart(2, "0");
      return `${year}-${month}-${day} ${hour}:${minute}`;
    }
  }
};
if (!Array) {
  const _component_custom_nav_bar = common_vendor.resolveComponent("custom-nav-bar");
  _component_custom_nav_bar();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      title: "交易明细"
    }),
    b: common_vendor.f($data.filterTypes, (item, index, i0) => {
      return {
        a: common_vendor.t(item.label),
        b: index,
        c: common_vendor.n({
          active: $data.currentType === item.value
        }),
        d: common_vendor.o(($event) => $options.handleFilterChange(item.value), index)
      };
    }),
    c: $data.list.length === 0
  }, $data.list.length === 0 ? {} : {
    d: common_vendor.f($data.list, (item, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.title),
        b: common_vendor.t(item.createTime),
        c: common_vendor.t(item.type === "income" ? "+" : "-"),
        d: common_vendor.t($options.formatAmount(item.amount)),
        e: common_vendor.n(item.type === "income" ? "income" : "expense"),
        f: item.status
      }, item.status ? {
        g: common_vendor.t($options.getStatusText(item.status))
      } : {}, {
        h: item.id
      });
    })
  }, {
    e: $data.loading
  }, $data.loading ? {} : !$data.hasMore ? {} : {}, {
    f: !$data.hasMore,
    g: $data.scrollHeight + "px",
    h: common_vendor.o((...args) => $options.handleScroll && $options.handleScroll(...args)),
    i: $data.navHeight + "px"
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/profile/wallet-detail.js.map
