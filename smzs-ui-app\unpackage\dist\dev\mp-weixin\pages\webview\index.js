"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const CustomNavbar = () => "../../components/custom-navbar/index.js";
const _sfc_main = {
  components: {
    CustomNavbar
  },
  data() {
    return {
      url: "",
      pageTitle: "资源浏览",
      showError: false,
      errorMessage: "无法加载网页，请检查网络连接或稍后再试"
    };
  },
  onLoad(options) {
    if (options.url) {
      try {
        this.url = decodeURIComponent(options.url);
        const urlObj = new URL(this.url);
        this.pageTitle = urlObj.hostname;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/webview/index.vue:56", "解析URL失败:", error);
        this.showError = true;
        this.errorMessage = "无效的URL地址";
      }
    } else {
      this.showError = true;
      this.errorMessage = "未提供URL";
    }
    if (options.title) {
      this.pageTitle = decodeURIComponent(options.title);
    }
  },
  methods: {
    handleMessage(event) {
      common_vendor.index.__f__("log", "at pages/webview/index.vue:73", "WebView消息:", event);
    },
    handleError(error) {
      common_vendor.index.__f__("error", "at pages/webview/index.vue:77", "WebView错误:", error);
      this.showError = true;
    },
    reload() {
      if (this.url) {
        this.showError = false;
        const currentUrl = this.url;
        this.url = "";
        setTimeout(() => {
          this.url = currentUrl;
        }, 100);
      }
    },
    goBack() {
      common_vendor.index.navigateBack();
    }
  }
};
if (!Array) {
  const _component_custom_navbar = common_vendor.resolveComponent("custom-navbar");
  _component_custom_navbar();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      title: $data.pageTitle,
      ["show-back"]: true,
      shape: "rounded",
      ["border-radius"]: 10,
      gradient: false,
      light: false
    }),
    b: $data.url,
    c: common_vendor.o((...args) => $options.handleMessage && $options.handleMessage(...args)),
    d: common_vendor.o((...args) => $options.handleError && $options.handleError(...args)),
    e: $data.showError
  }, $data.showError ? {
    f: common_assets._imports_0$4,
    g: common_vendor.t($data.errorMessage),
    h: common_vendor.o((...args) => $options.reload && $options.reload(...args)),
    i: common_vendor.o((...args) => $options.goBack && $options.goBack(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-a96d96f3"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/webview/index.js.map
