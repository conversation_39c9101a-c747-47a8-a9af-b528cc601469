
.agreement-page {
  min-height: 100vh;
  background-color: #FFFFFF;
  padding: 30rpx;
}
.agreement-content {
  max-width: 900rpx;
  margin: 0 auto;
  padding-bottom: 60rpx;
}
.header {
  text-align: center;
  margin-bottom: 40rpx;
}
.title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16rpx;
}
.update-time {
  font-size: 24rpx;
  color: #999999;
}
.intro-text {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  text-align: justify;
  margin-bottom: 50rpx;
  padding: 30rpx;
  background: #FFF9F0;
  border-radius: 16rpx;
}
.section {
  margin-bottom: 50rpx;
}
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  font-size: 34rpx;
  font-weight: 600;
  color: #333333;
}
.title-num {
  color: #E6A65D;
  margin-right: 16rpx;
}
.paragraph {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 24rpx;
  text-align: justify;
}
.feature-list {
  padding: 20rpx 0;
}
.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #666666;
}
.dot {
  width: 12rpx;
  height: 12rpx;
  background: #E6A65D;
  border-radius: 50%;
  margin-right: 16rpx;
  flex-shrink: 0;
}
.rule-list {
  padding: 20rpx 0;
}
.rule-item {
  margin-bottom: 30rpx;
}
.rule-title {
  display: block;
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 12rpx;
}
.rule-content {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
}
.contact-section {
  margin-top: 60rpx;
  padding: 40rpx;
  background: #F8F8F8;
  border-radius: 16rpx;
}
.contact-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
}
.contact-text {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 20rpx;
}
.contact-info {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 12rpx;
}
