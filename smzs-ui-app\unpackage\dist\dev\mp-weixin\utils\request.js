"use strict";
const common_vendor = require("../common/vendor.js");
const config_index = require("../config/index.js");
const baseUrl = config_index.config.baseUrl;
let lastLoginAttempt = 0;
let isLoggingIn = false;
const request = (options) => {
  options.header = options.header || {};
  options.header["Content-Type"] = "application/json";
  const token = common_vendor.index.getStorageSync("token");
  if (token) {
    options.header["Authorization"] = `Bearer ${token}`;
  }
  if (options.method === "GET" && options.params) {
    const queryString = Object.keys(options.params).filter((key) => options.params[key] !== void 0 && options.params[key] !== "").map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(options.params[key])}`).join("&");
    if (queryString) {
      options.url += (options.url.includes("?") ? "&" : "?") + queryString;
    }
    delete options.params;
  }
  options.url = baseUrl + options.url;
  common_vendor.index.__f__("log", "at utils/request.js:41", "Request:", {
    url: options.url,
    method: options.method,
    headers: options.header,
    data: options.data
  });
  return new Promise((resolve, reject) => {
    common_vendor.index.request({
      ...options,
      success: (res) => {
        if (res.statusCode === 401 || res.data && res.data.code === 401) {
          common_vendor.index.__f__("log", "at utils/request.js:54", "Token已过期");
          common_vendor.index.removeStorageSync("token");
          const now = Date.now();
          if (!isLoggingIn && now - lastLoginAttempt > 1e4) {
            isLoggingIn = true;
            lastLoginAttempt = now;
            silentLogin().then(() => {
              isLoggingIn = false;
              request(options).then(resolve).catch(reject);
            }).catch(() => {
              isLoggingIn = false;
              reject(new Error("登录失败"));
            });
            return;
          } else {
            reject(new Error("正在尝试重新登录，请稍后再试"));
            return;
          }
        }
        resolve(res.data);
      },
      fail: (err) => {
        common_vendor.index.__f__("error", "at utils/request.js:97", "Request failed:", err);
        reject(err);
      }
    });
  });
};
const silentLogin = () => {
  return new Promise((resolve, reject) => {
    common_vendor.index.login({
      provider: "weixin",
      success: (loginRes) => {
        if (loginRes.code) {
          common_vendor.index.request({
            url: baseUrl + "/portal/member/wx-login",
            method: "POST",
            data: {
              code: loginRes.code,
              source: "miniprogram",
              silent: true
            },
            success: (res) => {
              if (res.statusCode === 200 && res.data && res.data.code === 200 && res.data.data && res.data.data.token) {
                common_vendor.index.setStorageSync("token", res.data.data.token);
                common_vendor.index.__f__("log", "at utils/request.js:124", "自动续期token成功");
                const currentPages = getCurrentPages();
                const currentPage = currentPages[currentPages.length - 1];
                if (currentPage && currentPage.onLoad) {
                  currentPage.onLoad(currentPage.options || {});
                }
                resolve();
              } else {
                common_vendor.index.__f__("error", "at utils/request.js:134", "自动续期token响应异常:", res);
                reject(new Error("登录响应异常"));
              }
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at utils/request.js:139", "自动续期token请求失败:", err);
              reject(err);
            }
          });
        } else {
          common_vendor.index.__f__("error", "at utils/request.js:144", "自动续期获取code失败");
          reject(new Error("获取登录凭证失败"));
        }
      },
      fail: (err) => {
        common_vendor.index.__f__("error", "at utils/request.js:149", "微信登录失败:", err);
        reject(err);
      }
    });
  });
};
exports.request = request;
exports.silentLogin = silentLogin;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/request.js.map
