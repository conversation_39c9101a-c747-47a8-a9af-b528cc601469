"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const common_assets = require("../../common/assets.js");
const CustomNavBar = () => "../../components/custom-navbar/index.js";
const CustomPopup = () => "../../components/CustomPopup.js";
const _sfc_main = {
  components: {
    CustomNavBar,
    CustomPopup
  },
  data() {
    return {
      navHeight: 64,
      // 导航栏高度（状态栏 + 44）
      feedbackVisible: false,
      logoutVisible: false,
      feedbackContent: "",
      contactInfo: "",
      isMpWeixin: false
    };
  },
  created() {
    this.isMpWeixin = true;
    const systemInfo = common_vendor.index.getSystemInfoSync();
    this.navHeight = systemInfo.statusBarHeight + 44;
  },
  methods: {
    // 显示意见反馈弹窗
    showFeedbackPopup() {
      this.feedbackVisible = true;
    },
    // 关闭意见反馈弹窗
    closeFeedbackPopup() {
      this.feedbackVisible = false;
      this.feedbackContent = "";
      this.contactInfo = "";
    },
    // 提交反馈
    async submitFeedback() {
      if (!this.feedbackContent)
        return;
      try {
        await api_index.createFeedback({
          content: this.feedbackContent,
          contactInfo: this.contactInfo
        });
        common_vendor.index.showToast({
          title: "提交成功",
          icon: "success"
        });
        this.closeFeedbackPopup();
      } catch (error) {
        common_vendor.index.showToast({
          title: error.message || "提交失败",
          icon: "none"
        });
      }
    },
    // 检查更新
    async checkForUpdate() {
      try {
        const res = await api_index.checkUpdate();
        common_vendor.index.showToast({
          title: res.message,
          icon: "none"
        });
        if (res.hasUpdate) {
          const updateManager = common_vendor.index.getUpdateManager();
          updateManager.applyUpdate();
        }
      } catch (error) {
        common_vendor.index.showToast({
          title: "检查更新失败",
          icon: "none"
        });
      }
    },
    // 打开用户协议
    openUserAgreement() {
      common_vendor.index.navigateTo({
        url: "/pages/terms/user"
      });
    },
    // 打开隐私政策
    openPrivacyPolicy() {
      common_vendor.index.navigateTo({
        url: "/pages/terms/privacy"
      });
    },
    // 显示退出确认弹窗
    handleLogout() {
      this.logoutVisible = true;
    },
    // 关闭退出确认弹窗
    closeLogoutPopup() {
      this.logoutVisible = false;
    },
    // 确认退出
    confirmLogout() {
      common_vendor.index.clearStorageSync();
      common_vendor.index.switchTab({
        url: "/pages/home/<USER>"
      });
    }
  }
};
if (!Array) {
  const _component_custom_nav_bar = common_vendor.resolveComponent("custom-nav-bar");
  const _component_custom_popup = common_vendor.resolveComponent("custom-popup");
  (_component_custom_nav_bar + _component_custom_popup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.p({
      title: "设置"
    }),
    b: common_assets._imports_0$2,
    c: common_vendor.o((...args) => $options.showFeedbackPopup && $options.showFeedbackPopup(...args)),
    d: common_assets._imports_0$2,
    e: common_vendor.o((...args) => $options.checkForUpdate && $options.checkForUpdate(...args)),
    f: common_assets._imports_0$2,
    g: common_vendor.o((...args) => $options.openUserAgreement && $options.openUserAgreement(...args)),
    h: common_assets._imports_0$2,
    i: common_vendor.o((...args) => $options.openPrivacyPolicy && $options.openPrivacyPolicy(...args)),
    j: $data.navHeight + "px",
    k: common_vendor.o((...args) => $options.closeFeedbackPopup && $options.closeFeedbackPopup(...args)),
    l: $data.feedbackContent,
    m: common_vendor.o(($event) => $data.feedbackContent = $event.detail.value),
    n: $data.contactInfo,
    o: common_vendor.o(($event) => $data.contactInfo = $event.detail.value),
    p: !$data.feedbackContent,
    q: common_vendor.o((...args) => $options.submitFeedback && $options.submitFeedback(...args)),
    r: common_vendor.o(($event) => $data.feedbackVisible = $event),
    s: common_vendor.p({
      position: "center",
      visible: $data.feedbackVisible
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/profile/settings.js.map
