"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const common_assets = require("../../common/assets.js");
const CustomNavBar = () => "../../components/custom-navbar/index.js";
const CustomPopup = () => "../../components/CustomPopup.js";
const _sfc_main = {
  components: {
    CustomNavBar,
    CustomPopup
  },
  data() {
    return {
      navHeight: 64,
      // 导航栏高度（状态栏 + 44）
      walletInfo: {
        balance: 0,
        frozenBalance: 0,
        totalIncome: 0,
        energyCoin: 0
      },
      transactionList: [],
      total: 0,
      pageNum: 1,
      pageSize: 10,
      withdrawAmount: "",
      isLoading: false,
      isWithdrawPopupVisible: false
    };
  },
  computed: {
    canWithdraw() {
      const amount = parseFloat(this.withdrawAmount);
      return amount > 0 && amount <= this.walletInfo.balance / 100;
    }
  },
  onShow() {
    this.loadWalletInfo();
    this.loadTransactionList();
  },
  methods: {
    async loadWalletInfo() {
      try {
        const res = await api_index.getWalletInfo();
        if (res.code === 200) {
          this.walletInfo = res.data;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/profile/wallet.vue:153", "获取钱包信息失败:", error);
        common_vendor.index.showToast({
          title: "获取钱包信息失败",
          icon: "none"
        });
      }
    },
    async loadTransactionList() {
      try {
        const res = await api_index.getTransactionList({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          transactionType: ""
        });
        if (res.code === 200) {
          this.transactionList = (res.data.list || []).map((item) => ({
            id: item.flowId,
            title: item.remark || "交易",
            amount: item.amount,
            transactionType: item.transactionType === "expense" ? "expense" : "income",
            createTime: this.formatDate(item.createTime)
          }));
          this.total = res.data.total;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/profile/wallet.vue:179", "获取交易记录失败:", error);
        common_vendor.index.showToast({
          title: "获取交易记录失败",
          icon: "none"
        });
      }
    },
    formatDate(dateStr) {
      if (!dateStr)
        return "";
      const date = new Date(dateStr);
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hour = String(date.getHours()).padStart(2, "0");
      const minute = String(date.getMinutes()).padStart(2, "0");
      return `${month}-${day} ${hour}:${minute}`;
    },
    showWithdrawPopup() {
      this.isWithdrawPopupVisible = true;
    },
    hideWithdraw() {
      this.isWithdrawPopupVisible = false;
      this.withdrawAmount = "";
    },
    async handleWithdraw() {
      if (!this.withdrawAmount) {
        common_vendor.index.showToast({
          title: "请输入提现金额",
          icon: "none"
        });
        return;
      }
      const amount = parseFloat(this.withdrawAmount) * 100;
      if (amount < 100) {
        common_vendor.index.showToast({
          title: "提现金额不能小于1元",
          icon: "none"
        });
        return;
      }
      if (amount > this.walletInfo.balance) {
        common_vendor.index.showToast({
          title: "余额不足",
          icon: "none"
        });
        return;
      }
      try {
        this.isLoading = true;
        const res = await api_index.createWithdraw({
          amount,
          payChannel: "wxpay",
          remark: "用户提现"
        });
        if (res.code === 200) {
          common_vendor.index.showToast({
            title: "提现申请已提交",
            icon: "success"
          });
          this.hideWithdraw();
          this.loadWalletInfo();
          this.loadTransactionList();
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/profile/wallet.vue:250", "提现失败:", error);
        common_vendor.index.showToast({
          title: error.message || "提现失败",
          icon: "none"
        });
      } finally {
        this.isLoading = false;
      }
    },
    navigateToDetail() {
      common_vendor.index.navigateTo({
        url: "/pages/profile/wallet-detail"
      });
    },
    formatAmount(amount) {
      return (amount / 100).toFixed(2);
    }
  }
};
if (!Array) {
  const _component_custom_nav_bar = common_vendor.resolveComponent("custom-nav-bar");
  const _component_custom_popup = common_vendor.resolveComponent("custom-popup");
  (_component_custom_nav_bar + _component_custom_popup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      title: "我的钱包"
    }),
    b: common_vendor.t($options.formatAmount($data.walletInfo.balance)),
    c: common_assets._imports_0$6,
    d: common_vendor.o((...args) => $options.showWithdrawPopup && $options.showWithdrawPopup(...args)),
    e: common_assets._imports_1$5,
    f: common_vendor.o((...args) => $options.navigateToDetail && $options.navigateToDetail(...args)),
    g: common_vendor.o((...args) => $options.navigateToDetail && $options.navigateToDetail(...args)),
    h: $data.transactionList.length === 0
  }, $data.transactionList.length === 0 ? {} : {
    i: common_vendor.f($data.transactionList, (item, k0, i0) => {
      return {
        a: common_vendor.t(item.title),
        b: common_vendor.t(item.createTime),
        c: common_vendor.t(item.transactionType === "income" ? "+" : "-"),
        d: common_vendor.t($options.formatAmount(item.amount)),
        e: common_vendor.n(item.transactionType === "income" ? "income" : "expense"),
        f: item.id
      };
    })
  }, {
    j: $data.navHeight + "px",
    k: common_vendor.o((...args) => $options.hideWithdraw && $options.hideWithdraw(...args)),
    l: $data.withdrawAmount,
    m: common_vendor.o(($event) => $data.withdrawAmount = $event.detail.value),
    n: common_vendor.t($options.formatAmount($data.walletInfo.balance)),
    o: !$options.canWithdraw,
    p: common_vendor.o((...args) => $options.handleWithdraw && $options.handleWithdraw(...args)),
    q: common_vendor.o(($event) => $data.isWithdrawPopupVisible = $event),
    r: common_vendor.p({
      position: "center",
      visible: $data.isWithdrawPopupVisible
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/profile/wallet.js.map
