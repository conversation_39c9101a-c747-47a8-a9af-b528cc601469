<template>
  <view class="page-container">
    <!-- 使用自定义导航栏 - 实现类似Keep的下拉显示效果 -->
    <custom-navbar
      :title="showNavTitle ? '挑战详情' : ''"
      :show-back="true"
      :background="showNavTitle ? '#ffffff' : 'transparent'"
      :title-color="showNavTitle ? '#333' : '#fff'"
      :back-icon-color="showNavTitle ? '#333' : '#fff'"
      :back-text-color="showNavTitle ? '#333' : '#fff'"
      :border="showNavTitle"
      :blur="!showNavTitle"
      :shadow="showNavTitle"
      class="dynamic-navbar"
      :style="{
        transform: `translateY(${navbarTransform}px)`,
        opacity: navbarOpacity
      }"
    />

    <!-- 页面内容区 -->
    <scroll-view
      class="scroll-content"
      scroll-y
      @scroll="handleScroll"
      :style="{ paddingTop: '0px' }"
    >
      <!-- 今日打卡卡片 -->
      <view class="today-card">
        <view class="today-info">
          <view class="today-left">
            <text class="today-title">今日打卡</text>
            <text class="today-date">{{ formatToday() }}</text>
          </view>
          <view class="today-status" :class="getTodayStatusClass">
            {{ getTodayStatusText }}
          </view>
        </view>
        <view class="time-range-info">
          <text class="time-range-text">有效打卡时间: {{ formatTime(todayStatus?.validTimeRange?.startTime || challenge.startTime) }}-{{ formatTime(todayStatus?.validTimeRange?.endTime || challenge.endTime) }}</text>
        </view>
        <view v-if="todayStatus && todayStatus.record" class="checkin-result-info">
          <view class="checkin-time">打卡时间: {{ formatDateTime(todayStatus.record.checkinTime) }}</view>
          <!-- <view class="checkin-status" :class="{'valid': todayStatus.record.isValidTime, 'invalid': !todayStatus.record.isValidTime}">
            {{ todayStatus.record.isValidTime ? '有效打卡' : '无效打卡' }}
          </view> -->
          <view class="energy-info">
            <!-- <text>能量值: {{ todayStatus.record.energyValue }}</text> -->
            <text v-if="todayStatus.record.refundStatus === '0'" class="refund-pending">退款待处理</text>
            <text v-else-if="todayStatus.record.refundStatus === '1'" class="refund-done">已退款</text>
            <text v-else-if="todayStatus.record.refundStatus === '2'" class="refund-failed">退款失败，联系客服：lovestore8</text>
            <text v-else-if="todayStatus.record.refundStatus === '3'" class="refund-processing">退款处理中</text>
            <text v-else-if="todayStatus.record.refundStatus === '4'" class="refund-none">无需退款</text>
          </view>
        </view>
        <view v-if="!isTodayCompleted && challenge.status === '0'" class="today-action">
          <button class="checkin-btn" @click="handleCheckin">
            <image src="/static/images/common/checkin.png" class="checkin-icon" mode="aspectFit"></image>
            <text>立即打卡</text>
          </button>
        </view>
      </view>

      <!-- 今日任务详情卡片，仅当挑战有每日任务时显示 -->
      <view v-if="challenge.hasDailyTasks && dailyTask" class="daily-task-card">
        <!-- 任务头部 -->
        <view class="task-header">
          <view class="task-header-bg">
            <view class="header-wave wave1"></view>
            <view class="header-wave wave2"></view>
          </view>
          <view class="task-header-content">
            <view class="day-circle">
              <text class="day-number">{{ currentDayNumber }}</text>
              <text class="day-text">DAY</text>
            </view>
            <view class="task-header-info">
              <text class="task-category">{{ getTaskCategory(dailyTask.task) }}</text>
              <text class="task-main-title">{{ dailyTask.task.title }}</text>
              <view class="task-difficulty-indicator">
                <view
                  v-for="star in 3"
                  :key="star"
                  class="difficulty-star"
                  :class="{ 'active': star <= getTaskDifficulty(currentDayNumber) }"
                >
                  ⭐
                </view>
                <text class="difficulty-text">{{ getDifficultyText(currentDayNumber) }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 任务描述 -->
        <view class="task-description-section">
          <view class="description-icon">
            <image src="/static/images/task/description.png" class="icon" mode="aspectFit"></image>
          </view>
          <view class="description-content">
            <text class="description-title">今日目标</text>
            <text class="description-text">{{ dailyTask.task.description }}</text>
          </view>
        </view>

        <!-- 任务要求 -->
        <view v-if="dailyTask.task.taskRequirements" class="task-section enhanced">
          <view class="section-header">
            <view class="section-icon-wrapper">
              <image src="/static/images/task/check-list.png" class="section-icon" mode="aspectFit"></image>
            </view>
            <text class="section-title">任务要求</text>
            <view class="section-badge">必读</view>
          </view>
          <view class="section-content">
            {{ dailyTask.task.taskRequirements }}
          </view>
        </view>

        <!-- 小贴士 -->
        <view v-if="dailyTask.task.tips" class="task-section enhanced">
          <view class="section-header">
            <view class="section-icon-wrapper tips">
              <image src="/static/images/task/tips.png" class="section-icon" mode="aspectFit"></image>
            </view>
            <text class="section-title">成长贴士</text>
            <view class="section-badge tips">💡</view>
          </view>
          <view class="section-content tips">
            {{ dailyTask.task.tips }}
          </view>
        </view>

        <!-- 相关资源 -->
        <view v-if="dailyTask.task.resourceUrls" class="task-section enhanced">
          <view class="section-header">
            <view class="section-icon-wrapper resources">
              <image src="/static/images/task/resources.png" class="section-icon" mode="aspectFit"></image>
            </view>
            <text class="section-title">学习资源</text>
            <view class="section-badge resources">{{ formatResourceUrls(dailyTask.task.resourceUrls).length }}</view>
          </view>
          <view class="section-content resources">
            <view v-for="(url, index) in formatResourceUrls(dailyTask.task.resourceUrls)"
                  :key="index"
                  class="resource-item enhanced"
                  @tap="openResource(url)">
              <view class="resource-icon-wrapper">
                <image src="/static/images/task/link.png" class="resource-icon" mode="aspectFit"></image>
              </view>
              <view class="resource-content">
                <text class="resource-title">学习资源 {{ index + 1 }}</text>
                <text class="resource-desc">点击查看详细内容</text>
              </view>
              <view class="resource-arrow">
                <image src="/static/images/challenge/arrow-right.png" class="arrow-icon" mode="aspectFit"></image>
              </view>
            </view>
          </view>
        </view>

        <!-- 完成状态或激励信息 -->
        <view class="task-status-section">
          <view v-if="dailyTask.isCompleted" class="task-completion-status completed">
            <view class="completion-icon">
              <image src="/static/images/challenge/check-circle.png" class="status-icon" mode="aspectFit"></image>
            </view>
            <view class="completion-content">
              <text class="completion-title">🎉 今日任务已完成</text>
              <text class="completion-desc">你今天表现得很棒，继续保持！</text>
            </view>
          </view>
          <view v-else class="task-motivation">
            <view class="motivation-icon">
              <image src="/static/images/challenge/rocket.png" class="motivation-img" mode="aspectFit"></image>
            </view>
            <view class="motivation-content">
              <text class="motivation-title">{{ getMotivationTitle(currentDayNumber) }}</text>
              <text class="motivation-desc">{{ getMotivationDesc(currentDayNumber) }}</text>
            </view>
          </view>
        </view>

        <!-- 进度指示器 - 优化样式 -->
        <view class="task-progress-section enhanced">
          <view class="progress-header">
            <view class="progress-title-wrapper">
              <image src="/static/images/challenge/progress.png" class="progress-icon" mode="aspectFit"></image>
              <text class="progress-title">挑战进度</text>
            </view>
            <view class="progress-value-wrapper">
              <text class="progress-value">{{ currentDayNumber }}</text>
              <text class="progress-separator">/</text>
              <text class="progress-total">{{ challenge.durationDays || 21 }}</text>
            </view>
          </view>
          <view class="progress-track enhanced">
            <view class="progress-fill enhanced" :style="{ width: taskProgressWidth }">
              <view class="progress-shine"></view>
            </view>
            <view class="progress-marker enhanced" :style="{ left: taskProgressWidth }">
              <view class="marker-dot enhanced">
                <view class="marker-pulse"></view>
              </view>
            </view>
          </view>
          <view class="progress-milestones enhanced">
            <view
              v-for="milestone in getProgressMilestones()"
              :key="milestone.day"
              class="milestone enhanced"
              :class="{ 'completed': currentDayNumber >= milestone.day }"
              :style="{ left: milestone.position }"
            >
              <view class="milestone-dot enhanced"></view>
              <text class="milestone-text enhanced">{{ milestone.text }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 挑战信息卡片 -->
      <view class="challenge-card">
        <view class="challenge-header">
          <text class="challenge-title">{{ challenge.title }}</text>
          <text class="challenge-period">{{ formatDateRange(challenge.startDate, challenge.endDate) }}</text>
        </view>
        <view class="progress-bar">
          <view class="progress-info">
            <text class="progress-label">挑战进度</text>
            <text class="progress-value">{{ stats.keepDays }}/{{ challenge.ruleDays }}天</text>
          </view>
          <view class="progress-track">
            <view class="progress-fill" :style="{ width: progressWidth }"></view>
          </view>
        </view>
      </view>

      <!-- 统计数据卡片 -->
      <view class="stats-section">
        <text class="section-title">数据概览</text>
        <view class="stats-grid">
          <view class="stats-item">
            <view class="stats-icon" style="background: rgba(86, 119, 252, 0.1)">
              <image src="/static/images/challenge/leiji.png" mode="aspectFit"></image>
            </view>
            <text class="stats-value">{{ stats.keepDays }}</text>
            <text class="stats-label">累计打卡</text>
          </view>
          <view class="stats-item">
            <view class="stats-icon" style="background: rgba(255, 107, 107, 0.1)">
              <image src="/static/images/challenge/fire.png" mode="aspectFit"></image>
            </view>
            <text class="stats-value">{{ stats.totalEnergy }}</text>
            <text class="stats-label">累计能量</text>
          </view>
          <view class="stats-item">
            <view class="stats-icon" style="background: rgba(255, 215, 0, 0.1)">
              <image src="/static/images/challenge/medal.png" mode="aspectFit"></image>
            </view>
            <text class="stats-value">{{ stats.maxContinuousDays }}</text>
            <text class="stats-label">最长连续</text>
          </view>
          <view class="stats-item">
            <view class="stats-icon" style="background: rgba(32, 201, 151, 0.1)">
              <image src="/static/images/challenge/star.png" mode="aspectFit"></image>
            </view>
            <text class="stats-value">{{ (stats.completionRate * 100).toFixed(1) }}%</text>
            <text class="stats-label">完成率</text>
          </view>
        </view>
      </view>

      <!-- 打卡记录日历 -->
      <view class="calendar-section">
        <text class="section-title">打卡记录</text>
        <view class="calendar-card">
          <view class="weekdays">
            <text v-for="day in ['日', '一', '二', '三', '四', '五', '六']" :key="day">{{ day }}</text>
          </view>
          <view class="days-grid">
            <view 
              v-for="(day, index) in periodDays" 
              :key="index"
              class="day-item"
              :class="{
                'checked': day.checked,
                'invalid-time': day.invalidTime,
                'today': day.isToday,
                'in-period': day.inPeriod,
                'failed': day.failed,
                'placeholder': day.isPlaceholder
              }"
            >
              <template v-if="!day.isPlaceholder">
                <text class="day-text">{{ day.date }}</text>
                <text class="month-text" v-if="day.showMonth">{{ day.month }}月</text>
                <view v-if="day.checked" class="check-mark">
                  <image src="/static/images/common/check.png" mode="aspectFit"></image>
                </view>
              </template>
            </view>
          </view>
        </view>
      </view>

      <!-- 平均打卡时段 -->
      <view class="time-section">
        <text class="section-title">打卡时段</text>
        <view class="time-card">
          <view class="time-icon">
            <image src="/static/images/challenge/clock.png" mode="aspectFit"></image>
          </view>
          <view class="time-info">
            <text class="time-value">{{ stats.averageCheckinTime || '暂无数据' }}</text>
            <text class="time-label">平均打卡时间</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 打卡弹窗 使用自定义模态框 -->
    <view v-if="checkinPopup" class="modal-mask">
      <view class="modal-content success-modal" :class="{'warning-modal': !isValidTime, 'failed-modal': todayStatus && todayStatus.record && todayStatus.record.checkinStatus === '2'}">
        <view class="success-image">
          <image :src="getModalIcon()" mode="aspectFit"></image>
        </view>
        <view class="success-text">
          <text class="success-title">{{ getModalTitle() }}</text>
          <text class="success-desc">{{ getModalDescription() }}</text>
        </view>
        <view class="modal-footer">
          <button class="modal-btn" @click="closeCheckinPopup">{{ isValidTime ? '我还能更狠一点' : '发誓不再废' }}</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import CustomNavbar from '@/components/custom-navbar/index.vue'
import { getChallengeStats, checkInTask, getTodayTaskStatus, getChallengeTask } from '@/api/index'

export default {
  components: {
    CustomNavbar
  },

  data() {
    return {
      statusBarHeight: 20,
      challenge: {},
      stats: {
        keepDays: 0,
        completionRate: 0,
        totalEnergy: 0,
        maxContinuousDays: 0,
        averageCheckinTime: ''
      },
      selectedDates: [],
      checkinRemark: '',
      isTodayCompleted: false,
      todayStatus: null,
      currentMonth: '',
      calendarDays: [],
      isLoading: true,
      checkinPopup: false,
      isValidTime: true,
      dailyTask: null,
      currentDayNumber: 0,
      // 导航栏控制相关
      showNavTitle: false,
      lastScrollTop: 0,
      scrollDirection: 'up',
      navbarTransform: 0,
      navbarOpacity: 1
    }
  },

  computed: {
    getTodayStatusText() {
      if (this.isLoading) return ''
      if (this.challenge.status !== '0') return '已结束'
      if (!this.todayStatus || !this.todayStatus.record) return '待完成'
      if (this.todayStatus.record && this.todayStatus.record.checkinStatus === '1') {
        return this.todayStatus.record.isValidTime ? '挑战成功' : '挑战失败'
      } else if (this.todayStatus.record && this.todayStatus.record.checkinStatus === '2') {
        return '挑战失败'
      } else {
        return '未完成'
      }
    },

    getTodayStatusClass() {
      if (this.isLoading) return ''
      if (this.challenge.status !== '0') return 'status-ended'
      if (!this.todayStatus) return 'status-pending'
      if (this.todayStatus.record && this.todayStatus.record.checkinStatus === '1') {
        return this.todayStatus.record.isValidTime ? 'status-completed' : 'status-failed'
      } else if (this.todayStatus.record && this.todayStatus.record.checkinStatus === '2') {
        return 'status-failed'
      } else {
        return 'status-failed'
      }
    },

    progressWidth() {
      if (!this.challenge.ruleDays) return '0%'
      return `${(this.stats.keepDays / this.challenge.ruleDays * 100).toFixed(1)}%`
    },

    periodDays() {
      if (!this.challenge.startDate || !this.challenge.endDate) return []
      
      const startDate = new Date(this.challenge.startDate)
      const endDate = new Date(this.challenge.endDate)
      const days = []
      
      // 计算开始日期是星期几 (0-6, 0表示周日)
      const startDayOfWeek = startDate.getDay()
      
      // 添加前面的空白占位符
      for (let i = 0; i < startDayOfWeek; i++) {
        days.push({
          date: '',
          month: 0,
          showMonth: false,
          checked: false,
          isToday: false,
          inPeriod: false,
          failed: false,
          isPlaceholder: true
        })
      }
      
      let currentDate = new Date(startDate)
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      
      while (currentDate <= endDate) {
        const isStartMonth = currentDate.getDate() === 1 || 
                            currentDate.getTime() === startDate.getTime()
        
        const isFailed = currentDate < today && 
                        !this.isDateChecked(currentDate) && 
                        this.challenge.status === '0'
        
        days.push({
          date: currentDate.getDate(),
          month: currentDate.getMonth() + 1,
          showMonth: isStartMonth,
          checked: this.isDateChecked(currentDate),
          isToday: currentDate.toDateString() === today.toDateString(),
          inPeriod: true,
          failed: isFailed,
          fullDate: new Date(currentDate),
          dayOfWeek: currentDate.getDay()  // 添加星期几信息 (0-6)
        })
        
        currentDate.setDate(currentDate.getDate() + 1)
      }
      
      return days
    },

    // 计算任务进度宽度
    taskProgressWidth() {
      const totalDays = this.challenge.durationDays || 21
      const progress = (this.currentDayNumber / totalDays) * 100
      return `${Math.min(progress, 100)}%`
    }
  },

  onPullDownRefresh() {
    this.loadData().then(() => {
      uni.stopPullDownRefresh();
    }).catch(() => {
      uni.stopPullDownRefresh();
    });
  },

  onLoad(options) {
    // 获取状态栏高度
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight

    if (options.selectedCard) {
      this.challenge = JSON.parse(decodeURIComponent(options.selectedCard))
      this.loadData()
      this.initCalendar()
    }
  },

  methods: {
    goBack() {
      uni.navigateBack()
    },

    // 处理滚动事件 - 实现类似Keep的导航栏效果
    handleScroll(e) {
      const scrollTop = e.detail.scrollTop
      const threshold = 100 // 滚动阈值

      // 控制标题显示
      this.showNavTitle = scrollTop > threshold

      // 检测滚动方向
      const isScrollingDown = scrollTop > this.lastScrollTop
      this.scrollDirection = isScrollingDown ? 'down' : 'up'

      // 控制导航栏的显示/隐藏
      if (isScrollingDown && scrollTop > threshold) {
        // 向下滚动且超过阈值时隐藏导航栏
        this.navbarTransform = -60
        this.navbarOpacity = 0
      } else if (!isScrollingDown || scrollTop <= threshold) {
        // 向上滚动或未超过阈值时显示导航栏
        this.navbarTransform = 0
        this.navbarOpacity = 1
      }

      this.lastScrollTop = scrollTop
    },

    async loadData() {
      try {
        this.isLoading = true
        uni.showLoading({ title: '加载中...' })
        
        // 获取今日状态
        const statusRes = await getTodayTaskStatus(this.challenge.participationId)
        if (statusRes.code === 200) {
          this.todayStatus = statusRes.data
          this.isTodayCompleted = this.todayStatus?.record
        }

        // 获取统计数据
        const statsRes = await getChallengeStats(this.challenge.participationId)
        if (statsRes.code === 200) {
          this.stats = statsRes.data
          this.updateCalendarCheckedDays(this.stats.checkinDates)
        }

        // 如果挑战有每日任务，获取当天任务详情
        if (this.challenge.hasDailyTasks) {
          // 计算当前是参与挑战后的第几天
          const today = new Date()
          const startDate = new Date(this.challenge.startDate)
          const dayDiff = Math.floor((today - startDate) / (1000 * 60 * 60 * 24)) + 1
          this.currentDayNumber = Math.min(Math.max(1, dayDiff), 21) // 确保在1-21天范围内
          
          try {
            const taskRes = await getChallengeTask(this.challenge.participationId, this.currentDayNumber)
            if (taskRes.code === 200) {
              this.dailyTask = taskRes.data
            }
          } catch (error) {
            console.error('获取每日任务失败:', error)
          }
        }

        uni.hideLoading()
        this.isLoading = false
      } catch (error) {
        uni.hideLoading()
        this.isLoading = false
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    },

    formatToday() {
      const today = new Date()
      return `${today.getMonth() + 1}月${today.getDate()}日`
    },

    formatDateRange(start, end) {
      if (!start || !end) return ''
      const startDate = new Date(start)
      const endDate = new Date(end)
      return `${startDate.getMonth() + 1}月${startDate.getDate()}日 - ${endDate.getMonth() + 1}月${endDate.getDate()}日`
    },

    formatDateTime(time) {
      if (!time) return '--:--'
      const date = new Date(time)
      return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
    },

    formatTime(dateStr) {
        if (!dateStr) return '00:00';
        const timeStr = dateStr.split(' ')[1] || dateStr;
        const timeParts = timeStr.split(':');
        if (timeParts.length >= 2) {
            return `${timeParts[0]}:${timeParts[1]}`;
        }
        return timeStr;
    },

    initCalendar() {
      const startDate = new Date(this.challenge.startDate)
      const endDate = new Date(this.challenge.endDate)
      const now = new Date()
      
      // 默认显示当前月份,如果当前月不在挑战周期内,则显示开始月
      let currentDate = now
      if (now < startDate || now > endDate) {
        currentDate = startDate
      }
      
      this.currentMonth = `${currentDate.getFullYear()}年${currentDate.getMonth() + 1}月`
      this.generateCalendarDays(currentDate)
    },

    generateCalendarDays(date) {
      const year = date.getFullYear()
      const month = date.getMonth()
      const firstDay = new Date(year, month, 1)
      const lastDay = new Date(year, month + 1, 0)
      
      const startDate = new Date(this.challenge.startDate)
      const endDate = new Date(this.challenge.endDate)
      
      const days = []
      const startOffset = firstDay.getDay()
      
      // 上个月的天数
      for (let i = startOffset - 1; i >= 0; i--) {
        const prevDate = new Date(year, month, -i)
        days.push({
          date: prevDate.getDate(),
          currentMonth: false,
          checked: false,
          isToday: false,
          fullDate: prevDate,
          inRange: prevDate >= startDate && prevDate <= endDate
        })
      }
      
      // 当前月的天数
      const today = new Date()
      for (let i = 1; i <= lastDay.getDate(); i++) {
        const currentDate = new Date(year, month, i)
        days.push({
          date: i,
          currentMonth: true,
          checked: false,
          isToday: currentDate.toDateString() === today.toDateString(),
          fullDate: currentDate,
          inRange: currentDate >= startDate && currentDate <= endDate
        })
      }
      
      // 下个月的天数
      const remainingDays = 42 - days.length
      for (let i = 1; i <= remainingDays; i++) {
        const nextDate = new Date(year, month + 1, i)
        days.push({
          date: i,
          currentMonth: false,
          checked: false,
          isToday: false,
          fullDate: nextDate,
          inRange: nextDate >= startDate && nextDate <= endDate
        })
      }
      
      this.calendarDays = days
    },

    updateCalendarCheckedDays(checkedDates) {
      if (!checkedDates || !checkedDates.length) return
      
      if (this.stats.dailyRecords && this.stats.dailyRecords.length > 0) {
        this.stats.dailyRecords.forEach(daily => {
          const checkedDate = new Date(daily.checkinDate)
          const dayIndex = this.calendarDays.findIndex(day => 
            day.fullDate.toDateString() === checkedDate.toDateString()
          )
          if (dayIndex !== -1) {
            this.calendarDays[dayIndex].checked = true
            this.calendarDays[dayIndex].invalidTime = !daily.isValidTime
          }
        })
      } else {
        // 向后兼容，没有详细记录的情况
        checkedDates.forEach(dateStr => {
          const checkedDate = new Date(dateStr)
          const dayIndex = this.calendarDays.findIndex(day => 
            day.fullDate.toDateString() === checkedDate.toDateString()
          )
          if (dayIndex !== -1) {
            this.calendarDays[dayIndex].checked = true
          }
        })
      }
    },

    prevMonth() {
      const [year, month] = this.currentMonth.replace(/[年月]/g, '-').split('-')
      const prevDate = new Date(parseInt(year), parseInt(month) - 2)
      
      // 检查是否超出挑战周期
      const startDate = new Date(this.challenge.startDate)
      if (prevDate < new Date(startDate.getFullYear(), startDate.getMonth(), 1)) {
        return
      }
      
      this.currentMonth = `${prevDate.getFullYear()}年${prevDate.getMonth() + 1}月`
      this.generateCalendarDays(prevDate)
      this.updateCalendarCheckedDays(this.stats.checkinDates)
    },

    nextMonth() {
      const [year, month] = this.currentMonth.replace(/[年月]/g, '-').split('-')
      const nextDate = new Date(parseInt(year), parseInt(month))
      
      // 检查是否超出挑战周期
      const endDate = new Date(this.challenge.endDate)
      if (nextDate > new Date(endDate.getFullYear(), endDate.getMonth(), 1)) {
        return
      }
      
      this.currentMonth = `${nextDate.getFullYear()}年${nextDate.getMonth() + 1}月`
      this.generateCalendarDays(nextDate)
      this.updateCalendarCheckedDays(this.stats.checkinDates)
    },

    async handleCheckin() {
      if (this.challenge.status !== '0') {
        uni.showToast({
          title: '任务已结束',
          icon: 'none'
        })
        return
      }
      if (this.isTodayCompleted) {
        uni.showToast({
          title: '今日已完成打卡',
          icon: 'none'
        })
        return
      }

      try {
        uni.showLoading({ title: '打卡中...' })
        const res = await checkInTask({
          participationId: this.challenge.participationId,
          remark: ''
        })

        uni.hideLoading()
        if (res.code === 200) {
          this.isTodayCompleted = true
          this.isValidTime = res.data.isValidTime || false
          await this.loadData()
          this.checkinPopup = true
        } else {
          throw new Error(res.msg)
        }
      } catch (error) {
        console.log('打卡错误:', error)
        uni.hideLoading()
        
        // 获取错误信息（兼容多种格式）
        let errorMsg = ''
        if (error.message) {
          errorMsg = error.message
        } else if (typeof error === 'string') {
          errorMsg = error
        } else if (error.msg) {
          errorMsg = error.msg
        } else {
          errorMsg = '打卡失败'
        }
        
        uni.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 3000
        })
      }
    },

    closeCheckinPopup() {
      this.checkinPopup = false
    },

    isDateChecked(date) {
      if (!this.stats.checkinDates) return false
      return this.stats.checkinDates.some(checkedDate => 
        new Date(checkedDate).toDateString() === date.toDateString()
      )
    },

    getModalIcon() {
      if (this.todayStatus && this.todayStatus.record && this.todayStatus.record.checkinStatus === '2') {
        return 'https://cos.ap-shanghai.myqcloud.com/foolishsister-1257000723/smzs/challenge/checkin-warning.png'
      } else if (this.todayStatus && this.todayStatus.record && this.todayStatus.record.checkinStatus === '1') {
        return this.isValidTime ? 'https://cos.ap-shanghai.myqcloud.com/foolishsister-1257000723/smzs/challenge/checkin-success.png' : 'https://cos.ap-shanghai.myqcloud.com/foolishsister-1257000723/smzs/challenge/checkin-warning.png'
      }
      return 'https://cos.ap-shanghai.myqcloud.com/foolishsister-1257000723/smzs/challenge/checkin-warning.png'
    },

    getModalTitle() {
      if (this.todayStatus && this.todayStatus.record && this.todayStatus.record.checkinStatus === '2') {
        return '非有效时间打卡'
      } else if (this.todayStatus && this.todayStatus.record && this.todayStatus.record.checkinStatus === '1') {
        return this.isValidTime ? '打卡成功！' : '非有效时间打卡'
      }
      return '非有效时间打卡'
    },

    getModalDescription() {
      const timeRange = `${this.formatTime(this.todayStatus?.validTimeRange?.startTime || this.challenge.startTime)}-${this.formatTime(this.todayStatus?.validTimeRange?.endTime || this.challenge.endTime)}`;
      if (this.todayStatus && this.todayStatus.record && this.todayStatus.record.checkinStatus === '2') {
        return `"错过${timeRange}，米没了！你连这都坚持不了还想翻身？开玩笑的，别哭，下次别摆烂了"`
      } else if (this.todayStatus && this.todayStatus.record && this.todayStatus.record.checkinStatus === '1') {
        return this.isValidTime ? '"你干得不错，比昨天的你强了不少"' : `"错过${timeRange}，米没了！你连这都坚持不了还想翻身？开玩笑的，别哭，下次别摆烂了"`
      }
      return `"错过${timeRange}，米没了！你连这都坚持不了还想翻身？开玩笑的，别哭，下次别摆烂了"`
    },

    formatResourceUrls(urls) {
      if (!urls) return [];
      
      // 如果urls是字符串，尝试解析为数组
      let urlArray = urls;
      if (typeof urls === 'string') {
        try {
          // 尝试解析为JSON
          urlArray = JSON.parse(urls);
        } catch (e) {
          // 如果不是合法的JSON，可能是以逗号分隔的字符串
          urlArray = urls.split(',');
        }
      }
      
      if (!Array.isArray(urlArray)) {
        urlArray = [urlArray.toString()];
      }
      
      return urlArray;
    },

    openResource(url) {
      // 检查URL是否有http/https前缀，如果没有则添加
      let formattedUrl = url;
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        formattedUrl = 'https://' + url;
      }

      uni.navigateTo({
        url: `/pages/webview/index?url=${encodeURIComponent(formattedUrl)}`
      });
    },

    // 获取任务分类
    getTaskCategory(task) {
      if (!task || !task.title) return '成长任务'

      const title = task.title.toLowerCase()
      if (title.includes('冥想') || title.includes('meditation')) return '冥想修行'
      if (title.includes('运动') || title.includes('exercise')) return '运动健身'
      if (title.includes('阅读') || title.includes('reading')) return '知识学习'
      if (title.includes('反思') || title.includes('reflection')) return '自我反思'
      if (title.includes('写作') || title.includes('writing')) return '创作表达'
      if (title.includes('社交') || title.includes('social')) return '人际交往'

      return '成长任务'
    },

    // 获取任务难度
    getTaskDifficulty(dayNumber) {
      if (dayNumber <= 7) return 1 // 第一周：简单
      if (dayNumber <= 14) return 2 // 第二周：中等
      return 3 // 第三周：困难
    },

    // 获取难度文本
    getDifficultyText(dayNumber) {
      const difficulty = this.getTaskDifficulty(dayNumber)
      const texts = ['', '入门', '进阶', '挑战']
      return texts[difficulty] || '入门'
    },

    // 获取激励标题
    getMotivationTitle(dayNumber) {
      const titles = [
        '开始你的成长之旅！',
        '坚持就是胜利！',
        '你已经走了很远！',
        '继续保持这个节奏！',
        '你正在变得更好！',
        '每一天都是新的开始！',
        '第一周即将完成！',
        '恭喜完成第一周！',
        '进入第二阶段！',
        '你的坚持令人敬佩！',
        '已经过半了！',
        '你比想象中更强大！',
        '距离目标越来越近！',
        '第二周即将完成！',
        '进入最后冲刺阶段！',
        '你已经证明了自己！',
        '坚持到现在真不容易！',
        '胜利就在眼前！',
        '最后几天了！',
        '明天就是最后一天！',
        '恭喜完成21天挑战！'
      ]
      return titles[dayNumber - 1] || '继续加油！'
    },

    // 获取激励描述
    getMotivationDesc(dayNumber) {
      if (dayNumber <= 7) {
        return '新习惯的养成需要时间，每一天的坚持都在为未来的自己投资。'
      } else if (dayNumber <= 14) {
        return '你已经度过了最困难的适应期，现在是建立稳定习惯的关键时期。'
      } else {
        return '你即将完成这个了不起的挑战，这份坚持将成为你人生的宝贵财富。'
      }
    },

    // 获取进度里程碑
    getProgressMilestones() {
      const totalDays = this.challenge.durationDays || 21
      return [
        { day: 1, text: '开始', position: '0%' },
        { day: Math.floor(totalDays / 3), text: '1/3', position: '33.33%' },
        { day: Math.floor(totalDays * 2 / 3), text: '2/3', position: '66.66%' },
        { day: totalDays, text: '完成', position: '100%' }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background: #FAFAFA;
  position: relative;

  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 40vh;
    background: linear-gradient(135deg, #FFC72C 0%, #FFB300 50%, #f7c85d 100%);
    border-bottom-left-radius: 40rpx;
    border-bottom-right-radius: 40rpx;
    z-index: 0;
  }
}

/* 动态导航栏样式 */
.dynamic-navbar {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);

  &.transparent {
    background: rgba(255, 255, 255, 0.1) !important;
  }
}

.scroll-content {
  height: 100vh;
  box-sizing: border-box;
  padding: 20rpx;
  padding-top: calc(var(--status-bar-height, 20px) + 88rpx + 20rpx);
  position: relative;
  z-index: 1;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

.today-card {
  background: #fff;
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(255, 199, 44, 0.15);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);

  /* 添加微妙的渐变背景 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 199, 44, 0.02) 0%, rgba(255, 179, 0, 0.05) 100%);
    z-index: 0;
  }

  .today-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    position: relative;
    z-index: 1;

    .today-left {
      .today-title {
        font-size: 40rpx;
        font-weight: 600;
        background: linear-gradient(135deg, #FFC72C, #FFB300);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 12rpx;
        display: block;
      }

      .today-date {
        font-size: 28rpx;
        color: #666;
      }
    }

    .today-status {
      padding: 12rpx 24rpx;
      border-radius: 30rpx;
      font-size: 26rpx;

      &.status-completed {
        background: rgba(39, 174, 96, 0.1);
        color: #27AE60;
        border: 1px solid rgba(39, 174, 96, 0.2);
      }

      &.status-failed {
        background: rgba(235, 87, 87, 0.1);
        color: #EB5757;
        border: 1px solid rgba(235, 87, 87, 0.2);
      }

      &.status-pending {
        background: rgba(255, 159, 28, 0.1);
        color: #FF9F1C;
        border: 1px solid rgba(255, 159, 28, 0.2);
      }

      &.status-ended {
        background: rgba(153, 153, 153, 0.1);
        color: #999;
        border: 1px solid rgba(153, 153, 153, 0.2);
      }
    }
  }

  .time-range-info {
    margin-bottom: 20rpx;
    position: relative;
    z-index: 1;

    .time-range-text {
      font-size: 26rpx;
      color: #666;
    }
  }
  
  .checkin-result-info {
    margin-bottom: 30rpx;
    padding: 20rpx;
    background: #F9F9F9;
    border-radius: 16rpx;
    font-size: 26rpx;
    
    .checkin-time {
      margin-bottom: 10rpx;
      color: #333;
    }
    
    .checkin-status {
      display: inline-block;
      padding: 4rpx 16rpx;
      border-radius: 20rpx;
      margin-bottom: 10rpx;
      font-size: 24rpx;
      
      &.valid {
        background: rgba(39, 174, 96, 0.1);
        color: #27AE60;
      }
      
      &.invalid {
        background: rgba(235, 87, 87, 0.1);
        color: #EB5757;
      }
    }
    
    .energy-info {
      display: flex;
      justify-content: space-between;
      color: #666;
      
      .refund-pending {
        color: #FF9F1C;
      }
      
      .refund-done {
        color: #27AE60;
      }
      
      .refund-failed {
        color: #EB5757;
      }
      
      .refund-processing {
        color: #2F80ED;
      }
      
      .refund-none {
        color: #999;
      }
    }
  }

  .today-action {
    position: relative;
    z-index: 1;
    
    .checkin-btn {
      width: 100%;
      height: 96rpx;
      background: linear-gradient(135deg, #FFC72C, #FFB300);
      border-radius: 48rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;
      color: #fff;
      border: none;
      box-shadow: 0 4rpx 12rpx rgba(255, 199, 44, 0.15);
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
        box-shadow: 0 2rpx 8rpx rgba(255, 199, 44, 0.1);
      }

      .checkin-icon {
        width: 48rpx;
        height: 48rpx;
        margin-right: 16rpx;
      }
    }
    .checkin-btn::after {
      border: none !important;
    }
  }
}

.daily-task-card {
  background: #fff;
  border-radius: 32rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 199, 44, 0.15);
  overflow: hidden;
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);

  /* 添加微妙的动画效果 */
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  &:hover {
    transform: translateY(-4rpx);
    box-shadow: 0 12rpx 40rpx rgba(255, 199, 44, 0.2);
  }

  /* 任务头部样式 */
  .task-header {
    position: relative;
    padding: 40rpx;
    background: linear-gradient(135deg, #FFC72C 0%, #FFB300 100%);
    overflow: hidden;

    .task-header-bg {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;

      .header-wave {
        position: absolute;
        width: 200%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);

        &.wave1 {
          top: -50%;
          left: -50%;
          border-radius: 45%;
          animation: wave1 6s ease-in-out infinite;
        }

        &.wave2 {
          top: -30%;
          right: -50%;
          border-radius: 40%;
          animation: wave2 8s ease-in-out infinite reverse;
        }
      }
    }

    .task-header-content {
      position: relative;
      z-index: 2;
      display: flex;
      align-items: center;

      .day-circle {
        width: 120rpx;
        height: 120rpx;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 60rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-right: 24rpx;
        backdrop-filter: blur(10rpx);
        border: 2rpx solid rgba(255, 255, 255, 0.3);

        .day-number {
          font-size: 48rpx;
          font-weight: 700;
          color: #fff;
          line-height: 1;
        }

        .day-text {
          font-size: 20rpx;
          color: rgba(255, 255, 255, 0.8);
          font-weight: 500;
          margin-top: 4rpx;
        }
      }

      .task-header-info {
        flex: 1;

        .task-category {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.8);
          margin-bottom: 8rpx;
          display: block;
        }

        .task-main-title {
          font-size: 36rpx;
          font-weight: 700;
          color: #fff;
          margin-bottom: 12rpx;
          display: block;
          line-height: 1.3;
        }

        .task-difficulty-indicator {
          display: flex;
          align-items: center;

          .difficulty-star {
            font-size: 20rpx;
            margin-right: 4rpx;
            opacity: 0.4;
            transition: opacity 0.3s ease;

            &.active {
              opacity: 1;
            }
          }

          .difficulty-text {
            font-size: 22rpx;
            color: rgba(255, 255, 255, 0.9);
            margin-left: 8rpx;
            font-weight: 500;
          }
        }
      }
    }
  }

  /* 任务描述区域 */
  .task-description-section {
    padding: 32rpx 40rpx;
    display: flex;
    align-items: flex-start;

    .description-icon {
      width: 48rpx;
      height: 48rpx;
      margin-right: 16rpx;
      margin-top: 4rpx;

      .icon {
        width: 100%;
        height: 100%;
      }
    }

    .description-content {
      flex: 1;

      .description-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 12rpx;
        display: block;
      }

      .description-text {
        font-size: 26rpx;
        color: #666;
        line-height: 1.6;
        display: block;
      }
    }
  }

  .task-section {
    margin-bottom: 20rpx;

    &.enhanced {
      padding: 32rpx 40rpx;
      margin-bottom: 24rpx;
      border-radius: 24rpx;
      background: linear-gradient(135deg, rgba(255, 199, 44, 0.02) 0%, rgba(255, 179, 0, 0.05) 100%);
      border: 1rpx solid rgba(255, 199, 44, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2rpx);
        box-shadow: 0 8rpx 24rpx rgba(255, 199, 44, 0.1);
      }
    }

    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16rpx;

      .section-icon-wrapper {
        display: flex;
        align-items: center;
        padding: 12rpx;
        border-radius: 16rpx;
        background: rgba(255, 199, 44, 0.1);
        margin-right: 16rpx;

        &.tips {
          background: rgba(52, 152, 219, 0.1);
        }

        &.resources {
          background: rgba(155, 89, 182, 0.1);
        }
      }

      .section-icon {
        width: 32rpx;
        height: 32rpx;
      }

      .section-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
        flex: 1;
      }

      .section-badge {
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 20rpx;
        font-weight: 500;
        background: rgba(255, 199, 44, 0.2);
        color: #FFC72C;

        &.tips {
          background: rgba(52, 152, 219, 0.2);
          color: #3498DB;
        }

        &.resources {
          background: rgba(155, 89, 182, 0.2);
          color: #9B59B6;
        }
      }
    }

    .section-content {
      font-size: 26rpx;
      color: #333;
      line-height: 1.6;

      &.tips {
        background: rgba(52, 152, 219, 0.05);
        padding: 24rpx;
        border-radius: 16rpx;
        border-left: 4rpx solid #3498DB;
      }

      &.resources {
        display: flex;
        flex-direction: column;
        gap: 16rpx;
      }
    }
  }

  .task-completion-status {
    display: flex;
    align-items: center;
    padding: 12rpx 24rpx;
    border-radius: 20rpx;
    background: rgba(39, 174, 96, 0.1);
    color: #27AE60;
    margin-top: 20rpx;

    .status-icon {
      width: 48rpx;
      height: 48rpx;
      margin-right: 12rpx;
    }

    text {
      font-size: 26rpx;
      font-weight: 500;
    }
  }

  /* 增强的进度指示器样式 */
  .task-progress-section.enhanced {
    padding: 32rpx 40rpx;
    background: linear-gradient(135deg, rgba(255, 199, 44, 0.02) 0%, rgba(255, 179, 0, 0.05) 100%);
    border-radius: 24rpx;
    border: 1rpx solid rgba(255, 199, 44, 0.1);

    .progress-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24rpx;

      .progress-title-wrapper {
        display: flex;
        align-items: center;

        .progress-icon {
          width: 32rpx;
          height: 32rpx;
          margin-right: 12rpx;
        }

        .progress-title {
          font-size: 28rpx;
          font-weight: 600;
          color: #333;
        }
      }

      .progress-value-wrapper {
        display: flex;
        align-items: baseline;

        .progress-value {
          font-size: 36rpx;
          font-weight: 700;
          color: #FFC72C;
        }

        .progress-separator {
          font-size: 24rpx;
          color: #999;
          margin: 0 8rpx;
        }

        .progress-total {
          font-size: 28rpx;
          color: #666;
        }
      }
    }

    .progress-track.enhanced {
      height: 16rpx;
      background: rgba(255, 199, 44, 0.1);
      border-radius: 8rpx;
      position: relative;
      overflow: hidden;

      .progress-fill.enhanced {
        height: 100%;
        background: linear-gradient(90deg, #FFC72C 0%, #FFB300 50%, #FFA000 100%);
        border-radius: 8rpx;
        position: relative;
        transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);

        .progress-shine {
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);
          animation: shine 2s infinite;
        }
      }

      .progress-marker.enhanced {
        position: absolute;
        top: 50%;
        transform: translate(-50%, -50%);
        transition: left 0.8s cubic-bezier(0.4, 0, 0.2, 1);

        .marker-dot.enhanced {
          width: 24rpx;
          height: 24rpx;
          background: #FFC72C;
          border-radius: 50%;
          border: 4rpx solid #fff;
          box-shadow: 0 4rpx 12rpx rgba(255, 199, 44, 0.3);
          position: relative;

          .marker-pulse {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            height: 100%;
            background: rgba(255, 199, 44, 0.3);
            border-radius: 50%;
            animation: pulse 2s infinite;
          }
        }
      }
    }

    .progress-milestones.enhanced {
      display: flex;
      justify-content: space-between;
      margin-top: 16rpx;
      position: relative;

      .milestone.enhanced {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;

        &.completed {
          .milestone-dot.enhanced {
            background: #FFC72C;
            border-color: #FFB300;
          }

          .milestone-text.enhanced {
            color: #FFC72C;
            font-weight: 600;
          }
        }

        .milestone-dot.enhanced {
          width: 16rpx;
          height: 16rpx;
          background: #E0E0E0;
          border: 2rpx solid #BDBDBD;
          border-radius: 50%;
          margin-bottom: 8rpx;
          transition: all 0.3s ease;
        }

        .milestone-text.enhanced {
          font-size: 20rpx;
          color: #999;
          transition: all 0.3s ease;
        }
      }
    }
  }
}

.challenge-card {
  background: #fff;
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 199, 44, 0.08);

  .challenge-header {
    margin-bottom: 40rpx;

    .challenge-title {
      font-size: 36rpx;
      font-weight: 600;
      background: linear-gradient(135deg, #FFC72C, #FFB300);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 12rpx;
      display: block;
    }

    .challenge-period {
      font-size: 26rpx;
      color: #666;
    }
  }

  .progress-bar {
    .progress-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16rpx;

      .progress-label {
        font-size: 26rpx;
        color: #666;
      }

      .progress-value {
        font-size: 26rpx;
        color: #333;
        font-weight: 500;
      }
    }

    .progress-track {
      height: 20rpx;
      background: #FFF8F0;
      border-radius: 10rpx;
      overflow: hidden;

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #FFC72C, #FFB300);
        border-radius: 10rpx;
        transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }
  }
}

.stats-section {
  margin-bottom: 40rpx;

  .section-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
    display: block;
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24rpx;
  }

  .stats-item {
    background: #fff;
    border-radius: 32rpx;
    padding: 40rpx 30rpx;
    text-align: center;
    box-shadow: 0 8rpx 32rpx rgba(255, 199, 44, 0.08);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-4rpx);
      box-shadow: 0 12rpx 36rpx rgba(255, 199, 44, 0.12);
    }

    .stats-icon {
      width: 96rpx;
      height: 96rpx;
      border-radius: 48rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 24rpx;
      background: #FFF8F0;
      
      image {
        width: 48rpx;
        height: 48rpx;
      }
    }

    .stats-value {
      font-size: 44rpx;
      font-weight: 600;
      background: linear-gradient(135deg, #FFC72C, #FFB300);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 12rpx;
      display: block;
    }

    .stats-label {
      font-size: 26rpx;
      color: #666;
    }
  }
}

.calendar-section {
  margin-bottom: 40rpx;

  .section-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
    display: block;
  }

  .calendar-card {
    background: #fff;
    border-radius: 32rpx;
    padding: 30rpx 20rpx;
    box-shadow: 0 8rpx 32rpx rgba(255, 199, 44, 0.08);

    .weekdays {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      margin-bottom: 20rpx;
      text-align: center;

      text {
        font-size: 24rpx;
        color: #999;
        text-align: center;
        font-weight: 500;
      }
    }

    .days-grid {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 8rpx;
      padding: 0;

      .day-item {
        aspect-ratio: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;
        border-radius: 12rpx;
        min-height: 76rpx;
        transition: all 0.3s ease;
        background: #FFF8F0;
        border: none;

        &.placeholder {
          background: transparent;
          box-shadow: none;
          pointer-events: none;
        }

        &.checked {
          background: linear-gradient(135deg, #FFC72C, #FFB300);
          transform: translateY(-2rpx);
          box-shadow: 0 8rpx 16rpx rgba(255, 199, 44, 0.2);

          .day-text, .month-text {
            color: #fff;
          }

          .month-text {
            position: absolute;
            top: -6rpx;
            right: -6rpx;
            background: rgba(255, 255, 255, 0.9);
            color: #FFC72C;
            font-size: 16rpx;
            padding: 4rpx 8rpx;
            border-radius: 6rpx;
            margin: 0;
          }

          .check-mark {
            position: absolute;
            bottom: 8rpx;
            width: 28rpx;
            height: 28rpx;
            display: flex;
            align-items: center;
            justify-content: center;

            image {
              width: 24rpx;
              height: 24rpx;
            }
          }
        }
        
        &.invalid-time {
          background: linear-gradient(135deg, #FF9F1C, #F76E11);
          
          &::after {
            content: '非效';
            position: absolute;
            top: -6rpx;
            right: -6rpx;
            background: rgba(255, 255, 255, 0.9);
            color: #F76E11;
            font-size: 16rpx;
            padding: 4rpx 8rpx;
            border-radius: 6rpx;
          }
        }

        &.failed {
          background: #FFF5F5;

          .day-text {
            color: #FF5252;
          }

          .month-text {
            position: absolute;
            top: -6rpx;
            right: -6rpx;
            background: rgba(255, 82, 82, 0.1);
            color: #FF5252;
            font-size: 16rpx;
            padding: 4rpx 8rpx;
            border-radius: 6rpx;
            margin: 0;
          }
        }

        &.today {
          background: #FFF8F0;
          border: 2rpx solid #FFC72C;
          transform: scale(1.05);

          .day-text {
            color: #FFC72C;
            font-weight: 600;
          }

          &::after {
            content: '今日';
            position: absolute;
            top: -6rpx;
            right: -6rpx;
            background: #FFC72C;
            color: #fff;
            font-size: 16rpx;
            padding: 4rpx 8rpx;
            border-radius: 6rpx;
            transform: scale(0.8);
          }
        }

        &.in-period {
          &:not(.checked):not(.today):not(.failed) {
            &:hover {
              background: #FFF2E6;
              transform: translateY(-2rpx);
            }

            .month-text {
              position: absolute;
              top: -6rpx;
              right: -6rpx;
              background: rgba(255, 199, 44, 0.1);
              color: #FFC72C;
              font-size: 16rpx;
              padding: 4rpx 8rpx;
              border-radius: 6rpx;
              margin: 0;
            }
          }
        }

        &:not(.in-period) {
          background: #F8F8F8;
          opacity: 0.5;
          pointer-events: none;

          .day-text, .month-text {
            color: #999;
          }
        }

        .day-text {
          font-size: 28rpx;
          line-height: 1.2;
          font-weight: 500;
          color: #333;
        }

        .month-text {
          font-size: 20rpx;
          color: #666;
          margin-top: 4rpx;
        }
      }
    }
  }
}

.time-section {
  margin-bottom: 40rpx;

  .section-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
    display: block;
  }

  .time-card {
    background: #fff;
    border-radius: 32rpx;
    padding: 40rpx;
    display: flex;
    align-items: center;
    box-shadow: 0 8rpx 32rpx rgba(255, 199, 44, 0.08);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-4rpx);
      box-shadow: 0 12rpx 36rpx rgba(255, 199, 44, 0.12);
    }

    .time-icon {
      width: 96rpx;
      height: 96rpx;
      border-radius: 48rpx;
      background: #FFF8F0;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 24rpx;
      
      image {
        width: 48rpx;
        height: 48rpx;
      }
    }

    .time-info {
      flex: 1;

      .time-value {
        font-size: 36rpx;
        font-weight: 600;
        background: linear-gradient(135deg, #FFC72C, #FFB300);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 12rpx;
        display: block;
      }

      .time-label {
        font-size: 26rpx;
        color: #666;
      }
    }
  }
}

.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.success-modal {
  width: 600rpx;
  padding: 50rpx 40rpx;
  background: #fff;
  border-radius: 32rpx;
  text-align: center;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
  
  &.warning-modal {
    .success-title {
      background: linear-gradient(135deg, #FF9F1C, #F76E11);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    
    .modal-btn {
      background: linear-gradient(135deg, #FF9F1C, #F76E11);
    }
  }
  
  &.failed-modal {
    .success-title {
      background: linear-gradient(135deg, #FF9F1C, #F76E11);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    
    .modal-btn {
      background: linear-gradient(135deg, #FF9F1C, #F76E11);
    }
  }
  
  .success-image {
    width: 280rpx;
    height: 280rpx;
    margin: 0 auto 40rpx;
    
    image {
      width: 100%;
      height: 100%;
    }
  }
  
  .success-text {
    margin-bottom: 50rpx;
    
    .success-title {
      font-size: 44rpx;
      font-weight: 600;
      background: linear-gradient(135deg, #FFC72C, #FFB300);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 20rpx;
      display: block;
    }
    
    .success-desc {
      font-size: 30rpx;
      color: #666;
      line-height: 1.6;
    }
  }
  
  .modal-btn {
    width: 100%;
    height: 96rpx;
    background: linear-gradient(135deg, #FFC72C, #FFB300);
    border-radius: 48rpx;
    color: #fff;
    font-size: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    box-shadow: 0 8rpx 24rpx rgba(255, 199, 44, 0.2);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
      background: linear-gradient(135deg, #FFC72C, #DA291C);
    }
  }
}

.resources {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.resource-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #FFF8F0;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.resource-item:active {
  transform: scale(0.98);
  background: #FFF2E6;
}

.resource-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
}

.resource-text {
  color: #FFA500;
  font-size: 26rpx;
  font-weight: 500;
}

/* 动画效果 */
@keyframes wave1 {
  0%, 100% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
}

@keyframes wave2 {
  0%, 100% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(-180deg) scale(1.2); }
}

/* 增强的任务区块样式 */
.task-section.enhanced {
  margin: 0 40rpx 32rpx;
  border-radius: 20rpx;
  overflow: hidden;
  border: 1rpx solid rgba(255, 199, 44, 0.1);

  .section-header {
    display: flex;
    align-items: center;
    padding: 20rpx 24rpx;
    background: linear-gradient(135deg, rgba(255, 199, 44, 0.05) 0%, rgba(255, 179, 0, 0.05) 100%);

    .section-icon-wrapper {
      width: 40rpx;
      height: 40rpx;
      background: linear-gradient(135deg, #FFC72C, #FFB300);
      border-radius: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16rpx;

      &.tips {
        background: linear-gradient(135deg, #4CAF50, #66BB6A);
      }

      &.resources {
        background: linear-gradient(135deg, #2196F3, #42A5F5);
      }

      .section-icon {
        width: 24rpx;
        height: 24rpx;
      }
    }

    .section-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #333;
      flex: 1;
    }

    .section-badge {
      padding: 4rpx 12rpx;
      background: #FFC72C;
      color: #fff;
      border-radius: 12rpx;
      font-size: 20rpx;
      font-weight: 500;

      &.tips {
        background: transparent;
        color: #4CAF50;
        font-size: 24rpx;
      }

      &.resources {
        background: #2196F3;
      }
    }
  }

  .section-content {
    padding: 24rpx;
    background: #fff;

    &.tips {
      background: linear-gradient(135deg, rgba(76, 175, 80, 0.02) 0%, rgba(102, 187, 106, 0.02) 100%);
      border-left: 4rpx solid #4CAF50;
    }

    &.resources {
      padding: 0;

      .resource-item.enhanced {
        display: flex;
        align-items: center;
        padding: 20rpx 24rpx;
        border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
        transition: background-color 0.3s ease;

        &:last-child {
          border-bottom: none;
        }

        &:active {
          background-color: rgba(33, 150, 243, 0.05);
        }

        .resource-icon-wrapper {
          width: 40rpx;
          height: 40rpx;
          background: linear-gradient(135deg, #2196F3, #42A5F5);
          border-radius: 20rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16rpx;

          .resource-icon {
            width: 20rpx;
            height: 20rpx;
          }
        }

        .resource-content {
          flex: 1;

          .resource-title {
            font-size: 26rpx;
            font-weight: 500;
            color: #333;
            margin-bottom: 4rpx;
            display: block;
          }

          .resource-desc {
            font-size: 22rpx;
            color: #999;
            display: block;
          }
        }

        .resource-arrow {
          .arrow-icon {
            width: 24rpx;
            height: 24rpx;
            opacity: 0.6;
          }
        }
      }
    }
  }
}

/* 任务状态和进度样式 */
.task-status-section {
  padding: 0 40rpx 40rpx;

  .task-completion-status.completed {
    display: flex;
    align-items: center;
    padding: 24rpx;
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(139, 195, 74, 0.1));
    border-radius: 20rpx;
    border: 2rpx solid rgba(76, 175, 80, 0.2);

    .completion-icon {
      width: 48rpx;
      height: 48rpx;
      margin-right: 16rpx;

      .status-icon {
        width: 100%;
        height: 100%;
      }
    }

    .completion-content {
      flex: 1;

      .completion-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #4CAF50;
        margin-bottom: 4rpx;
        display: block;
      }

      .completion-desc {
        font-size: 24rpx;
        color: #66BB6A;
        display: block;
      }
    }
  }

  .task-motivation {
    display: flex;
    align-items: center;
    padding: 24rpx;
    background: linear-gradient(135deg, rgba(255, 199, 44, 0.1), rgba(255, 179, 0, 0.1));
    border-radius: 20rpx;
    border: 2rpx solid rgba(255, 199, 44, 0.2);

    .motivation-icon {
      width: 48rpx;
      height: 48rpx;
      margin-right: 16rpx;

      .motivation-img {
        width: 100%;
        height: 100%;
      }
    }

    .motivation-content {
      flex: 1;

      .motivation-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #FF8F00;
        margin-bottom: 4rpx;
        display: block;
      }

      .motivation-desc {
        font-size: 24rpx;
        color: #FFB300;
        display: block;
        line-height: 1.4;
      }
    }
  }
}

.task-progress-section {
  padding: 32rpx 40rpx 40rpx;
  background: linear-gradient(135deg, rgba(255, 199, 44, 0.02) 0%, rgba(255, 179, 0, 0.02) 100%);

  .progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;

    .progress-title {
      font-size: 26rpx;
      font-weight: 500;
      color: #333;
    }

    .progress-value {
      font-size: 24rpx;
      font-weight: 600;
      color: #FFC72C;
    }
  }

  .progress-track {
    position: relative;
    height: 12rpx;
    background: rgba(255, 199, 44, 0.1);
    border-radius: 6rpx;
    overflow: hidden;
    margin-bottom: 24rpx;

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #FFC72C 0%, #FFB300 100%);
      border-radius: 6rpx;
      transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .progress-marker {
      position: absolute;
      top: -4rpx;
      transform: translateX(-50%);
      transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);

      .marker-dot {
        width: 20rpx;
        height: 20rpx;
        background: #FFC72C;
        border-radius: 10rpx;
        border: 3rpx solid #fff;
        box-shadow: 0 2rpx 8rpx rgba(255, 199, 44, 0.3);
      }
    }
  }

  .progress-milestones {
    position: relative;
    height: 40rpx;

    .milestone {
      position: absolute;
      transform: translateX(-50%);
      display: flex;
      flex-direction: column;
      align-items: center;

      .milestone-dot {
        width: 12rpx;
        height: 12rpx;
        background: rgba(255, 199, 44, 0.3);
        border-radius: 6rpx;
        margin-bottom: 8rpx;
        transition: background-color 0.3s ease;
      }

      .milestone-text {
        font-size: 20rpx;
        color: #999;
        font-weight: 500;
      }

      &.completed {
        .milestone-dot {
          background: #FFC72C;
        }

        .milestone-text {
          color: #FFC72C;
        }
      }
    }
  }
}

/* 动画效果 */
@keyframes wave1 {
  0%, 100% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
}

@keyframes wave2 {
  0%, 100% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(-180deg) scale(1.1); }
}

@keyframes shine {
  0% { left: -100%; }
  100% { left: 100%; }
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0.5;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 为卡片添加进入动画 */
.today-card,
.daily-task-card,
.challenge-card,
.stats-section,
.calendar-section,
.time-section {
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.today-card {
  animation-delay: 0.1s;
}

.daily-task-card {
  animation-delay: 0.2s;
}

.challenge-card {
  animation-delay: 0.3s;
}

.stats-section {
  animation-delay: 0.4s;
}

.calendar-section {
  animation-delay: 0.5s;
}

.time-section {
  animation-delay: 0.6s;
}
</style>
