{"version": 3, "file": "challenge-daily.js", "sources": ["pages/home/<USER>", "../../../../tool/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaG9tZS9jaGFsbGVuZ2UtZGFpbHkudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"page-container modern-design\">\r\n    <!-- 动态标题栏 - 年轻化设计 -->\r\n    <view class=\"modern-header\" :class=\"{ 'scrolled': isScrolled }\" :style=\"headerStyle\">\r\n      <view class=\"header-bg\">\r\n        <view class=\"bg-gradient\"></view>\r\n        <view class=\"bg-particles\">\r\n          <view v-for=\"i in 6\" :key=\"i\" class=\"particle\" :style=\"getParticleStyle(i)\"></view>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"header-content\">\r\n        <view class=\"back-button\" @click=\"goBack\">\r\n          <view class=\"back-icon\">\r\n            <text class=\"iconfont\">←</text>\r\n          </view>\r\n        </view>\r\n\r\n        <view class=\"header-title-area\">\r\n          <text class=\"header-title\" :class=\"{ 'show': showTitle }\">{{ challenge.title || '挑战详情' }}</text>\r\n          <view class=\"title-decoration\">\r\n            <view class=\"decoration-line\"></view>\r\n          </view>\r\n        </view>\r\n\r\n        <view class=\"header-actions\">\r\n          <view class=\"action-btn share-btn\">\r\n            <text class=\"iconfont\">📤</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 页面内容区 -->\r\n    <scroll-view\r\n      class=\"scroll-content modern-scroll\"\r\n      scroll-y\r\n      @scroll=\"handleScroll\"\r\n      :style=\"{ paddingTop: headerHeight + 'px' }\"\r\n    >\r\n      <!-- 今日打卡卡片 - 年轻化重设计 -->\r\n      <view class=\"today-card modern-card\" :class=\"{ 'completed': isTodayCompleted }\">\r\n        <view class=\"card-bg-effects\">\r\n          <view class=\"gradient-orb orb-1\"></view>\r\n          <view class=\"gradient-orb orb-2\"></view>\r\n          <view class=\"floating-shapes\">\r\n            <view v-for=\"i in 4\" :key=\"i\" class=\"shape\" :class=\"`shape-${i}`\"></view>\r\n          </view>\r\n        </view>\r\n\r\n        <view class=\"today-header\">\r\n          <view class=\"today-badge\">\r\n            <text class=\"badge-emoji\">🔥</text>\r\n            <text class=\"badge-text\">今日挑战</text>\r\n          </view>\r\n          <view class=\"today-date-modern\">\r\n            <text class=\"date-text\">{{ formatToday() }}</text>\r\n            <view class=\"date-indicator\" :class=\"getTodayStatusClass\"></view>\r\n          </view>\r\n        </view>\r\n\r\n        <view class=\"today-main-content\">\r\n          <view class=\"status-display\">\r\n            <view class=\"status-icon\" :class=\"getTodayStatusClass\">\r\n              <text class=\"status-emoji\">{{ getStatusEmoji() }}</text>\r\n            </view>\r\n            <view class=\"status-info\">\r\n              <text class=\"status-title\">{{ getTodayStatusText }}</text>\r\n              <text class=\"status-subtitle\">{{ getStatusSubtitle() }}</text>\r\n            </view>\r\n          </view>\r\n\r\n          <view class=\"time-info-modern\">\r\n            <view class=\"time-label\">\r\n              <text class=\"time-icon\">⏰</text>\r\n              <text class=\"time-text\">有效时间</text>\r\n            </view>\r\n            <text class=\"time-range\">{{ formatTime(todayStatus?.validTimeRange?.startTime || challenge.startTime) }} - {{ formatTime(todayStatus?.validTimeRange?.endTime || challenge.endTime) }}</text>\r\n          </view>\r\n\r\n          <view v-if=\"todayStatus && todayStatus.record\" class=\"checkin-result-modern\">\r\n            <view class=\"result-item\">\r\n              <text class=\"result-label\">打卡时间</text>\r\n              <text class=\"result-value\">{{ formatDateTime(todayStatus.record.checkinTime) }}</text>\r\n            </view>\r\n            <view v-if=\"todayStatus.record.refundStatus !== '4'\" class=\"result-item refund-info\">\r\n              <text class=\"result-label\">退款状态</text>\r\n              <view class=\"refund-status\" :class=\"getRefundStatusClass(todayStatus.record.refundStatus)\">\r\n                <text class=\"refund-text\">{{ getRefundStatusText(todayStatus.record.refundStatus) }}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <view v-if=\"!isTodayCompleted && challenge.status === '0'\" class=\"action-area\">\r\n          <button class=\"modern-checkin-btn\" @click=\"handleCheckin\">\r\n            <view class=\"btn-bg\">\r\n              <view class=\"btn-gradient\"></view>\r\n              <view class=\"btn-shine\"></view>\r\n            </view>\r\n            <view class=\"btn-content\">\r\n              <text class=\"btn-emoji\">⚡</text>\r\n              <text class=\"btn-text\">立即打卡</text>\r\n            </view>\r\n            <view class=\"btn-ripple\"></view>\r\n          </button>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 今日任务详情卡片，仅当挑战有每日任务时显示 -->\r\n      <view v-if=\"challenge.hasDailyTasks && dailyTask\" class=\"daily-task-card\">\r\n        <!-- 任务头部 -->\r\n        <view class=\"task-header\">\r\n          <view class=\"task-header-bg\">\r\n            <view class=\"header-wave wave1\"></view>\r\n            <view class=\"header-wave wave2\"></view>\r\n          </view>\r\n          <view class=\"task-header-content\">\r\n            <view class=\"day-circle\">\r\n              <text class=\"day-number\">{{ currentDayNumber }}</text>\r\n              <text class=\"day-text\">DAY</text>\r\n            </view>\r\n            <view class=\"task-header-info\">\r\n              <text class=\"task-category\">{{ getTaskCategory(dailyTask.task) }}</text>\r\n              <text class=\"task-main-title\">{{ dailyTask.task.title }}</text>\r\n              <view class=\"task-difficulty-indicator\">\r\n                <view\r\n                  v-for=\"star in 3\"\r\n                  :key=\"star\"\r\n                  class=\"difficulty-star\"\r\n                  :class=\"{ 'active': star <= getTaskDifficulty(currentDayNumber) }\"\r\n                >\r\n                  ⭐\r\n                </view>\r\n                <text class=\"difficulty-text\">{{ getDifficultyText(currentDayNumber) }}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 任务描述 -->\r\n        <view class=\"task-description-section\">\r\n          <view class=\"description-icon\">\r\n            <image src=\"/static/images/task/description.png\" class=\"icon\" mode=\"aspectFit\"></image>\r\n          </view>\r\n          <view class=\"description-content\">\r\n            <text class=\"description-title\">今日目标</text>\r\n            <text class=\"description-text\">{{ dailyTask.task.description }}</text>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 任务要求 -->\r\n        <view v-if=\"dailyTask.task.taskRequirements\" class=\"task-section enhanced\">\r\n          <view class=\"section-header\">\r\n            <view class=\"section-icon-wrapper\">\r\n              <image src=\"/static/images/task/check-list.png\" class=\"section-icon\" mode=\"aspectFit\"></image>\r\n            </view>\r\n            <text class=\"section-title\">任务要求</text>\r\n            <view class=\"section-badge\">必读</view>\r\n          </view>\r\n          <view class=\"section-content\">\r\n            {{ dailyTask.task.taskRequirements }}\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 小贴士 -->\r\n        <view v-if=\"dailyTask.task.tips\" class=\"task-section enhanced\">\r\n          <view class=\"section-header\">\r\n            <view class=\"section-icon-wrapper tips\">\r\n              <image src=\"/static/images/task/tips.png\" class=\"section-icon\" mode=\"aspectFit\"></image>\r\n            </view>\r\n            <text class=\"section-title\">成长贴士</text>\r\n            <view class=\"section-badge tips\">💡</view>\r\n          </view>\r\n          <view class=\"section-content tips\">\r\n            {{ dailyTask.task.tips }}\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 相关资源 -->\r\n        <view v-if=\"dailyTask.task.resourceUrls\" class=\"task-section enhanced\">\r\n          <view class=\"section-header\">\r\n            <view class=\"section-icon-wrapper resources\">\r\n              <image src=\"/static/images/task/resources.png\" class=\"section-icon\" mode=\"aspectFit\"></image>\r\n            </view>\r\n            <text class=\"section-title\">学习资源</text>\r\n            <view class=\"section-badge resources\">{{ formatResourceUrls(dailyTask.task.resourceUrls).length }}</view>\r\n          </view>\r\n          <view class=\"section-content resources\">\r\n            <view v-for=\"(url, index) in formatResourceUrls(dailyTask.task.resourceUrls)\"\r\n                  :key=\"index\"\r\n                  class=\"resource-item enhanced\"\r\n                  @tap=\"openResource(url)\">\r\n              <view class=\"resource-icon-wrapper\">\r\n                <image src=\"/static/images/task/link.png\" class=\"resource-icon\" mode=\"aspectFit\"></image>\r\n              </view>\r\n              <view class=\"resource-content\">\r\n                <text class=\"resource-title\">学习资源 {{ index + 1 }}</text>\r\n                <text class=\"resource-desc\">点击查看详细内容</text>\r\n              </view>\r\n              <view class=\"resource-arrow\">\r\n                <image src=\"/static/images/challenge/arrow-right.png\" class=\"arrow-icon\" mode=\"aspectFit\"></image>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 完成状态或激励信息 -->\r\n        <view class=\"task-status-section\">\r\n          <view v-if=\"dailyTask.isCompleted\" class=\"task-completion-status completed\">\r\n            <view class=\"completion-icon\">\r\n              <image src=\"/static/images/challenge/check-circle.png\" class=\"status-icon\" mode=\"aspectFit\"></image>\r\n            </view>\r\n            <view class=\"completion-content\">\r\n              <text class=\"completion-title\">🎉 今日任务已完成</text>\r\n              <text class=\"completion-desc\">你今天表现得很棒，继续保持！</text>\r\n            </view>\r\n          </view>\r\n          <view v-else class=\"task-motivation\">\r\n            <view class=\"motivation-icon\">\r\n              <image src=\"/static/images/challenge/rocket.png\" class=\"motivation-img\" mode=\"aspectFit\"></image>\r\n            </view>\r\n            <view class=\"motivation-content\">\r\n              <text class=\"motivation-title\">{{ getMotivationTitle(currentDayNumber) }}</text>\r\n              <text class=\"motivation-desc\">{{ getMotivationDesc(currentDayNumber) }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 进度指示器 - 优化样式 -->\r\n        <view class=\"task-progress-section enhanced\">\r\n          <view class=\"progress-header\">\r\n            <view class=\"progress-title-wrapper\">\r\n              <image src=\"/static/images/challenge/progress.png\" class=\"progress-icon\" mode=\"aspectFit\"></image>\r\n              <text class=\"progress-title\">挑战进度</text>\r\n            </view>\r\n            <view class=\"progress-value-wrapper\">\r\n              <text class=\"progress-value\">{{ currentDayNumber }}</text>\r\n              <text class=\"progress-separator\">/</text>\r\n              <text class=\"progress-total\">{{ challenge.durationDays || 21 }}</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"progress-track enhanced\">\r\n            <view class=\"progress-fill enhanced\" :style=\"{ width: taskProgressWidth }\">\r\n              <view class=\"progress-shine\"></view>\r\n            </view>\r\n            <view class=\"progress-marker enhanced\" :style=\"{ left: taskProgressWidth }\">\r\n              <view class=\"marker-dot enhanced\">\r\n                <view class=\"marker-pulse\"></view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          <view class=\"progress-milestones enhanced\">\r\n            <view\r\n              v-for=\"milestone in getProgressMilestones()\"\r\n              :key=\"milestone.day\"\r\n              class=\"milestone enhanced\"\r\n              :class=\"{ 'completed': currentDayNumber >= milestone.day }\"\r\n              :style=\"{ left: milestone.position }\"\r\n            >\r\n              <view class=\"milestone-dot enhanced\"></view>\r\n              <text class=\"milestone-text enhanced\">{{ milestone.text }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 挑战信息卡片 -->\r\n      <view class=\"challenge-card\">\r\n        <view class=\"challenge-header\">\r\n          <text class=\"challenge-title\">{{ challenge.title }}</text>\r\n          <text class=\"challenge-period\">{{ formatDateRange(challenge.startDate, challenge.endDate) }}</text>\r\n        </view>\r\n        <view class=\"progress-bar\">\r\n          <view class=\"progress-info\">\r\n            <text class=\"progress-label\">挑战进度</text>\r\n            <text class=\"progress-value\">{{ stats.keepDays }}/{{ challenge.ruleDays }}天</text>\r\n          </view>\r\n          <view class=\"progress-track\">\r\n            <view class=\"progress-fill\" :style=\"{ width: progressWidth }\"></view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 统计数据卡片 -->\r\n      <view class=\"stats-section\">\r\n        <text class=\"section-title\">数据概览</text>\r\n        <view class=\"stats-grid\">\r\n          <view class=\"stats-item\">\r\n            <view class=\"stats-icon\" style=\"background: rgba(86, 119, 252, 0.1)\">\r\n              <image src=\"/static/images/challenge/leiji.png\" mode=\"aspectFit\"></image>\r\n            </view>\r\n            <text class=\"stats-value\">{{ stats.keepDays }}</text>\r\n            <text class=\"stats-label\">累计打卡</text>\r\n          </view>\r\n          <view class=\"stats-item\">\r\n            <view class=\"stats-icon\" style=\"background: rgba(255, 107, 107, 0.1)\">\r\n              <image src=\"/static/images/challenge/fire.png\" mode=\"aspectFit\"></image>\r\n            </view>\r\n            <text class=\"stats-value\">{{ stats.totalEnergy }}</text>\r\n            <text class=\"stats-label\">累计能量</text>\r\n          </view>\r\n          <view class=\"stats-item\">\r\n            <view class=\"stats-icon\" style=\"background: rgba(255, 215, 0, 0.1)\">\r\n              <image src=\"/static/images/challenge/medal.png\" mode=\"aspectFit\"></image>\r\n            </view>\r\n            <text class=\"stats-value\">{{ stats.maxContinuousDays }}</text>\r\n            <text class=\"stats-label\">最长连续</text>\r\n          </view>\r\n          <view class=\"stats-item\">\r\n            <view class=\"stats-icon\" style=\"background: rgba(32, 201, 151, 0.1)\">\r\n              <image src=\"/static/images/challenge/star.png\" mode=\"aspectFit\"></image>\r\n            </view>\r\n            <text class=\"stats-value\">{{ (stats.completionRate * 100).toFixed(1) }}%</text>\r\n            <text class=\"stats-label\">完成率</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 打卡记录日历 -->\r\n      <view class=\"calendar-section\">\r\n        <text class=\"section-title\">打卡记录</text>\r\n        <view class=\"calendar-card\">\r\n          <view class=\"weekdays\">\r\n            <text v-for=\"day in ['日', '一', '二', '三', '四', '五', '六']\" :key=\"day\">{{ day }}</text>\r\n          </view>\r\n          <view class=\"days-grid\">\r\n            <view \r\n              v-for=\"(day, index) in periodDays\" \r\n              :key=\"index\"\r\n              class=\"day-item\"\r\n              :class=\"{\r\n                'checked': day.checked,\r\n                'invalid-time': day.invalidTime,\r\n                'today': day.isToday,\r\n                'in-period': day.inPeriod,\r\n                'failed': day.failed,\r\n                'placeholder': day.isPlaceholder\r\n              }\"\r\n            >\r\n              <template v-if=\"!day.isPlaceholder\">\r\n                <text class=\"day-text\">{{ day.date }}</text>\r\n                <text class=\"month-text\" v-if=\"day.showMonth\">{{ day.month }}月</text>\r\n                <view v-if=\"day.checked\" class=\"check-mark\">\r\n                  <image src=\"/static/images/common/check.png\" mode=\"aspectFit\"></image>\r\n                </view>\r\n              </template>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 平均打卡时段 -->\r\n      <view class=\"time-section\">\r\n        <text class=\"section-title\">打卡时段</text>\r\n        <view class=\"time-card\">\r\n          <view class=\"time-icon\">\r\n            <image src=\"/static/images/challenge/clock.png\" mode=\"aspectFit\"></image>\r\n          </view>\r\n          <view class=\"time-info\">\r\n            <text class=\"time-value\">{{ stats.averageCheckinTime || '暂无数据' }}</text>\r\n            <text class=\"time-label\">平均打卡时间</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </scroll-view>\r\n\r\n    <!-- 打卡弹窗 使用自定义模态框 -->\r\n    <view v-if=\"checkinPopup\" class=\"modal-mask\">\r\n      <view class=\"modal-content success-modal\" :class=\"{'warning-modal': !isValidTime, 'failed-modal': todayStatus && todayStatus.record && todayStatus.record.checkinStatus === '2'}\">\r\n        <view class=\"success-image\">\r\n          <image :src=\"getModalIcon()\" mode=\"aspectFit\"></image>\r\n        </view>\r\n        <view class=\"success-text\">\r\n          <text class=\"success-title\">{{ getModalTitle() }}</text>\r\n          <text class=\"success-desc\">{{ getModalDescription() }}</text>\r\n        </view>\r\n        <view class=\"modal-footer\">\r\n          <button class=\"modal-btn\" @click=\"closeCheckinPopup\">{{ isValidTime ? '我还能更狠一点' : '发誓不再废' }}</button>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport CustomNavbar from '@/components/custom-navbar/index.vue'\r\nimport { getChallengeStats, checkInTask, getTodayTaskStatus, getChallengeTask } from '@/api/index'\r\n\r\nexport default {\r\n  components: {\r\n    CustomNavbar\r\n  },\r\n\r\n  data() {\r\n    return {\r\n      statusBarHeight: 20,\r\n      challenge: {},\r\n      stats: {\r\n        keepDays: 0,\r\n        completionRate: 0,\r\n        totalEnergy: 0,\r\n        maxContinuousDays: 0,\r\n        averageCheckinTime: ''\r\n      },\r\n      selectedDates: [],\r\n      checkinRemark: '',\r\n      isTodayCompleted: false,\r\n      todayStatus: null,\r\n      currentMonth: '',\r\n      calendarDays: [],\r\n      isLoading: true,\r\n      checkinPopup: false,\r\n      isValidTime: true,\r\n      dailyTask: null,\r\n      currentDayNumber: 0,\r\n      // 现代化UI控制\r\n      isScrolled: false,\r\n      showTitle: false,\r\n      lastScrollTop: 0,\r\n      headerHeight: 120,\r\n      scrollY: 0\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    // 头部样式\r\n    headerStyle() {\r\n      const opacity = Math.min(this.scrollY / 100, 0.95)\r\n      return {\r\n        background: `linear-gradient(135deg,\r\n          rgba(138, 43, 226, ${0.8 + opacity * 0.2}) 0%,\r\n          rgba(30, 144, 255, ${0.8 + opacity * 0.2}) 50%,\r\n          rgba(255, 20, 147, ${0.8 + opacity * 0.2}) 100%)`,\r\n        backdropFilter: `blur(${10 + this.scrollY * 0.1}px)`\r\n      }\r\n    },\r\n\r\n    getTodayStatusText() {\r\n      if (this.isLoading) return '加载中...'\r\n      if (this.challenge.status !== '0') return '挑战已结束'\r\n      if (!this.todayStatus || !this.todayStatus.record) return '等待打卡'\r\n      if (this.todayStatus.record && this.todayStatus.record.checkinStatus === '1') {\r\n        return this.todayStatus.record.isValidTime ? '打卡成功 🎉' : '时间无效 😅'\r\n      } else if (this.todayStatus.record && this.todayStatus.record.checkinStatus === '2') {\r\n        return '打卡失败 😔'\r\n      } else {\r\n        return '等待打卡'\r\n      }\r\n    },\r\n\r\n    getTodayStatusClass() {\r\n      if (this.isLoading) return 'status-loading'\r\n      if (this.challenge.status !== '0') return 'status-ended'\r\n      if (!this.todayStatus) return 'status-pending'\r\n      if (this.todayStatus.record && this.todayStatus.record.checkinStatus === '1') {\r\n        return this.todayStatus.record.isValidTime ? 'status-success' : 'status-warning'\r\n      } else if (this.todayStatus.record && this.todayStatus.record.checkinStatus === '2') {\r\n        return 'status-failed'\r\n      } else {\r\n        return 'status-pending'\r\n      }\r\n    },\r\n\r\n    progressWidth() {\r\n      if (!this.challenge.ruleDays) return '0%'\r\n      return `${(this.stats.keepDays / this.challenge.ruleDays * 100).toFixed(1)}%`\r\n    },\r\n\r\n    periodDays() {\r\n      if (!this.challenge.startDate || !this.challenge.endDate) return []\r\n      \r\n      const startDate = new Date(this.challenge.startDate)\r\n      const endDate = new Date(this.challenge.endDate)\r\n      const days = []\r\n      \r\n      // 计算开始日期是星期几 (0-6, 0表示周日)\r\n      const startDayOfWeek = startDate.getDay()\r\n      \r\n      // 添加前面的空白占位符\r\n      for (let i = 0; i < startDayOfWeek; i++) {\r\n        days.push({\r\n          date: '',\r\n          month: 0,\r\n          showMonth: false,\r\n          checked: false,\r\n          isToday: false,\r\n          inPeriod: false,\r\n          failed: false,\r\n          isPlaceholder: true\r\n        })\r\n      }\r\n      \r\n      let currentDate = new Date(startDate)\r\n      const today = new Date()\r\n      today.setHours(0, 0, 0, 0)\r\n      \r\n      while (currentDate <= endDate) {\r\n        const isStartMonth = currentDate.getDate() === 1 || \r\n                            currentDate.getTime() === startDate.getTime()\r\n        \r\n        const isFailed = currentDate < today && \r\n                        !this.isDateChecked(currentDate) && \r\n                        this.challenge.status === '0'\r\n        \r\n        days.push({\r\n          date: currentDate.getDate(),\r\n          month: currentDate.getMonth() + 1,\r\n          showMonth: isStartMonth,\r\n          checked: this.isDateChecked(currentDate),\r\n          isToday: currentDate.toDateString() === today.toDateString(),\r\n          inPeriod: true,\r\n          failed: isFailed,\r\n          fullDate: new Date(currentDate),\r\n          dayOfWeek: currentDate.getDay()  // 添加星期几信息 (0-6)\r\n        })\r\n        \r\n        currentDate.setDate(currentDate.getDate() + 1)\r\n      }\r\n      \r\n      return days\r\n    },\r\n\r\n    // 计算任务进度宽度\r\n    taskProgressWidth() {\r\n      const totalDays = this.challenge.durationDays || 21\r\n      const progress = (this.currentDayNumber / totalDays) * 100\r\n      return `${Math.min(progress, 100)}%`\r\n    }\r\n  },\r\n\r\n  onPullDownRefresh() {\r\n    this.loadData().then(() => {\r\n      uni.stopPullDownRefresh();\r\n    }).catch(() => {\r\n      uni.stopPullDownRefresh();\r\n    });\r\n  },\r\n\r\n  onLoad(options) {\r\n    // 获取状态栏高度\r\n    const systemInfo = uni.getSystemInfoSync()\r\n    this.statusBarHeight = systemInfo.statusBarHeight\r\n\r\n    if (options.selectedCard) {\r\n      this.challenge = JSON.parse(decodeURIComponent(options.selectedCard))\r\n      this.loadData()\r\n      this.initCalendar()\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack()\r\n    },\r\n\r\n    // 处理滚动事件 - 现代化动画效果\r\n    handleScroll(e) {\r\n      const scrollTop = e.detail.scrollTop\r\n      this.scrollY = scrollTop\r\n\r\n      // 控制头部状态\r\n      this.isScrolled = scrollTop > 50\r\n      this.showTitle = scrollTop > 80\r\n\r\n      this.lastScrollTop = scrollTop\r\n    },\r\n\r\n    // 获取粒子样式\r\n    getParticleStyle(index) {\r\n      const positions = [\r\n        { left: '10%', top: '20%', animationDelay: '0s' },\r\n        { left: '80%', top: '15%', animationDelay: '1s' },\r\n        { left: '60%', top: '40%', animationDelay: '2s' },\r\n        { left: '20%', top: '60%', animationDelay: '0.5s' },\r\n        { left: '90%', top: '70%', animationDelay: '1.5s' },\r\n        { left: '40%', top: '80%', animationDelay: '2.5s' }\r\n      ]\r\n      return positions[index - 1] || {}\r\n    },\r\n\r\n    // 获取状态表情\r\n    getStatusEmoji() {\r\n      const statusClass = this.getTodayStatusClass\r\n      const emojiMap = {\r\n        'status-success': '🎉',\r\n        'status-warning': '⚠️',\r\n        'status-failed': '😔',\r\n        'status-pending': '⏳',\r\n        'status-ended': '🏁',\r\n        'status-loading': '⏳'\r\n      }\r\n      return emojiMap[statusClass] || '⏳'\r\n    },\r\n\r\n    // 获取状态副标题\r\n    getStatusSubtitle() {\r\n      const statusClass = this.getTodayStatusClass\r\n      const subtitleMap = {\r\n        'status-success': '太棒了！继续保持',\r\n        'status-warning': '时间不对，下次注意',\r\n        'status-failed': '没关系，明天再来',\r\n        'status-pending': '准备好了吗？',\r\n        'status-ended': '挑战已完成',\r\n        'status-loading': '正在加载...'\r\n      }\r\n      return subtitleMap[statusClass] || '加油！'\r\n    },\r\n\r\n    // 获取退款状态样式类\r\n    getRefundStatusClass(status) {\r\n      const classMap = {\r\n        '0': 'refund-pending',\r\n        '1': 'refund-success',\r\n        '2': 'refund-failed',\r\n        '3': 'refund-processing'\r\n      }\r\n      return classMap[status] || ''\r\n    },\r\n\r\n    // 获取退款状态文本\r\n    getRefundStatusText(status) {\r\n      const textMap = {\r\n        '0': '退款待处理',\r\n        '1': '已退款',\r\n        '2': '退款失败',\r\n        '3': '退款处理中'\r\n      }\r\n      return textMap[status] || '未知状态'\r\n    },\r\n\r\n    async loadData() {\r\n      try {\r\n        this.isLoading = true\r\n        uni.showLoading({ title: '加载中...' })\r\n        \r\n        // 获取今日状态\r\n        const statusRes = await getTodayTaskStatus(this.challenge.participationId)\r\n        if (statusRes.code === 200) {\r\n          this.todayStatus = statusRes.data\r\n          this.isTodayCompleted = this.todayStatus?.record\r\n        }\r\n\r\n        // 获取统计数据\r\n        const statsRes = await getChallengeStats(this.challenge.participationId)\r\n        if (statsRes.code === 200) {\r\n          this.stats = statsRes.data\r\n          this.updateCalendarCheckedDays(this.stats.checkinDates)\r\n        }\r\n\r\n        // 如果挑战有每日任务，获取当天任务详情\r\n        if (this.challenge.hasDailyTasks) {\r\n          // 计算当前是参与挑战后的第几天\r\n          const today = new Date()\r\n          const startDate = new Date(this.challenge.startDate)\r\n          const dayDiff = Math.floor((today - startDate) / (1000 * 60 * 60 * 24)) + 1\r\n          this.currentDayNumber = Math.min(Math.max(1, dayDiff), 21) // 确保在1-21天范围内\r\n          \r\n          try {\r\n            const taskRes = await getChallengeTask(this.challenge.participationId, this.currentDayNumber)\r\n            if (taskRes.code === 200) {\r\n              this.dailyTask = taskRes.data\r\n            }\r\n          } catch (error) {\r\n            console.error('获取每日任务失败:', error)\r\n          }\r\n        }\r\n\r\n        uni.hideLoading()\r\n        this.isLoading = false\r\n      } catch (error) {\r\n        uni.hideLoading()\r\n        this.isLoading = false\r\n        uni.showToast({\r\n          title: '加载失败',\r\n          icon: 'none'\r\n        })\r\n      }\r\n    },\r\n\r\n    formatToday() {\r\n      const today = new Date()\r\n      return `${today.getMonth() + 1}月${today.getDate()}日`\r\n    },\r\n\r\n    formatDateRange(start, end) {\r\n      if (!start || !end) return ''\r\n      const startDate = new Date(start)\r\n      const endDate = new Date(end)\r\n      return `${startDate.getMonth() + 1}月${startDate.getDate()}日 - ${endDate.getMonth() + 1}月${endDate.getDate()}日`\r\n    },\r\n\r\n    formatDateTime(time) {\r\n      if (!time) return '--:--'\r\n      const date = new Date(time)\r\n      return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`\r\n    },\r\n\r\n    formatTime(dateStr) {\r\n        if (!dateStr) return '00:00';\r\n        const timeStr = dateStr.split(' ')[1] || dateStr;\r\n        const timeParts = timeStr.split(':');\r\n        if (timeParts.length >= 2) {\r\n            return `${timeParts[0]}:${timeParts[1]}`;\r\n        }\r\n        return timeStr;\r\n    },\r\n\r\n    initCalendar() {\r\n      const startDate = new Date(this.challenge.startDate)\r\n      const endDate = new Date(this.challenge.endDate)\r\n      const now = new Date()\r\n      \r\n      // 默认显示当前月份,如果当前月不在挑战周期内,则显示开始月\r\n      let currentDate = now\r\n      if (now < startDate || now > endDate) {\r\n        currentDate = startDate\r\n      }\r\n      \r\n      this.currentMonth = `${currentDate.getFullYear()}年${currentDate.getMonth() + 1}月`\r\n      this.generateCalendarDays(currentDate)\r\n    },\r\n\r\n    generateCalendarDays(date) {\r\n      const year = date.getFullYear()\r\n      const month = date.getMonth()\r\n      const firstDay = new Date(year, month, 1)\r\n      const lastDay = new Date(year, month + 1, 0)\r\n      \r\n      const startDate = new Date(this.challenge.startDate)\r\n      const endDate = new Date(this.challenge.endDate)\r\n      \r\n      const days = []\r\n      const startOffset = firstDay.getDay()\r\n      \r\n      // 上个月的天数\r\n      for (let i = startOffset - 1; i >= 0; i--) {\r\n        const prevDate = new Date(year, month, -i)\r\n        days.push({\r\n          date: prevDate.getDate(),\r\n          currentMonth: false,\r\n          checked: false,\r\n          isToday: false,\r\n          fullDate: prevDate,\r\n          inRange: prevDate >= startDate && prevDate <= endDate\r\n        })\r\n      }\r\n      \r\n      // 当前月的天数\r\n      const today = new Date()\r\n      for (let i = 1; i <= lastDay.getDate(); i++) {\r\n        const currentDate = new Date(year, month, i)\r\n        days.push({\r\n          date: i,\r\n          currentMonth: true,\r\n          checked: false,\r\n          isToday: currentDate.toDateString() === today.toDateString(),\r\n          fullDate: currentDate,\r\n          inRange: currentDate >= startDate && currentDate <= endDate\r\n        })\r\n      }\r\n      \r\n      // 下个月的天数\r\n      const remainingDays = 42 - days.length\r\n      for (let i = 1; i <= remainingDays; i++) {\r\n        const nextDate = new Date(year, month + 1, i)\r\n        days.push({\r\n          date: i,\r\n          currentMonth: false,\r\n          checked: false,\r\n          isToday: false,\r\n          fullDate: nextDate,\r\n          inRange: nextDate >= startDate && nextDate <= endDate\r\n        })\r\n      }\r\n      \r\n      this.calendarDays = days\r\n    },\r\n\r\n    updateCalendarCheckedDays(checkedDates) {\r\n      if (!checkedDates || !checkedDates.length) return\r\n      \r\n      if (this.stats.dailyRecords && this.stats.dailyRecords.length > 0) {\r\n        this.stats.dailyRecords.forEach(daily => {\r\n          const checkedDate = new Date(daily.checkinDate)\r\n          const dayIndex = this.calendarDays.findIndex(day => \r\n            day.fullDate.toDateString() === checkedDate.toDateString()\r\n          )\r\n          if (dayIndex !== -1) {\r\n            this.calendarDays[dayIndex].checked = true\r\n            this.calendarDays[dayIndex].invalidTime = !daily.isValidTime\r\n          }\r\n        })\r\n      } else {\r\n        // 向后兼容，没有详细记录的情况\r\n        checkedDates.forEach(dateStr => {\r\n          const checkedDate = new Date(dateStr)\r\n          const dayIndex = this.calendarDays.findIndex(day => \r\n            day.fullDate.toDateString() === checkedDate.toDateString()\r\n          )\r\n          if (dayIndex !== -1) {\r\n            this.calendarDays[dayIndex].checked = true\r\n          }\r\n        })\r\n      }\r\n    },\r\n\r\n    prevMonth() {\r\n      const [year, month] = this.currentMonth.replace(/[年月]/g, '-').split('-')\r\n      const prevDate = new Date(parseInt(year), parseInt(month) - 2)\r\n      \r\n      // 检查是否超出挑战周期\r\n      const startDate = new Date(this.challenge.startDate)\r\n      if (prevDate < new Date(startDate.getFullYear(), startDate.getMonth(), 1)) {\r\n        return\r\n      }\r\n      \r\n      this.currentMonth = `${prevDate.getFullYear()}年${prevDate.getMonth() + 1}月`\r\n      this.generateCalendarDays(prevDate)\r\n      this.updateCalendarCheckedDays(this.stats.checkinDates)\r\n    },\r\n\r\n    nextMonth() {\r\n      const [year, month] = this.currentMonth.replace(/[年月]/g, '-').split('-')\r\n      const nextDate = new Date(parseInt(year), parseInt(month))\r\n      \r\n      // 检查是否超出挑战周期\r\n      const endDate = new Date(this.challenge.endDate)\r\n      if (nextDate > new Date(endDate.getFullYear(), endDate.getMonth(), 1)) {\r\n        return\r\n      }\r\n      \r\n      this.currentMonth = `${nextDate.getFullYear()}年${nextDate.getMonth() + 1}月`\r\n      this.generateCalendarDays(nextDate)\r\n      this.updateCalendarCheckedDays(this.stats.checkinDates)\r\n    },\r\n\r\n    async handleCheckin() {\r\n      if (this.challenge.status !== '0') {\r\n        uni.showToast({\r\n          title: '任务已结束',\r\n          icon: 'none'\r\n        })\r\n        return\r\n      }\r\n      if (this.isTodayCompleted) {\r\n        uni.showToast({\r\n          title: '今日已完成打卡',\r\n          icon: 'none'\r\n        })\r\n        return\r\n      }\r\n\r\n      try {\r\n        uni.showLoading({ title: '打卡中...' })\r\n        const res = await checkInTask({\r\n          participationId: this.challenge.participationId,\r\n          remark: ''\r\n        })\r\n\r\n        uni.hideLoading()\r\n        if (res.code === 200) {\r\n          this.isTodayCompleted = true\r\n          this.isValidTime = res.data.isValidTime || false\r\n          await this.loadData()\r\n          this.checkinPopup = true\r\n        } else {\r\n          throw new Error(res.msg)\r\n        }\r\n      } catch (error) {\r\n        console.log('打卡错误:', error)\r\n        uni.hideLoading()\r\n        \r\n        // 获取错误信息（兼容多种格式）\r\n        let errorMsg = ''\r\n        if (error.message) {\r\n          errorMsg = error.message\r\n        } else if (typeof error === 'string') {\r\n          errorMsg = error\r\n        } else if (error.msg) {\r\n          errorMsg = error.msg\r\n        } else {\r\n          errorMsg = '打卡失败'\r\n        }\r\n        \r\n        uni.showToast({\r\n          title: errorMsg,\r\n          icon: 'none',\r\n          duration: 3000\r\n        })\r\n      }\r\n    },\r\n\r\n    closeCheckinPopup() {\r\n      this.checkinPopup = false\r\n    },\r\n\r\n    isDateChecked(date) {\r\n      if (!this.stats.checkinDates) return false\r\n      return this.stats.checkinDates.some(checkedDate => \r\n        new Date(checkedDate).toDateString() === date.toDateString()\r\n      )\r\n    },\r\n\r\n    getModalIcon() {\r\n      if (this.todayStatus && this.todayStatus.record && this.todayStatus.record.checkinStatus === '2') {\r\n        return 'https://cos.ap-shanghai.myqcloud.com/foolishsister-1257000723/smzs/challenge/checkin-warning.png'\r\n      } else if (this.todayStatus && this.todayStatus.record && this.todayStatus.record.checkinStatus === '1') {\r\n        return this.isValidTime ? 'https://cos.ap-shanghai.myqcloud.com/foolishsister-1257000723/smzs/challenge/checkin-success.png' : 'https://cos.ap-shanghai.myqcloud.com/foolishsister-1257000723/smzs/challenge/checkin-warning.png'\r\n      }\r\n      return 'https://cos.ap-shanghai.myqcloud.com/foolishsister-1257000723/smzs/challenge/checkin-warning.png'\r\n    },\r\n\r\n    getModalTitle() {\r\n      if (this.todayStatus && this.todayStatus.record && this.todayStatus.record.checkinStatus === '2') {\r\n        return '非有效时间打卡'\r\n      } else if (this.todayStatus && this.todayStatus.record && this.todayStatus.record.checkinStatus === '1') {\r\n        return this.isValidTime ? '打卡成功！' : '非有效时间打卡'\r\n      }\r\n      return '非有效时间打卡'\r\n    },\r\n\r\n    getModalDescription() {\r\n      const timeRange = `${this.formatTime(this.todayStatus?.validTimeRange?.startTime || this.challenge.startTime)}-${this.formatTime(this.todayStatus?.validTimeRange?.endTime || this.challenge.endTime)}`;\r\n      if (this.todayStatus && this.todayStatus.record && this.todayStatus.record.checkinStatus === '2') {\r\n        return `\"错过${timeRange}，米没了！你连这都坚持不了还想翻身？开玩笑的，别哭，下次别摆烂了\"`\r\n      } else if (this.todayStatus && this.todayStatus.record && this.todayStatus.record.checkinStatus === '1') {\r\n        return this.isValidTime ? '\"你干得不错，比昨天的你强了不少\"' : `\"错过${timeRange}，米没了！你连这都坚持不了还想翻身？开玩笑的，别哭，下次别摆烂了\"`\r\n      }\r\n      return `\"错过${timeRange}，米没了！你连这都坚持不了还想翻身？开玩笑的，别哭，下次别摆烂了\"`\r\n    },\r\n\r\n    formatResourceUrls(urls) {\r\n      if (!urls) return [];\r\n      \r\n      // 如果urls是字符串，尝试解析为数组\r\n      let urlArray = urls;\r\n      if (typeof urls === 'string') {\r\n        try {\r\n          // 尝试解析为JSON\r\n          urlArray = JSON.parse(urls);\r\n        } catch (e) {\r\n          // 如果不是合法的JSON，可能是以逗号分隔的字符串\r\n          urlArray = urls.split(',');\r\n        }\r\n      }\r\n      \r\n      if (!Array.isArray(urlArray)) {\r\n        urlArray = [urlArray.toString()];\r\n      }\r\n      \r\n      return urlArray;\r\n    },\r\n\r\n    openResource(url) {\r\n      // 检查URL是否有http/https前缀，如果没有则添加\r\n      let formattedUrl = url;\r\n      if (!url.startsWith('http://') && !url.startsWith('https://')) {\r\n        formattedUrl = 'https://' + url;\r\n      }\r\n\r\n      uni.navigateTo({\r\n        url: `/pages/webview/index?url=${encodeURIComponent(formattedUrl)}`\r\n      });\r\n    },\r\n\r\n    // 获取任务分类\r\n    getTaskCategory(task) {\r\n      if (!task || !task.title) return '成长任务'\r\n\r\n      const title = task.title.toLowerCase()\r\n      if (title.includes('冥想') || title.includes('meditation')) return '冥想修行'\r\n      if (title.includes('运动') || title.includes('exercise')) return '运动健身'\r\n      if (title.includes('阅读') || title.includes('reading')) return '知识学习'\r\n      if (title.includes('反思') || title.includes('reflection')) return '自我反思'\r\n      if (title.includes('写作') || title.includes('writing')) return '创作表达'\r\n      if (title.includes('社交') || title.includes('social')) return '人际交往'\r\n\r\n      return '成长任务'\r\n    },\r\n\r\n    // 获取任务难度\r\n    getTaskDifficulty(dayNumber) {\r\n      if (dayNumber <= 7) return 1 // 第一周：简单\r\n      if (dayNumber <= 14) return 2 // 第二周：中等\r\n      return 3 // 第三周：困难\r\n    },\r\n\r\n    // 获取难度文本\r\n    getDifficultyText(dayNumber) {\r\n      const difficulty = this.getTaskDifficulty(dayNumber)\r\n      const texts = ['', '入门', '进阶', '挑战']\r\n      return texts[difficulty] || '入门'\r\n    },\r\n\r\n    // 获取激励标题\r\n    getMotivationTitle(dayNumber) {\r\n      const titles = [\r\n        '开始你的成长之旅！',\r\n        '坚持就是胜利！',\r\n        '你已经走了很远！',\r\n        '继续保持这个节奏！',\r\n        '你正在变得更好！',\r\n        '每一天都是新的开始！',\r\n        '第一周即将完成！',\r\n        '恭喜完成第一周！',\r\n        '进入第二阶段！',\r\n        '你的坚持令人敬佩！',\r\n        '已经过半了！',\r\n        '你比想象中更强大！',\r\n        '距离目标越来越近！',\r\n        '第二周即将完成！',\r\n        '进入最后冲刺阶段！',\r\n        '你已经证明了自己！',\r\n        '坚持到现在真不容易！',\r\n        '胜利就在眼前！',\r\n        '最后几天了！',\r\n        '明天就是最后一天！',\r\n        '恭喜完成21天挑战！'\r\n      ]\r\n      return titles[dayNumber - 1] || '继续加油！'\r\n    },\r\n\r\n    // 获取激励描述\r\n    getMotivationDesc(dayNumber) {\r\n      if (dayNumber <= 7) {\r\n        return '新习惯的养成需要时间，每一天的坚持都在为未来的自己投资。'\r\n      } else if (dayNumber <= 14) {\r\n        return '你已经度过了最困难的适应期，现在是建立稳定习惯的关键时期。'\r\n      } else {\r\n        return '你即将完成这个了不起的挑战，这份坚持将成为你人生的宝贵财富。'\r\n      }\r\n    },\r\n\r\n    // 获取进度里程碑\r\n    getProgressMilestones() {\r\n      const totalDays = this.challenge.durationDays || 21\r\n      return [\r\n        { day: 1, text: '开始', position: '0%' },\r\n        { day: Math.floor(totalDays / 3), text: '1/3', position: '33.33%' },\r\n        { day: Math.floor(totalDays * 2 / 3), text: '2/3', position: '66.66%' },\r\n        { day: totalDays, text: '完成', position: '100%' }\r\n      ]\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* 现代化页面容器 */\r\n.page-container.modern-design {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg,\r\n    #667eea 0%,\r\n    #764ba2 25%,\r\n    #f093fb 50%,\r\n    #f5576c 75%,\r\n    #4facfe 100%);\r\n  position: relative;\r\n  overflow: hidden;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background:\r\n      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\r\n      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\r\n      radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);\r\n    z-index: 0;\r\n  }\r\n\r\n  &::after {\r\n    content: '';\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: rgba(255, 255, 255, 0.05);\r\n    backdrop-filter: blur(1px);\r\n    z-index: 0;\r\n  }\r\n}\r\n\r\n/* 现代化头部 */\r\n.modern-header {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 120rpx;\r\n  z-index: 100;\r\n  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\r\n\r\n  &.scrolled {\r\n    height: 100rpx;\r\n\r\n    .header-content {\r\n      padding: 0 32rpx;\r\n    }\r\n  }\r\n\r\n  .header-bg {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    overflow: hidden;\r\n\r\n    .bg-gradient {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      bottom: 0;\r\n      background: linear-gradient(135deg,\r\n        rgba(138, 43, 226, 0.9) 0%,\r\n        rgba(30, 144, 255, 0.9) 50%,\r\n        rgba(255, 20, 147, 0.9) 100%);\r\n      backdrop-filter: blur(20rpx);\r\n      -webkit-backdrop-filter: blur(20rpx);\r\n    }\r\n\r\n    .bg-particles {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      bottom: 0;\r\n\r\n      .particle {\r\n        position: absolute;\r\n        width: 8rpx;\r\n        height: 8rpx;\r\n        background: rgba(255, 255, 255, 0.6);\r\n        border-radius: 50%;\r\n        animation: float 3s ease-in-out infinite;\r\n      }\r\n    }\r\n  }\r\n\r\n  .header-content {\r\n    position: relative;\r\n    z-index: 2;\r\n    height: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 0 40rpx;\r\n    padding-top: var(--status-bar-height, 20px);\r\n\r\n    .back-button {\r\n      width: 80rpx;\r\n      height: 80rpx;\r\n      border-radius: 40rpx;\r\n      background: rgba(255, 255, 255, 0.2);\r\n      backdrop-filter: blur(10rpx);\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      transition: all 0.3s ease;\r\n\r\n      &:active {\r\n        transform: scale(0.9);\r\n        background: rgba(255, 255, 255, 0.3);\r\n      }\r\n\r\n      .back-icon {\r\n        font-size: 36rpx;\r\n        color: #fff;\r\n        font-weight: bold;\r\n      }\r\n    }\r\n\r\n    .header-title-area {\r\n      flex: 1;\r\n      text-align: center;\r\n      position: relative;\r\n\r\n      .header-title {\r\n        font-size: 36rpx;\r\n        font-weight: 700;\r\n        color: #fff;\r\n        opacity: 0;\r\n        transform: translateY(10rpx);\r\n        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\r\n\r\n        &.show {\r\n          opacity: 1;\r\n          transform: translateY(0);\r\n        }\r\n      }\r\n\r\n      .title-decoration {\r\n        position: absolute;\r\n        bottom: -10rpx;\r\n        left: 50%;\r\n        transform: translateX(-50%);\r\n        width: 60rpx;\r\n        height: 4rpx;\r\n\r\n        .decoration-line {\r\n          width: 100%;\r\n          height: 100%;\r\n          background: linear-gradient(90deg, transparent, #fff, transparent);\r\n          border-radius: 2rpx;\r\n          opacity: 0;\r\n          animation: slideIn 0.5s ease 0.3s forwards;\r\n        }\r\n      }\r\n    }\r\n\r\n    .header-actions {\r\n      width: 80rpx;\r\n      display: flex;\r\n      justify-content: flex-end;\r\n\r\n      .action-btn {\r\n        width: 80rpx;\r\n        height: 80rpx;\r\n        border-radius: 40rpx;\r\n        background: rgba(255, 255, 255, 0.2);\r\n        backdrop-filter: blur(10rpx);\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        transition: all 0.3s ease;\r\n\r\n        &:active {\r\n          transform: scale(0.9);\r\n          background: rgba(255, 255, 255, 0.3);\r\n        }\r\n\r\n        .iconfont {\r\n          font-size: 32rpx;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 现代化滚动内容 */\r\n.scroll-content.modern-scroll {\r\n  height: 100vh;\r\n  box-sizing: border-box;\r\n  padding: 32rpx;\r\n  position: relative;\r\n  z-index: 1;\r\n  -webkit-overflow-scrolling: touch;\r\n  scroll-behavior: smooth;\r\n}\r\n\r\n/* 现代化今日打卡卡片 */\r\n.today-card.modern-card {\r\n  background: rgba(255, 255, 255, 0.15);\r\n  backdrop-filter: blur(30rpx);\r\n  -webkit-backdrop-filter: blur(30rpx);\r\n  border-radius: 40rpx;\r\n  padding: 48rpx;\r\n  margin-bottom: 40rpx;\r\n  position: relative;\r\n  overflow: hidden;\r\n  border: 2rpx solid rgba(255, 255, 255, 0.2);\r\n  box-shadow:\r\n    0 20rpx 60rpx rgba(0, 0, 0, 0.1),\r\n    0 8rpx 32rpx rgba(138, 43, 226, 0.15),\r\n    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);\r\n  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\r\n\r\n  &:hover {\r\n    transform: translateY(-8rpx);\r\n    box-shadow:\r\n      0 32rpx 80rpx rgba(0, 0, 0, 0.15),\r\n      0 16rpx 48rpx rgba(138, 43, 226, 0.2),\r\n      inset 0 1rpx 0 rgba(255, 255, 255, 0.4);\r\n  }\r\n\r\n  &.completed {\r\n    background: rgba(76, 175, 80, 0.15);\r\n    border-color: rgba(76, 175, 80, 0.3);\r\n\r\n    .card-bg-effects .gradient-orb {\r\n      background: radial-gradient(circle, rgba(76, 175, 80, 0.3) 0%, transparent 70%);\r\n    }\r\n  }\r\n\r\n  .card-bg-effects {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    z-index: 0;\r\n\r\n    .gradient-orb {\r\n      position: absolute;\r\n      border-radius: 50%;\r\n      filter: blur(40rpx);\r\n      animation: float 6s ease-in-out infinite;\r\n\r\n      &.orb-1 {\r\n        width: 200rpx;\r\n        height: 200rpx;\r\n        top: -50rpx;\r\n        right: -50rpx;\r\n        background: radial-gradient(circle, rgba(255, 20, 147, 0.3) 0%, transparent 70%);\r\n        animation-delay: 0s;\r\n      }\r\n\r\n      &.orb-2 {\r\n        width: 150rpx;\r\n        height: 150rpx;\r\n        bottom: -30rpx;\r\n        left: -30rpx;\r\n        background: radial-gradient(circle, rgba(30, 144, 255, 0.3) 0%, transparent 70%);\r\n        animation-delay: 3s;\r\n      }\r\n    }\r\n\r\n    .floating-shapes {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      bottom: 0;\r\n\r\n      .shape {\r\n        position: absolute;\r\n        background: rgba(255, 255, 255, 0.1);\r\n        animation: floatShape 8s ease-in-out infinite;\r\n\r\n        &.shape-1 {\r\n          width: 20rpx;\r\n          height: 20rpx;\r\n          border-radius: 50%;\r\n          top: 20%;\r\n          left: 10%;\r\n          animation-delay: 0s;\r\n        }\r\n\r\n        &.shape-2 {\r\n          width: 16rpx;\r\n          height: 16rpx;\r\n          border-radius: 4rpx;\r\n          top: 60%;\r\n          right: 15%;\r\n          animation-delay: 2s;\r\n        }\r\n\r\n        &.shape-3 {\r\n          width: 12rpx;\r\n          height: 12rpx;\r\n          border-radius: 50%;\r\n          bottom: 30%;\r\n          left: 20%;\r\n          animation-delay: 4s;\r\n        }\r\n\r\n        &.shape-4 {\r\n          width: 18rpx;\r\n          height: 18rpx;\r\n          border-radius: 6rpx;\r\n          top: 40%;\r\n          right: 30%;\r\n          animation-delay: 6s;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  /* 今日头部区域 */\r\n  .today-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 32rpx;\r\n    position: relative;\r\n    z-index: 1;\r\n\r\n    .today-badge {\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 16rpx 24rpx;\r\n      background: rgba(255, 255, 255, 0.2);\r\n      border-radius: 50rpx;\r\n      backdrop-filter: blur(10rpx);\r\n\r\n      .badge-emoji {\r\n        font-size: 32rpx;\r\n        margin-right: 12rpx;\r\n      }\r\n\r\n      .badge-text {\r\n        font-size: 28rpx;\r\n        font-weight: 600;\r\n        color: #fff;\r\n      }\r\n    }\r\n\r\n    .today-date-modern {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .date-text {\r\n        font-size: 32rpx;\r\n        font-weight: 700;\r\n        color: #fff;\r\n        margin-right: 16rpx;\r\n      }\r\n\r\n      .date-indicator {\r\n        width: 16rpx;\r\n        height: 16rpx;\r\n        border-radius: 50%;\r\n        background: rgba(255, 255, 255, 0.5);\r\n        animation: pulse 2s ease-in-out infinite;\r\n\r\n        &.status-success {\r\n          background: #4CAF50;\r\n        }\r\n\r\n        &.status-warning {\r\n          background: #FF9800;\r\n        }\r\n\r\n        &.status-failed {\r\n          background: #F44336;\r\n        }\r\n\r\n        &.status-pending {\r\n          background: #2196F3;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  /* 主要内容区域 */\r\n  .today-main-content {\r\n    position: relative;\r\n    z-index: 1;\r\n\r\n    .status-display {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: 32rpx;\r\n      padding: 24rpx;\r\n      background: rgba(255, 255, 255, 0.1);\r\n      border-radius: 24rpx;\r\n      backdrop-filter: blur(10rpx);\r\n\r\n      .status-icon {\r\n        width: 80rpx;\r\n        height: 80rpx;\r\n        border-radius: 40rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        margin-right: 24rpx;\r\n        background: rgba(255, 255, 255, 0.2);\r\n\r\n        &.status-success {\r\n          background: linear-gradient(135deg, #4CAF50, #66BB6A);\r\n        }\r\n\r\n        &.status-warning {\r\n          background: linear-gradient(135deg, #FF9800, #FFB74D);\r\n        }\r\n\r\n        &.status-failed {\r\n          background: linear-gradient(135deg, #F44336, #EF5350);\r\n        }\r\n\r\n        &.status-pending {\r\n          background: linear-gradient(135deg, #2196F3, #42A5F5);\r\n        }\r\n\r\n        .status-emoji {\r\n          font-size: 40rpx;\r\n        }\r\n      }\r\n\r\n      .status-info {\r\n        flex: 1;\r\n\r\n        .status-title {\r\n          font-size: 32rpx;\r\n          font-weight: 700;\r\n          color: #fff;\r\n          margin-bottom: 8rpx;\r\n          display: block;\r\n        }\r\n\r\n        .status-subtitle {\r\n          font-size: 24rpx;\r\n          color: rgba(255, 255, 255, 0.8);\r\n          display: block;\r\n        }\r\n      }\r\n    }\r\n\r\n    .time-info-modern {\r\n      margin-bottom: 24rpx;\r\n      padding: 20rpx 24rpx;\r\n      background: rgba(255, 255, 255, 0.08);\r\n      border-radius: 20rpx;\r\n      border: 1rpx solid rgba(255, 255, 255, 0.1);\r\n\r\n      .time-label {\r\n        display: flex;\r\n        align-items: center;\r\n        margin-bottom: 8rpx;\r\n\r\n        .time-icon {\r\n          font-size: 24rpx;\r\n          margin-right: 8rpx;\r\n        }\r\n\r\n        .time-text {\r\n          font-size: 24rpx;\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n\r\n      .time-range {\r\n        font-size: 28rpx;\r\n        font-weight: 600;\r\n        color: #fff;\r\n      }\r\n    }\r\n\r\n    .checkin-result-modern {\r\n      .result-item {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        padding: 16rpx 24rpx;\r\n        margin-bottom: 12rpx;\r\n        background: rgba(255, 255, 255, 0.08);\r\n        border-radius: 16rpx;\r\n\r\n        .result-label {\r\n          font-size: 24rpx;\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n\r\n        .result-value {\r\n          font-size: 26rpx;\r\n          font-weight: 600;\r\n          color: #fff;\r\n        }\r\n\r\n        &.refund-info {\r\n          .refund-status {\r\n            padding: 8rpx 16rpx;\r\n            border-radius: 20rpx;\r\n            font-size: 22rpx;\r\n\r\n            &.refund-pending {\r\n              background: rgba(255, 152, 0, 0.2);\r\n              color: #FF9800;\r\n            }\r\n\r\n            &.refund-success {\r\n              background: rgba(76, 175, 80, 0.2);\r\n              color: #4CAF50;\r\n            }\r\n\r\n            &.refund-failed {\r\n              background: rgba(244, 67, 54, 0.2);\r\n              color: #F44336;\r\n            }\r\n\r\n            &.refund-processing {\r\n              background: rgba(33, 150, 243, 0.2);\r\n              color: #2196F3;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .time-range-info {\r\n    margin-bottom: 20rpx;\r\n    position: relative;\r\n    z-index: 1;\r\n\r\n    .time-range-text {\r\n      font-size: 26rpx;\r\n      color: #666;\r\n    }\r\n  }\r\n  \r\n  .checkin-result-info {\r\n    margin-bottom: 30rpx;\r\n    padding: 20rpx;\r\n    background: #F9F9F9;\r\n    border-radius: 16rpx;\r\n    font-size: 26rpx;\r\n    \r\n    .checkin-time {\r\n      margin-bottom: 10rpx;\r\n      color: #333;\r\n    }\r\n    \r\n    .checkin-status {\r\n      display: inline-block;\r\n      padding: 4rpx 16rpx;\r\n      border-radius: 20rpx;\r\n      margin-bottom: 10rpx;\r\n      font-size: 24rpx;\r\n      \r\n      &.valid {\r\n        background: rgba(39, 174, 96, 0.1);\r\n        color: #27AE60;\r\n      }\r\n      \r\n      &.invalid {\r\n        background: rgba(235, 87, 87, 0.1);\r\n        color: #EB5757;\r\n      }\r\n    }\r\n    \r\n    .energy-info {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      color: #666;\r\n      \r\n      .refund-pending {\r\n        color: #FF9F1C;\r\n      }\r\n      \r\n      .refund-done {\r\n        color: #27AE60;\r\n      }\r\n      \r\n      .refund-failed {\r\n        color: #EB5757;\r\n      }\r\n      \r\n      .refund-processing {\r\n        color: #2F80ED;\r\n      }\r\n      \r\n      .refund-none {\r\n        color: #999;\r\n      }\r\n    }\r\n  }\r\n\r\n  .today-action {\r\n    position: relative;\r\n    z-index: 1;\r\n    \r\n    .checkin-btn {\r\n      width: 100%;\r\n      height: 96rpx;\r\n      background: linear-gradient(135deg, #FFC72C, #FFB300);\r\n      border-radius: 48rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      font-size: 32rpx;\r\n      color: #fff;\r\n      border: none;\r\n      box-shadow: 0 4rpx 12rpx rgba(255, 199, 44, 0.15);\r\n      transition: all 0.3s ease;\r\n\r\n      &:active {\r\n        transform: scale(0.98);\r\n        box-shadow: 0 2rpx 8rpx rgba(255, 199, 44, 0.1);\r\n      }\r\n\r\n      .checkin-icon {\r\n        width: 48rpx;\r\n        height: 48rpx;\r\n        margin-right: 16rpx;\r\n      }\r\n    }\r\n    .checkin-btn::after {\r\n      border: none !important;\r\n    }\r\n  }\r\n}\r\n\r\n.daily-task-card {\r\n  background: #fff;\r\n  border-radius: 32rpx;\r\n  margin-bottom: 30rpx;\r\n  box-shadow: 0 8rpx 32rpx rgba(255, 199, 44, 0.15);\r\n  overflow: hidden;\r\n  backdrop-filter: blur(20rpx);\r\n  -webkit-backdrop-filter: blur(20rpx);\r\n  border: 1rpx solid rgba(255, 255, 255, 0.2);\r\n\r\n  /* 添加微妙的动画效果 */\r\n  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\r\n\r\n  &:hover {\r\n    transform: translateY(-4rpx);\r\n    box-shadow: 0 12rpx 40rpx rgba(255, 199, 44, 0.2);\r\n  }\r\n\r\n  /* 任务头部样式 */\r\n  .task-header {\r\n    position: relative;\r\n    padding: 40rpx;\r\n    background: linear-gradient(135deg, #FFC72C 0%, #FFB300 100%);\r\n    overflow: hidden;\r\n\r\n    .task-header-bg {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      bottom: 0;\r\n\r\n      .header-wave {\r\n        position: absolute;\r\n        width: 200%;\r\n        height: 100%;\r\n        background: rgba(255, 255, 255, 0.1);\r\n\r\n        &.wave1 {\r\n          top: -50%;\r\n          left: -50%;\r\n          border-radius: 45%;\r\n          animation: wave1 6s ease-in-out infinite;\r\n        }\r\n\r\n        &.wave2 {\r\n          top: -30%;\r\n          right: -50%;\r\n          border-radius: 40%;\r\n          animation: wave2 8s ease-in-out infinite reverse;\r\n        }\r\n      }\r\n    }\r\n\r\n    .task-header-content {\r\n      position: relative;\r\n      z-index: 2;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .day-circle {\r\n        width: 120rpx;\r\n        height: 120rpx;\r\n        background: rgba(255, 255, 255, 0.2);\r\n        border-radius: 60rpx;\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        justify-content: center;\r\n        margin-right: 24rpx;\r\n        backdrop-filter: blur(10rpx);\r\n        border: 2rpx solid rgba(255, 255, 255, 0.3);\r\n\r\n        .day-number {\r\n          font-size: 48rpx;\r\n          font-weight: 700;\r\n          color: #fff;\r\n          line-height: 1;\r\n        }\r\n\r\n        .day-text {\r\n          font-size: 20rpx;\r\n          color: rgba(255, 255, 255, 0.8);\r\n          font-weight: 500;\r\n          margin-top: 4rpx;\r\n        }\r\n      }\r\n\r\n      .task-header-info {\r\n        flex: 1;\r\n\r\n        .task-category {\r\n          font-size: 24rpx;\r\n          color: rgba(255, 255, 255, 0.8);\r\n          margin-bottom: 8rpx;\r\n          display: block;\r\n        }\r\n\r\n        .task-main-title {\r\n          font-size: 36rpx;\r\n          font-weight: 700;\r\n          color: #fff;\r\n          margin-bottom: 12rpx;\r\n          display: block;\r\n          line-height: 1.3;\r\n        }\r\n\r\n        .task-difficulty-indicator {\r\n          display: flex;\r\n          align-items: center;\r\n\r\n          .difficulty-star {\r\n            font-size: 20rpx;\r\n            margin-right: 4rpx;\r\n            opacity: 0.4;\r\n            transition: opacity 0.3s ease;\r\n\r\n            &.active {\r\n              opacity: 1;\r\n            }\r\n          }\r\n\r\n          .difficulty-text {\r\n            font-size: 22rpx;\r\n            color: rgba(255, 255, 255, 0.9);\r\n            margin-left: 8rpx;\r\n            font-weight: 500;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  /* 任务描述区域 */\r\n  .task-description-section {\r\n    padding: 32rpx 40rpx;\r\n    display: flex;\r\n    align-items: flex-start;\r\n\r\n    .description-icon {\r\n      width: 48rpx;\r\n      height: 48rpx;\r\n      margin-right: 16rpx;\r\n      margin-top: 4rpx;\r\n\r\n      .icon {\r\n        width: 100%;\r\n        height: 100%;\r\n      }\r\n    }\r\n\r\n    .description-content {\r\n      flex: 1;\r\n\r\n      .description-title {\r\n        font-size: 28rpx;\r\n        font-weight: 600;\r\n        color: #333;\r\n        margin-bottom: 12rpx;\r\n        display: block;\r\n      }\r\n\r\n      .description-text {\r\n        font-size: 26rpx;\r\n        color: #666;\r\n        line-height: 1.6;\r\n        display: block;\r\n      }\r\n    }\r\n  }\r\n\r\n  .task-section {\r\n    margin-bottom: 20rpx;\r\n\r\n    &.enhanced {\r\n      padding: 32rpx 40rpx;\r\n      margin-bottom: 24rpx;\r\n      border-radius: 24rpx;\r\n      background: linear-gradient(135deg, rgba(255, 199, 44, 0.02) 0%, rgba(255, 179, 0, 0.05) 100%);\r\n      border: 1rpx solid rgba(255, 199, 44, 0.1);\r\n      transition: all 0.3s ease;\r\n\r\n      &:hover {\r\n        transform: translateY(-2rpx);\r\n        box-shadow: 0 8rpx 24rpx rgba(255, 199, 44, 0.1);\r\n      }\r\n    }\r\n\r\n    .section-header {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      margin-bottom: 16rpx;\r\n\r\n      .section-icon-wrapper {\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 12rpx;\r\n        border-radius: 16rpx;\r\n        background: rgba(255, 199, 44, 0.1);\r\n        margin-right: 16rpx;\r\n\r\n        &.tips {\r\n          background: rgba(52, 152, 219, 0.1);\r\n        }\r\n\r\n        &.resources {\r\n          background: rgba(155, 89, 182, 0.1);\r\n        }\r\n      }\r\n\r\n      .section-icon {\r\n        width: 32rpx;\r\n        height: 32rpx;\r\n      }\r\n\r\n      .section-title {\r\n        font-size: 28rpx;\r\n        font-weight: 600;\r\n        color: #333;\r\n        flex: 1;\r\n      }\r\n\r\n      .section-badge {\r\n        padding: 8rpx 16rpx;\r\n        border-radius: 20rpx;\r\n        font-size: 20rpx;\r\n        font-weight: 500;\r\n        background: rgba(255, 199, 44, 0.2);\r\n        color: #FFC72C;\r\n\r\n        &.tips {\r\n          background: rgba(52, 152, 219, 0.2);\r\n          color: #3498DB;\r\n        }\r\n\r\n        &.resources {\r\n          background: rgba(155, 89, 182, 0.2);\r\n          color: #9B59B6;\r\n        }\r\n      }\r\n    }\r\n\r\n    .section-content {\r\n      font-size: 26rpx;\r\n      color: #333;\r\n      line-height: 1.6;\r\n\r\n      &.tips {\r\n        background: rgba(52, 152, 219, 0.05);\r\n        padding: 24rpx;\r\n        border-radius: 16rpx;\r\n        border-left: 4rpx solid #3498DB;\r\n      }\r\n\r\n      &.resources {\r\n        display: flex;\r\n        flex-direction: column;\r\n        gap: 16rpx;\r\n      }\r\n    }\r\n  }\r\n\r\n  .task-completion-status {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 12rpx 24rpx;\r\n    border-radius: 20rpx;\r\n    background: rgba(39, 174, 96, 0.1);\r\n    color: #27AE60;\r\n    margin-top: 20rpx;\r\n\r\n    .status-icon {\r\n      width: 48rpx;\r\n      height: 48rpx;\r\n      margin-right: 12rpx;\r\n    }\r\n\r\n    text {\r\n      font-size: 26rpx;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n\r\n  /* 增强的进度指示器样式 */\r\n  .task-progress-section.enhanced {\r\n    padding: 32rpx 40rpx;\r\n    background: linear-gradient(135deg, rgba(255, 199, 44, 0.02) 0%, rgba(255, 179, 0, 0.05) 100%);\r\n    border-radius: 24rpx;\r\n    border: 1rpx solid rgba(255, 199, 44, 0.1);\r\n\r\n    .progress-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 24rpx;\r\n\r\n      .progress-title-wrapper {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .progress-icon {\r\n          width: 32rpx;\r\n          height: 32rpx;\r\n          margin-right: 12rpx;\r\n        }\r\n\r\n        .progress-title {\r\n          font-size: 28rpx;\r\n          font-weight: 600;\r\n          color: #333;\r\n        }\r\n      }\r\n\r\n      .progress-value-wrapper {\r\n        display: flex;\r\n        align-items: baseline;\r\n\r\n        .progress-value {\r\n          font-size: 36rpx;\r\n          font-weight: 700;\r\n          color: #FFC72C;\r\n        }\r\n\r\n        .progress-separator {\r\n          font-size: 24rpx;\r\n          color: #999;\r\n          margin: 0 8rpx;\r\n        }\r\n\r\n        .progress-total {\r\n          font-size: 28rpx;\r\n          color: #666;\r\n        }\r\n      }\r\n    }\r\n\r\n    .progress-track.enhanced {\r\n      height: 16rpx;\r\n      background: rgba(255, 199, 44, 0.1);\r\n      border-radius: 8rpx;\r\n      position: relative;\r\n      overflow: hidden;\r\n\r\n      .progress-fill.enhanced {\r\n        height: 100%;\r\n        background: linear-gradient(90deg, #FFC72C 0%, #FFB300 50%, #FFA000 100%);\r\n        border-radius: 8rpx;\r\n        position: relative;\r\n        transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);\r\n\r\n        .progress-shine {\r\n          position: absolute;\r\n          top: 0;\r\n          left: -100%;\r\n          width: 100%;\r\n          height: 100%;\r\n          background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);\r\n          animation: shine 2s infinite;\r\n        }\r\n      }\r\n\r\n      .progress-marker.enhanced {\r\n        position: absolute;\r\n        top: 50%;\r\n        transform: translate(-50%, -50%);\r\n        transition: left 0.8s cubic-bezier(0.4, 0, 0.2, 1);\r\n\r\n        .marker-dot.enhanced {\r\n          width: 24rpx;\r\n          height: 24rpx;\r\n          background: #FFC72C;\r\n          border-radius: 50%;\r\n          border: 4rpx solid #fff;\r\n          box-shadow: 0 4rpx 12rpx rgba(255, 199, 44, 0.3);\r\n          position: relative;\r\n\r\n          .marker-pulse {\r\n            position: absolute;\r\n            top: 50%;\r\n            left: 50%;\r\n            transform: translate(-50%, -50%);\r\n            width: 100%;\r\n            height: 100%;\r\n            background: rgba(255, 199, 44, 0.3);\r\n            border-radius: 50%;\r\n            animation: pulse 2s infinite;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .progress-milestones.enhanced {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      margin-top: 16rpx;\r\n      position: relative;\r\n\r\n      .milestone.enhanced {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        position: relative;\r\n\r\n        &.completed {\r\n          .milestone-dot.enhanced {\r\n            background: #FFC72C;\r\n            border-color: #FFB300;\r\n          }\r\n\r\n          .milestone-text.enhanced {\r\n            color: #FFC72C;\r\n            font-weight: 600;\r\n          }\r\n        }\r\n\r\n        .milestone-dot.enhanced {\r\n          width: 16rpx;\r\n          height: 16rpx;\r\n          background: #E0E0E0;\r\n          border: 2rpx solid #BDBDBD;\r\n          border-radius: 50%;\r\n          margin-bottom: 8rpx;\r\n          transition: all 0.3s ease;\r\n        }\r\n\r\n        .milestone-text.enhanced {\r\n          font-size: 20rpx;\r\n          color: #999;\r\n          transition: all 0.3s ease;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.challenge-card {\r\n  background: #fff;\r\n  border-radius: 32rpx;\r\n  padding: 40rpx;\r\n  margin-bottom: 30rpx;\r\n  box-shadow: 0 8rpx 32rpx rgba(255, 199, 44, 0.08);\r\n\r\n  .challenge-header {\r\n    margin-bottom: 40rpx;\r\n\r\n    .challenge-title {\r\n      font-size: 36rpx;\r\n      font-weight: 600;\r\n      background: linear-gradient(135deg, #FFC72C, #FFB300);\r\n      -webkit-background-clip: text;\r\n      -webkit-text-fill-color: transparent;\r\n      margin-bottom: 12rpx;\r\n      display: block;\r\n    }\r\n\r\n    .challenge-period {\r\n      font-size: 26rpx;\r\n      color: #666;\r\n    }\r\n  }\r\n\r\n  .progress-bar {\r\n    .progress-info {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      margin-bottom: 16rpx;\r\n\r\n      .progress-label {\r\n        font-size: 26rpx;\r\n        color: #666;\r\n      }\r\n\r\n      .progress-value {\r\n        font-size: 26rpx;\r\n        color: #333;\r\n        font-weight: 500;\r\n      }\r\n    }\r\n\r\n    .progress-track {\r\n      height: 20rpx;\r\n      background: #FFF8F0;\r\n      border-radius: 10rpx;\r\n      overflow: hidden;\r\n\r\n      .progress-fill {\r\n        height: 100%;\r\n        background: linear-gradient(90deg, #FFC72C, #FFB300);\r\n        border-radius: 10rpx;\r\n        transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.stats-section {\r\n  margin-bottom: 40rpx;\r\n\r\n  .section-title {\r\n    font-size: 36rpx;\r\n    font-weight: 600;\r\n    color: #333;\r\n    margin-bottom: 24rpx;\r\n    display: block;\r\n  }\r\n\r\n  .stats-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(2, 1fr);\r\n    gap: 24rpx;\r\n  }\r\n\r\n  .stats-item {\r\n    background: #fff;\r\n    border-radius: 32rpx;\r\n    padding: 40rpx 30rpx;\r\n    text-align: center;\r\n    box-shadow: 0 8rpx 32rpx rgba(255, 199, 44, 0.08);\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      transform: translateY(-4rpx);\r\n      box-shadow: 0 12rpx 36rpx rgba(255, 199, 44, 0.12);\r\n    }\r\n\r\n    .stats-icon {\r\n      width: 96rpx;\r\n      height: 96rpx;\r\n      border-radius: 48rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      margin: 0 auto 24rpx;\r\n      background: #FFF8F0;\r\n      \r\n      image {\r\n        width: 48rpx;\r\n        height: 48rpx;\r\n      }\r\n    }\r\n\r\n    .stats-value {\r\n      font-size: 44rpx;\r\n      font-weight: 600;\r\n      background: linear-gradient(135deg, #FFC72C, #FFB300);\r\n      -webkit-background-clip: text;\r\n      -webkit-text-fill-color: transparent;\r\n      margin-bottom: 12rpx;\r\n      display: block;\r\n    }\r\n\r\n    .stats-label {\r\n      font-size: 26rpx;\r\n      color: #666;\r\n    }\r\n  }\r\n}\r\n\r\n.calendar-section {\r\n  margin-bottom: 40rpx;\r\n\r\n  .section-title {\r\n    font-size: 36rpx;\r\n    font-weight: 600;\r\n    color: #333;\r\n    margin-bottom: 24rpx;\r\n    display: block;\r\n  }\r\n\r\n  .calendar-card {\r\n    background: #fff;\r\n    border-radius: 32rpx;\r\n    padding: 30rpx 20rpx;\r\n    box-shadow: 0 8rpx 32rpx rgba(255, 199, 44, 0.08);\r\n\r\n    .weekdays {\r\n      display: grid;\r\n      grid-template-columns: repeat(7, 1fr);\r\n      margin-bottom: 20rpx;\r\n      text-align: center;\r\n\r\n      text {\r\n        font-size: 24rpx;\r\n        color: #999;\r\n        text-align: center;\r\n        font-weight: 500;\r\n      }\r\n    }\r\n\r\n    .days-grid {\r\n      display: grid;\r\n      grid-template-columns: repeat(7, 1fr);\r\n      gap: 8rpx;\r\n      padding: 0;\r\n\r\n      .day-item {\r\n        aspect-ratio: 1;\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        justify-content: center;\r\n        position: relative;\r\n        border-radius: 12rpx;\r\n        min-height: 76rpx;\r\n        transition: all 0.3s ease;\r\n        background: #FFF8F0;\r\n        border: none;\r\n\r\n        &.placeholder {\r\n          background: transparent;\r\n          box-shadow: none;\r\n          pointer-events: none;\r\n        }\r\n\r\n        &.checked {\r\n          background: linear-gradient(135deg, #FFC72C, #FFB300);\r\n          transform: translateY(-2rpx);\r\n          box-shadow: 0 8rpx 16rpx rgba(255, 199, 44, 0.2);\r\n\r\n          .day-text, .month-text {\r\n            color: #fff;\r\n          }\r\n\r\n          .month-text {\r\n            position: absolute;\r\n            top: -6rpx;\r\n            right: -6rpx;\r\n            background: rgba(255, 255, 255, 0.9);\r\n            color: #FFC72C;\r\n            font-size: 16rpx;\r\n            padding: 4rpx 8rpx;\r\n            border-radius: 6rpx;\r\n            margin: 0;\r\n          }\r\n\r\n          .check-mark {\r\n            position: absolute;\r\n            bottom: 8rpx;\r\n            width: 28rpx;\r\n            height: 28rpx;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n\r\n            image {\r\n              width: 24rpx;\r\n              height: 24rpx;\r\n            }\r\n          }\r\n        }\r\n        \r\n        &.invalid-time {\r\n          background: linear-gradient(135deg, #FF9F1C, #F76E11);\r\n          \r\n          &::after {\r\n            content: '非效';\r\n            position: absolute;\r\n            top: -6rpx;\r\n            right: -6rpx;\r\n            background: rgba(255, 255, 255, 0.9);\r\n            color: #F76E11;\r\n            font-size: 16rpx;\r\n            padding: 4rpx 8rpx;\r\n            border-radius: 6rpx;\r\n          }\r\n        }\r\n\r\n        &.failed {\r\n          background: #FFF5F5;\r\n\r\n          .day-text {\r\n            color: #FF5252;\r\n          }\r\n\r\n          .month-text {\r\n            position: absolute;\r\n            top: -6rpx;\r\n            right: -6rpx;\r\n            background: rgba(255, 82, 82, 0.1);\r\n            color: #FF5252;\r\n            font-size: 16rpx;\r\n            padding: 4rpx 8rpx;\r\n            border-radius: 6rpx;\r\n            margin: 0;\r\n          }\r\n        }\r\n\r\n        &.today {\r\n          background: #FFF8F0;\r\n          border: 2rpx solid #FFC72C;\r\n          transform: scale(1.05);\r\n\r\n          .day-text {\r\n            color: #FFC72C;\r\n            font-weight: 600;\r\n          }\r\n\r\n          &::after {\r\n            content: '今日';\r\n            position: absolute;\r\n            top: -6rpx;\r\n            right: -6rpx;\r\n            background: #FFC72C;\r\n            color: #fff;\r\n            font-size: 16rpx;\r\n            padding: 4rpx 8rpx;\r\n            border-radius: 6rpx;\r\n            transform: scale(0.8);\r\n          }\r\n        }\r\n\r\n        &.in-period {\r\n          &:not(.checked):not(.today):not(.failed) {\r\n            &:hover {\r\n              background: #FFF2E6;\r\n              transform: translateY(-2rpx);\r\n            }\r\n\r\n            .month-text {\r\n              position: absolute;\r\n              top: -6rpx;\r\n              right: -6rpx;\r\n              background: rgba(255, 199, 44, 0.1);\r\n              color: #FFC72C;\r\n              font-size: 16rpx;\r\n              padding: 4rpx 8rpx;\r\n              border-radius: 6rpx;\r\n              margin: 0;\r\n            }\r\n          }\r\n        }\r\n\r\n        &:not(.in-period) {\r\n          background: #F8F8F8;\r\n          opacity: 0.5;\r\n          pointer-events: none;\r\n\r\n          .day-text, .month-text {\r\n            color: #999;\r\n          }\r\n        }\r\n\r\n        .day-text {\r\n          font-size: 28rpx;\r\n          line-height: 1.2;\r\n          font-weight: 500;\r\n          color: #333;\r\n        }\r\n\r\n        .month-text {\r\n          font-size: 20rpx;\r\n          color: #666;\r\n          margin-top: 4rpx;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.time-section {\r\n  margin-bottom: 40rpx;\r\n\r\n  .section-title {\r\n    font-size: 36rpx;\r\n    font-weight: 600;\r\n    color: #333;\r\n    margin-bottom: 24rpx;\r\n    display: block;\r\n  }\r\n\r\n  .time-card {\r\n    background: #fff;\r\n    border-radius: 32rpx;\r\n    padding: 40rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    box-shadow: 0 8rpx 32rpx rgba(255, 199, 44, 0.08);\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      transform: translateY(-4rpx);\r\n      box-shadow: 0 12rpx 36rpx rgba(255, 199, 44, 0.12);\r\n    }\r\n\r\n    .time-icon {\r\n      width: 96rpx;\r\n      height: 96rpx;\r\n      border-radius: 48rpx;\r\n      background: #FFF8F0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      margin-right: 24rpx;\r\n      \r\n      image {\r\n        width: 48rpx;\r\n        height: 48rpx;\r\n      }\r\n    }\r\n\r\n    .time-info {\r\n      flex: 1;\r\n\r\n      .time-value {\r\n        font-size: 36rpx;\r\n        font-weight: 600;\r\n        background: linear-gradient(135deg, #FFC72C, #FFB300);\r\n        -webkit-background-clip: text;\r\n        -webkit-text-fill-color: transparent;\r\n        margin-bottom: 12rpx;\r\n        display: block;\r\n      }\r\n\r\n      .time-label {\r\n        font-size: 26rpx;\r\n        color: #666;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.modal-mask {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.6);\r\n  backdrop-filter: blur(5px);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 999;\r\n}\r\n\r\n.success-modal {\r\n  width: 600rpx;\r\n  padding: 50rpx 40rpx;\r\n  background: #fff;\r\n  border-radius: 32rpx;\r\n  text-align: center;\r\n  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);\r\n  \r\n  &.warning-modal {\r\n    .success-title {\r\n      background: linear-gradient(135deg, #FF9F1C, #F76E11);\r\n      -webkit-background-clip: text;\r\n      -webkit-text-fill-color: transparent;\r\n    }\r\n    \r\n    .modal-btn {\r\n      background: linear-gradient(135deg, #FF9F1C, #F76E11);\r\n    }\r\n  }\r\n  \r\n  &.failed-modal {\r\n    .success-title {\r\n      background: linear-gradient(135deg, #FF9F1C, #F76E11);\r\n      -webkit-background-clip: text;\r\n      -webkit-text-fill-color: transparent;\r\n    }\r\n    \r\n    .modal-btn {\r\n      background: linear-gradient(135deg, #FF9F1C, #F76E11);\r\n    }\r\n  }\r\n  \r\n  .success-image {\r\n    width: 280rpx;\r\n    height: 280rpx;\r\n    margin: 0 auto 40rpx;\r\n    \r\n    image {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  \r\n  .success-text {\r\n    margin-bottom: 50rpx;\r\n    \r\n    .success-title {\r\n      font-size: 44rpx;\r\n      font-weight: 600;\r\n      background: linear-gradient(135deg, #FFC72C, #FFB300);\r\n      -webkit-background-clip: text;\r\n      -webkit-text-fill-color: transparent;\r\n      margin-bottom: 20rpx;\r\n      display: block;\r\n    }\r\n    \r\n    .success-desc {\r\n      font-size: 30rpx;\r\n      color: #666;\r\n      line-height: 1.6;\r\n    }\r\n  }\r\n  \r\n  .modal-btn {\r\n    width: 100%;\r\n    height: 96rpx;\r\n    background: linear-gradient(135deg, #FFC72C, #FFB300);\r\n    border-radius: 48rpx;\r\n    color: #fff;\r\n    font-size: 32rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    border: none;\r\n    box-shadow: 0 8rpx 24rpx rgba(255, 199, 44, 0.2);\r\n    transition: all 0.3s ease;\r\n\r\n    &:active {\r\n      transform: scale(0.98);\r\n      background: linear-gradient(135deg, #FFC72C, #DA291C);\r\n    }\r\n  }\r\n}\r\n\r\n.resources {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16rpx;\r\n}\r\n\r\n.resource-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 20rpx;\r\n  background: #FFF8F0;\r\n  border-radius: 16rpx;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.resource-item:active {\r\n  transform: scale(0.98);\r\n  background: #FFF2E6;\r\n}\r\n\r\n.resource-icon {\r\n  width: 36rpx;\r\n  height: 36rpx;\r\n  margin-right: 16rpx;\r\n}\r\n\r\n.resource-text {\r\n  color: #FFA500;\r\n  font-size: 26rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 动画效果 */\r\n@keyframes wave1 {\r\n  0%, 100% { transform: rotate(0deg) scale(1); }\r\n  50% { transform: rotate(180deg) scale(1.1); }\r\n}\r\n\r\n@keyframes wave2 {\r\n  0%, 100% { transform: rotate(0deg) scale(1); }\r\n  50% { transform: rotate(-180deg) scale(1.2); }\r\n}\r\n\r\n/* 增强的任务区块样式 */\r\n.task-section.enhanced {\r\n  margin: 0 40rpx 32rpx;\r\n  border-radius: 20rpx;\r\n  overflow: hidden;\r\n  border: 1rpx solid rgba(255, 199, 44, 0.1);\r\n\r\n  .section-header {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 20rpx 24rpx;\r\n    background: linear-gradient(135deg, rgba(255, 199, 44, 0.05) 0%, rgba(255, 179, 0, 0.05) 100%);\r\n\r\n    .section-icon-wrapper {\r\n      width: 40rpx;\r\n      height: 40rpx;\r\n      background: linear-gradient(135deg, #FFC72C, #FFB300);\r\n      border-radius: 20rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      margin-right: 16rpx;\r\n\r\n      &.tips {\r\n        background: linear-gradient(135deg, #4CAF50, #66BB6A);\r\n      }\r\n\r\n      &.resources {\r\n        background: linear-gradient(135deg, #2196F3, #42A5F5);\r\n      }\r\n\r\n      .section-icon {\r\n        width: 24rpx;\r\n        height: 24rpx;\r\n      }\r\n    }\r\n\r\n    .section-title {\r\n      font-size: 28rpx;\r\n      font-weight: 600;\r\n      color: #333;\r\n      flex: 1;\r\n    }\r\n\r\n    .section-badge {\r\n      padding: 4rpx 12rpx;\r\n      background: #FFC72C;\r\n      color: #fff;\r\n      border-radius: 12rpx;\r\n      font-size: 20rpx;\r\n      font-weight: 500;\r\n\r\n      &.tips {\r\n        background: transparent;\r\n        color: #4CAF50;\r\n        font-size: 24rpx;\r\n      }\r\n\r\n      &.resources {\r\n        background: #2196F3;\r\n      }\r\n    }\r\n  }\r\n\r\n  .section-content {\r\n    padding: 24rpx;\r\n    background: #fff;\r\n\r\n    &.tips {\r\n      background: linear-gradient(135deg, rgba(76, 175, 80, 0.02) 0%, rgba(102, 187, 106, 0.02) 100%);\r\n      border-left: 4rpx solid #4CAF50;\r\n    }\r\n\r\n    &.resources {\r\n      padding: 0;\r\n\r\n      .resource-item.enhanced {\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 20rpx 24rpx;\r\n        border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);\r\n        transition: background-color 0.3s ease;\r\n\r\n        &:last-child {\r\n          border-bottom: none;\r\n        }\r\n\r\n        &:active {\r\n          background-color: rgba(33, 150, 243, 0.05);\r\n        }\r\n\r\n        .resource-icon-wrapper {\r\n          width: 40rpx;\r\n          height: 40rpx;\r\n          background: linear-gradient(135deg, #2196F3, #42A5F5);\r\n          border-radius: 20rpx;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          margin-right: 16rpx;\r\n\r\n          .resource-icon {\r\n            width: 20rpx;\r\n            height: 20rpx;\r\n          }\r\n        }\r\n\r\n        .resource-content {\r\n          flex: 1;\r\n\r\n          .resource-title {\r\n            font-size: 26rpx;\r\n            font-weight: 500;\r\n            color: #333;\r\n            margin-bottom: 4rpx;\r\n            display: block;\r\n          }\r\n\r\n          .resource-desc {\r\n            font-size: 22rpx;\r\n            color: #999;\r\n            display: block;\r\n          }\r\n        }\r\n\r\n        .resource-arrow {\r\n          .arrow-icon {\r\n            width: 24rpx;\r\n            height: 24rpx;\r\n            opacity: 0.6;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 任务状态和进度样式 */\r\n.task-status-section {\r\n  padding: 0 40rpx 40rpx;\r\n\r\n  .task-completion-status.completed {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 24rpx;\r\n    background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(139, 195, 74, 0.1));\r\n    border-radius: 20rpx;\r\n    border: 2rpx solid rgba(76, 175, 80, 0.2);\r\n\r\n    .completion-icon {\r\n      width: 48rpx;\r\n      height: 48rpx;\r\n      margin-right: 16rpx;\r\n\r\n      .status-icon {\r\n        width: 100%;\r\n        height: 100%;\r\n      }\r\n    }\r\n\r\n    .completion-content {\r\n      flex: 1;\r\n\r\n      .completion-title {\r\n        font-size: 28rpx;\r\n        font-weight: 600;\r\n        color: #4CAF50;\r\n        margin-bottom: 4rpx;\r\n        display: block;\r\n      }\r\n\r\n      .completion-desc {\r\n        font-size: 24rpx;\r\n        color: #66BB6A;\r\n        display: block;\r\n      }\r\n    }\r\n  }\r\n\r\n  .task-motivation {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 24rpx;\r\n    background: linear-gradient(135deg, rgba(255, 199, 44, 0.1), rgba(255, 179, 0, 0.1));\r\n    border-radius: 20rpx;\r\n    border: 2rpx solid rgba(255, 199, 44, 0.2);\r\n\r\n    .motivation-icon {\r\n      width: 48rpx;\r\n      height: 48rpx;\r\n      margin-right: 16rpx;\r\n\r\n      .motivation-img {\r\n        width: 100%;\r\n        height: 100%;\r\n      }\r\n    }\r\n\r\n    .motivation-content {\r\n      flex: 1;\r\n\r\n      .motivation-title {\r\n        font-size: 28rpx;\r\n        font-weight: 600;\r\n        color: #FF8F00;\r\n        margin-bottom: 4rpx;\r\n        display: block;\r\n      }\r\n\r\n      .motivation-desc {\r\n        font-size: 24rpx;\r\n        color: #FFB300;\r\n        display: block;\r\n        line-height: 1.4;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.task-progress-section {\r\n  padding: 32rpx 40rpx 40rpx;\r\n  background: linear-gradient(135deg, rgba(255, 199, 44, 0.02) 0%, rgba(255, 179, 0, 0.02) 100%);\r\n\r\n  .progress-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 16rpx;\r\n\r\n    .progress-title {\r\n      font-size: 26rpx;\r\n      font-weight: 500;\r\n      color: #333;\r\n    }\r\n\r\n    .progress-value {\r\n      font-size: 24rpx;\r\n      font-weight: 600;\r\n      color: #FFC72C;\r\n    }\r\n  }\r\n\r\n  .progress-track {\r\n    position: relative;\r\n    height: 12rpx;\r\n    background: rgba(255, 199, 44, 0.1);\r\n    border-radius: 6rpx;\r\n    overflow: hidden;\r\n    margin-bottom: 24rpx;\r\n\r\n    .progress-fill {\r\n      height: 100%;\r\n      background: linear-gradient(90deg, #FFC72C 0%, #FFB300 100%);\r\n      border-radius: 6rpx;\r\n      transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);\r\n    }\r\n\r\n    .progress-marker {\r\n      position: absolute;\r\n      top: -4rpx;\r\n      transform: translateX(-50%);\r\n      transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);\r\n\r\n      .marker-dot {\r\n        width: 20rpx;\r\n        height: 20rpx;\r\n        background: #FFC72C;\r\n        border-radius: 10rpx;\r\n        border: 3rpx solid #fff;\r\n        box-shadow: 0 2rpx 8rpx rgba(255, 199, 44, 0.3);\r\n      }\r\n    }\r\n  }\r\n\r\n  .progress-milestones {\r\n    position: relative;\r\n    height: 40rpx;\r\n\r\n    .milestone {\r\n      position: absolute;\r\n      transform: translateX(-50%);\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n\r\n      .milestone-dot {\r\n        width: 12rpx;\r\n        height: 12rpx;\r\n        background: rgba(255, 199, 44, 0.3);\r\n        border-radius: 6rpx;\r\n        margin-bottom: 8rpx;\r\n        transition: background-color 0.3s ease;\r\n      }\r\n\r\n      .milestone-text {\r\n        font-size: 20rpx;\r\n        color: #999;\r\n        font-weight: 500;\r\n      }\r\n\r\n      &.completed {\r\n        .milestone-dot {\r\n          background: #FFC72C;\r\n        }\r\n\r\n        .milestone-text {\r\n          color: #FFC72C;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 动画效果 */\r\n@keyframes wave1 {\r\n  0%, 100% { transform: rotate(0deg) scale(1); }\r\n  50% { transform: rotate(180deg) scale(1.1); }\r\n}\r\n\r\n@keyframes wave2 {\r\n  0%, 100% { transform: rotate(0deg) scale(1); }\r\n  50% { transform: rotate(-180deg) scale(1.1); }\r\n}\r\n\r\n@keyframes shine {\r\n  0% { left: -100%; }\r\n  100% { left: 100%; }\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    transform: translate(-50%, -50%) scale(1);\r\n    opacity: 1;\r\n  }\r\n  50% {\r\n    transform: translate(-50%, -50%) scale(1.5);\r\n    opacity: 0.5;\r\n  }\r\n  100% {\r\n    transform: translate(-50%, -50%) scale(2);\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n@keyframes fadeInUp {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(30rpx);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n/* 为卡片添加进入动画 */\r\n.today-card,\r\n.daily-task-card,\r\n.challenge-card,\r\n.stats-section,\r\n.calendar-section,\r\n.time-section {\r\n  animation: fadeInUp 0.6s ease-out;\r\n  animation-fill-mode: both;\r\n}\r\n\r\n.today-card {\r\n  animation-delay: 0.1s;\r\n}\r\n\r\n.daily-task-card {\r\n  animation-delay: 0.2s;\r\n}\r\n\r\n.challenge-card {\r\n  animation-delay: 0.3s;\r\n}\r\n\r\n.stats-section {\r\n  animation-delay: 0.4s;\r\n}\r\n\r\n.calendar-section {\r\n  animation-delay: 0.5s;\r\n}\r\n\r\n.time-section {\r\n  animation-delay: 0.6s;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'E:/work/study/smzs/smzs-ui-app/pages/home/<USER>'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "getTodayTaskStatus", "getChallengeStats", "getChallengeTask", "checkInTask"], "mappings": ";;;;AAkYA,MAAK,eAAgB,MAAW;AAGhC,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EAED,OAAO;AACL,WAAO;AAAA,MACL,iBAAiB;AAAA,MACjB,WAAW,CAAE;AAAA,MACb,OAAO;AAAA,QACL,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,QACb,mBAAmB;AAAA,QACnB,oBAAoB;AAAA,MACrB;AAAA,MACD,eAAe,CAAE;AAAA,MACjB,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,aAAa;AAAA,MACb,cAAc;AAAA,MACd,cAAc,CAAE;AAAA,MAChB,WAAW;AAAA,MACX,cAAc;AAAA,MACd,aAAa;AAAA,MACb,WAAW;AAAA,MACX,kBAAkB;AAAA;AAAA,MAElB,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,eAAe;AAAA,MACf,cAAc;AAAA,MACd,SAAS;AAAA,IACX;AAAA,EACD;AAAA,EAED,UAAU;AAAA;AAAA,IAER,cAAc;AACZ,YAAM,UAAU,KAAK,IAAI,KAAK,UAAU,KAAK,IAAI;AACjD,aAAO;AAAA,QACL,YAAY;AAAA,+BACW,MAAM,UAAU,GAAG;AAAA,+BACnB,MAAM,UAAU,GAAG;AAAA,+BACnB,MAAM,UAAU,GAAG;AAAA,QAC1C,gBAAgB,QAAQ,KAAK,KAAK,UAAU,GAAG;AAAA,MACjD;AAAA,IACD;AAAA,IAED,qBAAqB;AACnB,UAAI,KAAK;AAAW,eAAO;AAC3B,UAAI,KAAK,UAAU,WAAW;AAAK,eAAO;AAC1C,UAAI,CAAC,KAAK,eAAe,CAAC,KAAK,YAAY;AAAQ,eAAO;AAC1D,UAAI,KAAK,YAAY,UAAU,KAAK,YAAY,OAAO,kBAAkB,KAAK;AAC5E,eAAO,KAAK,YAAY,OAAO,cAAc,YAAY;AAAA,MAC3D,WAAW,KAAK,YAAY,UAAU,KAAK,YAAY,OAAO,kBAAkB,KAAK;AACnF,eAAO;AAAA,aACF;AACL,eAAO;AAAA,MACT;AAAA,IACD;AAAA,IAED,sBAAsB;AACpB,UAAI,KAAK;AAAW,eAAO;AAC3B,UAAI,KAAK,UAAU,WAAW;AAAK,eAAO;AAC1C,UAAI,CAAC,KAAK;AAAa,eAAO;AAC9B,UAAI,KAAK,YAAY,UAAU,KAAK,YAAY,OAAO,kBAAkB,KAAK;AAC5E,eAAO,KAAK,YAAY,OAAO,cAAc,mBAAmB;AAAA,MAClE,WAAW,KAAK,YAAY,UAAU,KAAK,YAAY,OAAO,kBAAkB,KAAK;AACnF,eAAO;AAAA,aACF;AACL,eAAO;AAAA,MACT;AAAA,IACD;AAAA,IAED,gBAAgB;AACd,UAAI,CAAC,KAAK,UAAU;AAAU,eAAO;AACrC,aAAO,IAAI,KAAK,MAAM,WAAW,KAAK,UAAU,WAAW,KAAK,QAAQ,CAAC,CAAC;AAAA,IAC3E;AAAA,IAED,aAAa;AACX,UAAI,CAAC,KAAK,UAAU,aAAa,CAAC,KAAK,UAAU;AAAS,eAAO,CAAC;AAElE,YAAM,YAAY,IAAI,KAAK,KAAK,UAAU,SAAS;AACnD,YAAM,UAAU,IAAI,KAAK,KAAK,UAAU,OAAO;AAC/C,YAAM,OAAO,CAAC;AAGd,YAAM,iBAAiB,UAAU,OAAO;AAGxC,eAAS,IAAI,GAAG,IAAI,gBAAgB,KAAK;AACvC,aAAK,KAAK;AAAA,UACR,MAAM;AAAA,UACN,OAAO;AAAA,UACP,WAAW;AAAA,UACX,SAAS;AAAA,UACT,SAAS;AAAA,UACT,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,eAAe;AAAA,SAChB;AAAA,MACH;AAEA,UAAI,cAAc,IAAI,KAAK,SAAS;AACpC,YAAM,QAAQ,oBAAI,KAAK;AACvB,YAAM,SAAS,GAAG,GAAG,GAAG,CAAC;AAEzB,aAAO,eAAe,SAAS;AAC7B,cAAM,eAAe,YAAY,QAAO,MAAO,KAC3B,YAAY,QAAO,MAAO,UAAU,QAAQ;AAEhE,cAAM,WAAW,cAAc,SACf,CAAC,KAAK,cAAc,WAAW,KAC/B,KAAK,UAAU,WAAW;AAE1C,aAAK,KAAK;AAAA,UACR,MAAM,YAAY,QAAS;AAAA,UAC3B,OAAO,YAAY,SAAQ,IAAK;AAAA,UAChC,WAAW;AAAA,UACX,SAAS,KAAK,cAAc,WAAW;AAAA,UACvC,SAAS,YAAY,mBAAmB,MAAM,aAAc;AAAA,UAC5D,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,UAAU,IAAI,KAAK,WAAW;AAAA,UAC9B,WAAW,YAAY;;SACxB;AAED,oBAAY,QAAQ,YAAY,QAAO,IAAK,CAAC;AAAA,MAC/C;AAEA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,oBAAoB;AAClB,YAAM,YAAY,KAAK,UAAU,gBAAgB;AACjD,YAAM,WAAY,KAAK,mBAAmB,YAAa;AACvD,aAAO,GAAG,KAAK,IAAI,UAAU,GAAG,CAAC;AAAA,IACnC;AAAA,EACD;AAAA,EAED,oBAAoB;AAClB,SAAK,WAAW,KAAK,MAAM;AACzBA,oBAAG,MAAC,oBAAmB;AAAA,KACxB,EAAE,MAAM,MAAM;AACbA,oBAAG,MAAC,oBAAmB;AAAA,IACzB,CAAC;AAAA,EACF;AAAA,EAED,OAAO,SAAS;AAEd,UAAM,aAAaA,cAAG,MAAC,kBAAkB;AACzC,SAAK,kBAAkB,WAAW;AAElC,QAAI,QAAQ,cAAc;AACxB,WAAK,YAAY,KAAK,MAAM,mBAAmB,QAAQ,YAAY,CAAC;AACpE,WAAK,SAAS;AACd,WAAK,aAAa;AAAA,IACpB;AAAA,EACD;AAAA,EAED,SAAS;AAAA,IACP,SAAS;AACPA,oBAAAA,MAAI,aAAa;AAAA,IAClB;AAAA;AAAA,IAGD,aAAa,GAAG;AACd,YAAM,YAAY,EAAE,OAAO;AAC3B,WAAK,UAAU;AAGf,WAAK,aAAa,YAAY;AAC9B,WAAK,YAAY,YAAY;AAE7B,WAAK,gBAAgB;AAAA,IACtB;AAAA;AAAA,IAGD,iBAAiB,OAAO;AACtB,YAAM,YAAY;AAAA,QAChB,EAAE,MAAM,OAAO,KAAK,OAAO,gBAAgB,KAAM;AAAA,QACjD,EAAE,MAAM,OAAO,KAAK,OAAO,gBAAgB,KAAM;AAAA,QACjD,EAAE,MAAM,OAAO,KAAK,OAAO,gBAAgB,KAAM;AAAA,QACjD,EAAE,MAAM,OAAO,KAAK,OAAO,gBAAgB,OAAQ;AAAA,QACnD,EAAE,MAAM,OAAO,KAAK,OAAO,gBAAgB,OAAQ;AAAA,QACnD,EAAE,MAAM,OAAO,KAAK,OAAO,gBAAgB,OAAO;AAAA,MACpD;AACA,aAAO,UAAU,QAAQ,CAAC,KAAK,CAAC;AAAA,IACjC;AAAA;AAAA,IAGD,iBAAiB;AACf,YAAM,cAAc,KAAK;AACzB,YAAM,WAAW;AAAA,QACf,kBAAkB;AAAA,QAClB,kBAAkB;AAAA,QAClB,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,QAClB,gBAAgB;AAAA,QAChB,kBAAkB;AAAA,MACpB;AACA,aAAO,SAAS,WAAW,KAAK;AAAA,IACjC;AAAA;AAAA,IAGD,oBAAoB;AAClB,YAAM,cAAc,KAAK;AACzB,YAAM,cAAc;AAAA,QAClB,kBAAkB;AAAA,QAClB,kBAAkB;AAAA,QAClB,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,QAClB,gBAAgB;AAAA,QAChB,kBAAkB;AAAA,MACpB;AACA,aAAO,YAAY,WAAW,KAAK;AAAA,IACpC;AAAA;AAAA,IAGD,qBAAqB,QAAQ;AAC3B,YAAM,WAAW;AAAA,QACf,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AACA,aAAO,SAAS,MAAM,KAAK;AAAA,IAC5B;AAAA;AAAA,IAGD,oBAAoB,QAAQ;AAC1B,YAAM,UAAU;AAAA,QACd,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AACA,aAAO,QAAQ,MAAM,KAAK;AAAA,IAC3B;AAAA,IAED,MAAM,WAAW;;AACf,UAAI;AACF,aAAK,YAAY;AACjBA,sBAAAA,MAAI,YAAY,EAAE,OAAO,UAAU;AAGnC,cAAM,YAAY,MAAMC,UAAAA,mBAAmB,KAAK,UAAU,eAAe;AACzE,YAAI,UAAU,SAAS,KAAK;AAC1B,eAAK,cAAc,UAAU;AAC7B,eAAK,oBAAmB,UAAK,gBAAL,mBAAkB;AAAA,QAC5C;AAGA,cAAM,WAAW,MAAMC,UAAAA,kBAAkB,KAAK,UAAU,eAAe;AACvE,YAAI,SAAS,SAAS,KAAK;AACzB,eAAK,QAAQ,SAAS;AACtB,eAAK,0BAA0B,KAAK,MAAM,YAAY;AAAA,QACxD;AAGA,YAAI,KAAK,UAAU,eAAe;AAEhC,gBAAM,QAAQ,oBAAI,KAAK;AACvB,gBAAM,YAAY,IAAI,KAAK,KAAK,UAAU,SAAS;AACnD,gBAAM,UAAU,KAAK,OAAO,QAAQ,cAAc,MAAO,KAAK,KAAK,GAAG,IAAI;AAC1E,eAAK,mBAAmB,KAAK,IAAI,KAAK,IAAI,GAAG,OAAO,GAAG,EAAE;AAEzD,cAAI;AACF,kBAAM,UAAU,MAAMC,2BAAiB,KAAK,UAAU,iBAAiB,KAAK,gBAAgB;AAC5F,gBAAI,QAAQ,SAAS,KAAK;AACxB,mBAAK,YAAY,QAAQ;AAAA,YAC3B;AAAA,UACA,SAAO,OAAO;AACdH,0BAAAA,MAAA,MAAA,SAAA,yCAAc,aAAa,KAAK;AAAA,UAClC;AAAA,QACF;AAEAA,sBAAAA,MAAI,YAAY;AAChB,aAAK,YAAY;AAAA,MACjB,SAAO,OAAO;AACdA,sBAAAA,MAAI,YAAY;AAChB,aAAK,YAAY;AACjBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA,IAED,cAAc;AACZ,YAAM,QAAQ,oBAAI,KAAK;AACvB,aAAO,GAAG,MAAM,aAAa,CAAC,IAAI,MAAM,QAAS,CAAA;AAAA,IAClD;AAAA,IAED,gBAAgB,OAAO,KAAK;AAC1B,UAAI,CAAC,SAAS,CAAC;AAAK,eAAO;AAC3B,YAAM,YAAY,IAAI,KAAK,KAAK;AAChC,YAAM,UAAU,IAAI,KAAK,GAAG;AAC5B,aAAO,GAAG,UAAU,SAAQ,IAAK,CAAC,IAAI,UAAU,QAAS,CAAA,OAAO,QAAQ,SAAQ,IAAK,CAAC,IAAI,QAAQ,QAAS,CAAA;AAAA,IAC5G;AAAA,IAED,eAAe,MAAM;AACnB,UAAI,CAAC;AAAM,eAAO;AAClB,YAAM,OAAO,IAAI,KAAK,IAAI;AAC1B,aAAO,GAAG,KAAK,SAAQ,EAAG,WAAW,SAAS,GAAG,GAAG,CAAC,IAAI,KAAK,WAAU,EAAG,SAAQ,EAAG,SAAS,GAAG,GAAG,CAAC;AAAA,IACvG;AAAA,IAED,WAAW,SAAS;AAChB,UAAI,CAAC;AAAS,eAAO;AACrB,YAAM,UAAU,QAAQ,MAAM,GAAG,EAAE,CAAC,KAAK;AACzC,YAAM,YAAY,QAAQ,MAAM,GAAG;AACnC,UAAI,UAAU,UAAU,GAAG;AACvB,eAAO,GAAG,UAAU,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC;AAAA,MAC1C;AACA,aAAO;AAAA,IACV;AAAA,IAED,eAAe;AACb,YAAM,YAAY,IAAI,KAAK,KAAK,UAAU,SAAS;AACnD,YAAM,UAAU,IAAI,KAAK,KAAK,UAAU,OAAO;AAC/C,YAAM,MAAM,oBAAI,KAAK;AAGrB,UAAI,cAAc;AAClB,UAAI,MAAM,aAAa,MAAM,SAAS;AACpC,sBAAc;AAAA,MAChB;AAEA,WAAK,eAAe,GAAG,YAAY,YAAW,CAAE,IAAI,YAAY,aAAa,CAAC;AAC9E,WAAK,qBAAqB,WAAW;AAAA,IACtC;AAAA,IAED,qBAAqB,MAAM;AACzB,YAAM,OAAO,KAAK,YAAY;AAC9B,YAAM,QAAQ,KAAK,SAAS;AAC5B,YAAM,WAAW,IAAI,KAAK,MAAM,OAAO,CAAC;AACxC,YAAM,UAAU,IAAI,KAAK,MAAM,QAAQ,GAAG,CAAC;AAE3C,YAAM,YAAY,IAAI,KAAK,KAAK,UAAU,SAAS;AACnD,YAAM,UAAU,IAAI,KAAK,KAAK,UAAU,OAAO;AAE/C,YAAM,OAAO,CAAC;AACd,YAAM,cAAc,SAAS,OAAO;AAGpC,eAAS,IAAI,cAAc,GAAG,KAAK,GAAG,KAAK;AACzC,cAAM,WAAW,IAAI,KAAK,MAAM,OAAO,CAAC,CAAC;AACzC,aAAK,KAAK;AAAA,UACR,MAAM,SAAS,QAAS;AAAA,UACxB,cAAc;AAAA,UACd,SAAS;AAAA,UACT,SAAS;AAAA,UACT,UAAU;AAAA,UACV,SAAS,YAAY,aAAa,YAAY;AAAA,SAC/C;AAAA,MACH;AAGA,YAAM,QAAQ,oBAAI,KAAK;AACvB,eAAS,IAAI,GAAG,KAAK,QAAQ,QAAO,GAAI,KAAK;AAC3C,cAAM,cAAc,IAAI,KAAK,MAAM,OAAO,CAAC;AAC3C,aAAK,KAAK;AAAA,UACR,MAAM;AAAA,UACN,cAAc;AAAA,UACd,SAAS;AAAA,UACT,SAAS,YAAY,mBAAmB,MAAM,aAAc;AAAA,UAC5D,UAAU;AAAA,UACV,SAAS,eAAe,aAAa,eAAe;AAAA,SACrD;AAAA,MACH;AAGA,YAAM,gBAAgB,KAAK,KAAK;AAChC,eAAS,IAAI,GAAG,KAAK,eAAe,KAAK;AACvC,cAAM,WAAW,IAAI,KAAK,MAAM,QAAQ,GAAG,CAAC;AAC5C,aAAK,KAAK;AAAA,UACR,MAAM;AAAA,UACN,cAAc;AAAA,UACd,SAAS;AAAA,UACT,SAAS;AAAA,UACT,UAAU;AAAA,UACV,SAAS,YAAY,aAAa,YAAY;AAAA,SAC/C;AAAA,MACH;AAEA,WAAK,eAAe;AAAA,IACrB;AAAA,IAED,0BAA0B,cAAc;AACtC,UAAI,CAAC,gBAAgB,CAAC,aAAa;AAAQ;AAE3C,UAAI,KAAK,MAAM,gBAAgB,KAAK,MAAM,aAAa,SAAS,GAAG;AACjE,aAAK,MAAM,aAAa,QAAQ,WAAS;AACvC,gBAAM,cAAc,IAAI,KAAK,MAAM,WAAW;AAC9C,gBAAM,WAAW,KAAK,aAAa;AAAA,YAAU,SAC3C,IAAI,SAAS,mBAAmB,YAAY,aAAa;AAAA,UAC3D;AACA,cAAI,aAAa,IAAI;AACnB,iBAAK,aAAa,QAAQ,EAAE,UAAU;AACtC,iBAAK,aAAa,QAAQ,EAAE,cAAc,CAAC,MAAM;AAAA,UACnD;AAAA,SACD;AAAA,aACI;AAEL,qBAAa,QAAQ,aAAW;AAC9B,gBAAM,cAAc,IAAI,KAAK,OAAO;AACpC,gBAAM,WAAW,KAAK,aAAa;AAAA,YAAU,SAC3C,IAAI,SAAS,mBAAmB,YAAY,aAAa;AAAA,UAC3D;AACA,cAAI,aAAa,IAAI;AACnB,iBAAK,aAAa,QAAQ,EAAE,UAAU;AAAA,UACxC;AAAA,SACD;AAAA,MACH;AAAA,IACD;AAAA,IAED,YAAY;AACV,YAAM,CAAC,MAAM,KAAK,IAAI,KAAK,aAAa,QAAQ,SAAS,GAAG,EAAE,MAAM,GAAG;AACvE,YAAM,WAAW,IAAI,KAAK,SAAS,IAAI,GAAG,SAAS,KAAK,IAAI,CAAC;AAG7D,YAAM,YAAY,IAAI,KAAK,KAAK,UAAU,SAAS;AACnD,UAAI,WAAW,IAAI,KAAK,UAAU,YAAa,GAAE,UAAU,YAAY,CAAC,GAAG;AACzE;AAAA,MACF;AAEA,WAAK,eAAe,GAAG,SAAS,YAAW,CAAE,IAAI,SAAS,aAAa,CAAC;AACxE,WAAK,qBAAqB,QAAQ;AAClC,WAAK,0BAA0B,KAAK,MAAM,YAAY;AAAA,IACvD;AAAA,IAED,YAAY;AACV,YAAM,CAAC,MAAM,KAAK,IAAI,KAAK,aAAa,QAAQ,SAAS,GAAG,EAAE,MAAM,GAAG;AACvE,YAAM,WAAW,IAAI,KAAK,SAAS,IAAI,GAAG,SAAS,KAAK,CAAC;AAGzD,YAAM,UAAU,IAAI,KAAK,KAAK,UAAU,OAAO;AAC/C,UAAI,WAAW,IAAI,KAAK,QAAQ,YAAa,GAAE,QAAQ,YAAY,CAAC,GAAG;AACrE;AAAA,MACF;AAEA,WAAK,eAAe,GAAG,SAAS,YAAW,CAAE,IAAI,SAAS,aAAa,CAAC;AACxE,WAAK,qBAAqB,QAAQ;AAClC,WAAK,0BAA0B,KAAK,MAAM,YAAY;AAAA,IACvD;AAAA,IAED,MAAM,gBAAgB;AACpB,UAAI,KAAK,UAAU,WAAW,KAAK;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AACA,UAAI,KAAK,kBAAkB;AACzBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEA,UAAI;AACFA,sBAAAA,MAAI,YAAY,EAAE,OAAO,UAAU;AACnC,cAAM,MAAM,MAAMI,sBAAY;AAAA,UAC5B,iBAAiB,KAAK,UAAU;AAAA,UAChC,QAAQ;AAAA,SACT;AAEDJ,sBAAAA,MAAI,YAAY;AAChB,YAAI,IAAI,SAAS,KAAK;AACpB,eAAK,mBAAmB;AACxB,eAAK,cAAc,IAAI,KAAK,eAAe;AAC3C,gBAAM,KAAK,SAAS;AACpB,eAAK,eAAe;AAAA,eACf;AACL,gBAAM,IAAI,MAAM,IAAI,GAAG;AAAA,QACzB;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAY,MAAA,OAAA,yCAAA,SAAS,KAAK;AAC1BA,sBAAAA,MAAI,YAAY;AAGhB,YAAI,WAAW;AACf,YAAI,MAAM,SAAS;AACjB,qBAAW,MAAM;AAAA,QACnB,WAAW,OAAO,UAAU,UAAU;AACpC,qBAAW;AAAA,QACb,WAAW,MAAM,KAAK;AACpB,qBAAW,MAAM;AAAA,eACZ;AACL,qBAAW;AAAA,QACb;AAEAA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,SACX;AAAA,MACH;AAAA,IACD;AAAA,IAED,oBAAoB;AAClB,WAAK,eAAe;AAAA,IACrB;AAAA,IAED,cAAc,MAAM;AAClB,UAAI,CAAC,KAAK,MAAM;AAAc,eAAO;AACrC,aAAO,KAAK,MAAM,aAAa;AAAA,QAAK,iBAClC,IAAI,KAAK,WAAW,EAAE,aAAY,MAAO,KAAK,aAAa;AAAA,MAC7D;AAAA,IACD;AAAA,IAED,eAAe;AACb,UAAI,KAAK,eAAe,KAAK,YAAY,UAAU,KAAK,YAAY,OAAO,kBAAkB,KAAK;AAChG,eAAO;AAAA,MACP,WAAS,KAAK,eAAe,KAAK,YAAY,UAAU,KAAK,YAAY,OAAO,kBAAkB,KAAK;AACvG,eAAO,KAAK,cAAc,qGAAqG;AAAA,MACjI;AACA,aAAO;AAAA,IACR;AAAA,IAED,gBAAgB;AACd,UAAI,KAAK,eAAe,KAAK,YAAY,UAAU,KAAK,YAAY,OAAO,kBAAkB,KAAK;AAChG,eAAO;AAAA,MACP,WAAS,KAAK,eAAe,KAAK,YAAY,UAAU,KAAK,YAAY,OAAO,kBAAkB,KAAK;AACvG,eAAO,KAAK,cAAc,UAAU;AAAA,MACtC;AACA,aAAO;AAAA,IACR;AAAA,IAED,sBAAsB;;AACpB,YAAM,YAAY,GAAG,KAAK,aAAW,gBAAK,gBAAL,mBAAkB,mBAAlB,mBAAkC,cAAa,KAAK,UAAU,SAAS,CAAC,IAAI,KAAK,aAAW,gBAAK,gBAAL,mBAAkB,mBAAlB,mBAAkC,YAAW,KAAK,UAAU,OAAO,CAAC;AACrM,UAAI,KAAK,eAAe,KAAK,YAAY,UAAU,KAAK,YAAY,OAAO,kBAAkB,KAAK;AAChG,eAAO,MAAM,SAAS;AAAA,MACtB,WAAS,KAAK,eAAe,KAAK,YAAY,UAAU,KAAK,YAAY,OAAO,kBAAkB,KAAK;AACvG,eAAO,KAAK,cAAc,sBAAsB,MAAM,SAAS;AAAA,MACjE;AACA,aAAO,MAAM,SAAS;AAAA,IACvB;AAAA,IAED,mBAAmB,MAAM;AACvB,UAAI,CAAC;AAAM,eAAO;AAGlB,UAAI,WAAW;AACf,UAAI,OAAO,SAAS,UAAU;AAC5B,YAAI;AAEF,qBAAW,KAAK,MAAM,IAAI;AAAA,QAC5B,SAAS,GAAG;AAEV,qBAAW,KAAK,MAAM,GAAG;AAAA,QAC3B;AAAA,MACF;AAEA,UAAI,CAAC,MAAM,QAAQ,QAAQ,GAAG;AAC5B,mBAAW,CAAC,SAAS,SAAQ,CAAE;AAAA,MACjC;AAEA,aAAO;AAAA,IACR;AAAA,IAED,aAAa,KAAK;AAEhB,UAAI,eAAe;AACnB,UAAI,CAAC,IAAI,WAAW,SAAS,KAAK,CAAC,IAAI,WAAW,UAAU,GAAG;AAC7D,uBAAe,aAAa;AAAA,MAC9B;AAEAA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4BAA4B,mBAAmB,YAAY,CAAC;AAAA,MACnE,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB,MAAM;AACpB,UAAI,CAAC,QAAQ,CAAC,KAAK;AAAO,eAAO;AAEjC,YAAM,QAAQ,KAAK,MAAM,YAAY;AACrC,UAAI,MAAM,SAAS,IAAI,KAAK,MAAM,SAAS,YAAY;AAAG,eAAO;AACjE,UAAI,MAAM,SAAS,IAAI,KAAK,MAAM,SAAS,UAAU;AAAG,eAAO;AAC/D,UAAI,MAAM,SAAS,IAAI,KAAK,MAAM,SAAS,SAAS;AAAG,eAAO;AAC9D,UAAI,MAAM,SAAS,IAAI,KAAK,MAAM,SAAS,YAAY;AAAG,eAAO;AACjE,UAAI,MAAM,SAAS,IAAI,KAAK,MAAM,SAAS,SAAS;AAAG,eAAO;AAC9D,UAAI,MAAM,SAAS,IAAI,KAAK,MAAM,SAAS,QAAQ;AAAG,eAAO;AAE7D,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,kBAAkB,WAAW;AAC3B,UAAI,aAAa;AAAG,eAAO;AAC3B,UAAI,aAAa;AAAI,eAAO;AAC5B,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,kBAAkB,WAAW;AAC3B,YAAM,aAAa,KAAK,kBAAkB,SAAS;AACnD,YAAM,QAAQ,CAAC,IAAI,MAAM,MAAM,IAAI;AACnC,aAAO,MAAM,UAAU,KAAK;AAAA,IAC7B;AAAA;AAAA,IAGD,mBAAmB,WAAW;AAC5B,YAAM,SAAS;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,aAAO,OAAO,YAAY,CAAC,KAAK;AAAA,IACjC;AAAA;AAAA,IAGD,kBAAkB,WAAW;AAC3B,UAAI,aAAa,GAAG;AAClB,eAAO;AAAA,iBACE,aAAa,IAAI;AAC1B,eAAO;AAAA,aACF;AACL,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,wBAAwB;AACtB,YAAM,YAAY,KAAK,UAAU,gBAAgB;AACjD,aAAO;AAAA,QACL,EAAE,KAAK,GAAG,MAAM,MAAM,UAAU,KAAM;AAAA,QACtC,EAAE,KAAK,KAAK,MAAM,YAAY,CAAC,GAAG,MAAM,OAAO,UAAU,SAAU;AAAA,QACnE,EAAE,KAAK,KAAK,MAAM,YAAY,IAAI,CAAC,GAAG,MAAM,OAAO,UAAU,SAAU;AAAA,QACvE,EAAE,KAAK,WAAW,MAAM,MAAM,UAAU,OAAO;AAAA,MACjD;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACphCA,GAAG,WAAW,eAAe;"}