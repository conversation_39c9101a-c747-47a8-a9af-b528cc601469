// ==========================================================================
// 生成日期：2025-02-22 23:15:09 +0800 CST
// 生成路径: apps/smzs/services/smzs_challenge_participation.go
// 生成人：X
// ==========================================================================

package services

import (
	"pandax/apps/smzs/entity"
	"pandax/pkg/global"
)

type (
	ParticipationModel interface {
		Insert(data entity.Participation) (*entity.Participation, error)
		FindOne(participationId int64) (*entity.Participation, error)
		FindOneWithChallenge(participationId int64) (*ParticipationWithChallengeDetail, error)
		FindListPage(pageNum, pageSize int, data entity.Participation) (*[]ParticipationWithChallenge, int64, error)
		FindList(data entity.Participation) (*[]entity.Participation, error)
		Update(data entity.Participation) error
		Delete(participationIds []int64) error
		CheckParticipationExists(memberId int64, challengeId int64) (bool, error)
		FindFinishedListPage(pageNum, pageSize int, memberId int64) (*[]ParticipationWithChallenge, int64, error)
	}

	participationModelImpl struct {
		table string
	}

	// ParticipationWithChallenge 包含挑战信息的参与记录
	ParticipationWithChallenge struct {
		entity.Participation
		ChallengeImg  string `json:"challengeImg"`  // 挑战图片
		RuleDays      int    `json:"ruleDays"`      // 挑战天数
		KeepDays      int    `json:"keepDays"`      // 已坚持天数
		RemainDays    int    `json:"remainDays"`    // 剩余天数
		StartTime     string `json:"startTime"`     // 挑战开始时间
		EndTime       string `json:"endTime"`       // 挑战结束时间
		HasDailyTasks bool   `json:"hasDailyTasks"` // 是否有每日任务
	}

	// ParticipationWithChallengeDetail 包含详细挑战信息的参与记录
	ParticipationWithChallengeDetail struct {
		entity.Participation
		Challenge entity.Challenge `json:"challenge"` // 完整的挑战信息
	}
)

var ParticipationModelDao ParticipationModel = &participationModelImpl{
	table: `smzs_challenge_participation`,
}

func (m *participationModelImpl) Insert(data entity.Participation) (*entity.Participation, error) {
	err := global.Db.Table(m.table).Create(&data).Error
	return &data, err
}

func (m *participationModelImpl) FindOne(participationId int64) (*entity.Participation, error) {
	resData := new(entity.Participation)
	db := global.Db.Table(m.table).Where("participation_id = ?", participationId)
	err := db.First(resData).Error
	return resData, err
}

// FindListPage 获取分页数据
func (m *participationModelImpl) FindListPage(pageNum, pageSize int, data entity.Participation) (list *[]ParticipationWithChallenge, total int64, err error) {
	db := global.Db.Model(&entity.Participation{})

	// 添加查询条件
	if data.MemberId != 0 {
		db = db.Where("smzs_challenge_participation.member_id = ?", data.MemberId)
	}
	if data.Status != "" {
		db = db.Where("smzs_challenge_participation.status = ?", data.Status)
	}

	// 关联挑战表
	db = db.Select(`
		smzs_challenge_participation.*,
		smzs_challenge.image_urls as challenge_img,
		smzs_challenge.duration_days as rule_days,
		(
			SELECT COUNT(*) 
			FROM smzs_challenge_daily 
			WHERE participation_id = smzs_challenge_participation.participation_id 
			AND checkin_status = '1'
		) as keep_days,
		DATEDIFF(smzs_challenge_participation.end_date, CURDATE()) as remain_days,
		smzs_challenge.start_time as start_time,
		smzs_challenge.end_time as end_time,
		smzs_challenge.has_daily_tasks as has_daily_tasks
	`).
		Joins("LEFT JOIN smzs_challenge ON smzs_challenge_participation.challenge_id = smzs_challenge.challenge_id")

	// 计算总数
	err = db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err = db.Order("smzs_challenge_participation.create_time DESC").
		Offset((pageNum - 1) * pageSize).
		Limit(pageSize).
		Find(&list).Error

	return list, total, err
}

func (m *participationModelImpl) FindList(data entity.Participation) (*[]entity.Participation, error) {
	list := make([]entity.Participation, 0)
	db := global.Db.Table(m.table)
	// 此处填写 where参数判断
	if data.Status != "" {
		db = db.Where("status = ?", data.Status)
	}
	if data.Nickname != "" {
		db = db.Where("nickname like ?", "%"+data.Nickname+"%")
	}
	if data.Title != "" {
		db = db.Where("title = ?", data.Title)
	}
	if data.ParticipationId != 0 {
		db = db.Where("participation_id = ?", data.ParticipationId)
	}
	if data.MemberId != 0 {
		db = db.Where("member_id = ?", data.MemberId)
	}
	if data.ChallengeId != 0 {
		db = db.Where("challenge_id = ?", data.ChallengeId)
	}
	err := db.Find(&list).Error
	return &list, err
}

func (m *participationModelImpl) Update(data entity.Participation) error {
	return global.Db.Table(m.table).Updates(&data).Error
}

func (m *participationModelImpl) Delete(participationIds []int64) error {
	return global.Db.Table(m.table).Delete(&entity.Participation{}, "participation_id in (?)", participationIds).Error
}

func (m *participationModelImpl) CheckParticipationExists(memberId int64, challengeId int64) (bool, error) {
	var count int64
	err := global.Db.Model(&entity.Participation{}).
		Where("member_id = ? AND challenge_id = ? AND status = ?", memberId, challengeId, 0).
		Count(&count).Error
	return count > 0, err
}

// FindOneWithChallenge 获取单条参与记录并关联挑战详情
func (m *participationModelImpl) FindOneWithChallenge(participationId int64) (*ParticipationWithChallengeDetail, error) {
	resData := new(ParticipationWithChallengeDetail)

	// 先查询参与记录
	participation := new(entity.Participation)
	err := global.Db.Table(m.table).Where("participation_id = ?", participationId).First(participation).Error
	if err != nil {
		return nil, err
	}

	// 再查询关联的挑战详情
	challenge := new(entity.Challenge)
	err = global.Db.Table("smzs_challenge").Where("challenge_id = ?", participation.ChallengeId).First(challenge).Error
	if err != nil {
		return nil, err
	}

	// 组装结果
	resData.Participation = *participation
	resData.Challenge = *challenge

	return resData, nil
}

func (m *participationModelImpl) FindFinishedListPage(pageNum, pageSize int, memberId int64) (*[]ParticipationWithChallenge, int64, error) {
	db := global.Db.Model(&entity.Participation{})

	// 添加查询条件 已结束的挑战
	db = db.Where("smzs_challenge_participation.member_id = ? AND (smzs_challenge_participation.status = 1 OR smzs_challenge_participation.status = 2)", memberId)

	// 关联挑战表
	db = db.Select(`
		smzs_challenge_participation.*,
		smzs_challenge.image_urls as challenge_img,
		smzs_challenge.duration_days as rule_days,
		smzs_challenge.has_daily_tasks as has_daily_tasks,
		(
			SELECT COUNT(*) 
			FROM smzs_challenge_daily 
			WHERE participation_id = smzs_challenge_participation.participation_id 
			AND checkin_status = '1'
		) as keep_days,
		DATEDIFF(smzs_challenge_participation.end_date, CURDATE()) as remain_days,
		smzs_challenge.start_time as start_time,
		smzs_challenge.end_time as end_time
	`).
		Joins("LEFT JOIN smzs_challenge ON smzs_challenge_participation.challenge_id = smzs_challenge.challenge_id")

	// 计算总数
	var total int64
	err := db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	list := make([]ParticipationWithChallenge, 0)
	err = db.Order("smzs_challenge_participation.create_time DESC").
		Offset((pageNum - 1) * pageSize).
		Limit(pageSize).
		Find(&list).Error

	return &list, total, err
}
