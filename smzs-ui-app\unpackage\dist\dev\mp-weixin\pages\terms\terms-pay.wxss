
.container.data-v-4d32b948 {
  min-height: 100vh;
  background: #f8f8f8;
  position: relative;
}
.content-scroll.data-v-4d32b948 {
  height: 100vh;
  padding-bottom: 32rpx;
  box-sizing: border-box;
  padding-top: calc(var(--status-bar-height) + 120rpx);
}
.content.data-v-4d32b948 {
  padding: 32rpx;
}
.section.data-v-4d32b948 {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}
.section-title.data-v-4d32b948 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  position: relative;
  padding-left: 24rpx;
}
.section-title.data-v-4d32b948::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background: #ffb700;
  border-radius: 4rpx;
}
.section-content.data-v-4d32b948 {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.text.data-v-4d32b948 {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}
