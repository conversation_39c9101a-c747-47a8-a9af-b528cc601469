"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      activeIndex: 0,
      challenges: [],
      isRefreshing: false,
      tabsList: [
        { name: "全部" },
        { name: "生活" },
        { name: "学习" },
        { name: "运动" }
      ],
      pageNum: 1,
      pageSize: 20,
      isPro: false,
      showProDialog: false,
      selectedChallenge: null
    };
  },
  onShow() {
    this.loadChallengeList();
    this.checkProStatus();
  },
  methods: {
    handleSearch(keyword) {
      this.searchKeyword = keyword;
    },
    handleSearchConfirm(keyword) {
      this.searchKeyword = keyword;
      this.loadChallengeList();
    },
    handleFilter() {
      common_vendor.index.showToast({
        title: "筛选功能开发中",
        icon: "none"
      });
    },
    loadChallengeList() {
      api_index.getChallengeList({
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        keyword: this.searchKeyword
      }).then((res) => {
        if (res.code === 200) {
          this.challenges = res.data.data || [];
        } else {
          common_vendor.index.showToast({
            title: "获取列表失败",
            icon: "none"
          });
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/challenge/index.vue:172", "获取挑战列表失败:", err);
        common_vendor.index.showToast({
          title: "获取列表失败",
          icon: "none"
        });
      });
    },
    handleTabChange(index) {
      this.activeIndex = index;
    },
    handleSwiperChange(e) {
      this.activeIndex = e.detail.current;
    },
    handleChallengeItemClick(item) {
      if (item.requirePro && !this.isPro) {
        this.selectedChallenge = item;
        this.showProDialog = true;
        return;
      }
      if (item.hasDailyTasks) {
        common_vendor.index.navigateTo({
          url: `/pages/challenge/challenge-daily-detail?data=${encodeURIComponent(JSON.stringify(item))}`
        });
      } else {
        common_vendor.index.navigateTo({
          url: `/pages/challenge/challenge-detail?data=${encodeURIComponent(JSON.stringify(item))}`
        });
      }
    },
    formatDate(dateStr) {
      if (!dateStr)
        return "";
      const timeStr = dateStr.split(" ")[1] || dateStr;
      const timeParts = timeStr.split(":");
      if (timeParts.length >= 2) {
        return `${timeParts[0]}:${timeParts[1]}`;
      }
      return timeStr;
    },
    getTagClass(type) {
      const classMap = {
        "运动": "tag-sport",
        "学习": "tag-study",
        "生活": "tag-life"
      };
      return classMap[type] || "tag-default";
    },
    async onRefresh() {
      this.isRefreshing = true;
      await this.loadChallengeList();
      this.isRefreshing = false;
    },
    getFilteredChallenges(index) {
      if (index === 0) {
        return this.challenges;
      }
      return this.challenges.filter(
        (item) => item.challengeType === this.tabsList[index].name
      );
    },
    async checkProStatus() {
      try {
        const token = common_vendor.index.getStorageSync("token");
        if (!token)
          return;
        const res = await api_index.checkProStatus();
        if (res.code === 200) {
          this.isPro = res.data.isPro;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/challenge/index.vue:245", "获取会员状态失败:", error);
      }
    },
    closeProDialog() {
      this.showProDialog = false;
    },
    goToProPage() {
      this.showProDialog = false;
      common_vendor.index.navigateTo({
        url: "/pages/profile/pro-subscription"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($data.tabsList, (tab, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(tab.name),
        b: $data.activeIndex === index
      }, $data.activeIndex === index ? {} : {}, {
        c: index,
        d: common_vendor.n($data.activeIndex === index ? "active" : ""),
        e: common_vendor.o(($event) => $options.handleTabChange(index), index)
      });
    }),
    b: common_vendor.f($data.tabsList, (tab, index, i0) => {
      return {
        a: common_vendor.f($options.getFilteredChallenges(index), (item, k1, i1) => {
          return common_vendor.e({
            a: item.requirePro
          }, item.requirePro ? {} : {}, {
            b: item.imageUrls,
            c: common_vendor.t(item.title),
            d: common_vendor.t(item.challengeType),
            e: common_vendor.n($options.getTagClass(item.challengeType)),
            f: common_vendor.t($options.formatDate(item.startTime)),
            g: common_vendor.t($options.formatDate(item.endTime)),
            h: item.challenge,
            i: common_vendor.o(($event) => $options.handleChallengeItemClick(item), item.challenge)
          });
        }),
        b: common_vendor.o((...args) => $options.onRefresh && $options.onRefresh(...args), index),
        c: index
      };
    }),
    c: common_assets._imports_0$1,
    d: $data.isRefreshing,
    e: $data.activeIndex,
    f: common_vendor.o((...args) => $options.handleSwiperChange && $options.handleSwiperChange(...args)),
    g: $data.showProDialog
  }, $data.showProDialog ? {
    h: common_vendor.o((...args) => $options.closeProDialog && $options.closeProDialog(...args)),
    i: common_assets._imports_1$1,
    j: common_assets._imports_6,
    k: common_assets._imports_6,
    l: common_assets._imports_6,
    m: common_vendor.o((...args) => $options.closeProDialog && $options.closeProDialog(...args)),
    n: common_vendor.o((...args) => $options.goToProPage && $options.goToProPage(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/challenge/index.js.map
