# 依赖文件
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建输出
dist/
build/
out/
.next/
.nuxt/

# 环境配置文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 编辑器和IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
logs/
*.log

# 临时文件
tmp/
temp/
.tmp/

# 缓存文件
.cache/
.parcel-cache/

# 测试覆盖率报告
coverage/
.nyc_output/

# 运行时文件
*.pid
*.seed
*.pid.lock

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 备份文件
*.bak
*.backup

# 压缩文件
*.zip
*.tar.gz
*.rar

# 文档总结文件夹（包含敏感或临时总结信息）
smzs-docs/summaries/

# uni-app 特定文件
unpackage/
.hbuilderx/

# Go 特定文件
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
go.work

# 上传文件目录
uploads/
static/uploads/

# 证书文件
*.pem
*.key
*.crt
cert/

# 配置文件（可能包含敏感信息）
config.yml
config.yaml
config.json
